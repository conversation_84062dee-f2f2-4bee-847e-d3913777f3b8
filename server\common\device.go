package common

import (
	"Spark/modules"
	"Spark/server/model"
	"Spark/utils/melody"
	"sync"
	"time"
)

// Device 扩展modules.Device结构，添加额外信息
type Device struct {
	modules.Device
	UUID       string `json:"uuid"`
	Name       string `json:"name"`
	IP         string `json:"ip"`
	Status     string `json:"status"`
	LastOnline string `json:"last_online"`
	FirstSeen  string `json:"first_seen"`
	Remark     string `json:"remark"`
}

// DeviceStatus 表示设备状态
const (
	DeviceStatusOnline  = "ONLINE"  // 设备在线
	DeviceStatusOffline = "OFFLINE" // 设备离线
	DeviceStatusLost    = "LOST"    // 设备丢失（长时间未连接）
)

// DevicesMap 是一个线程安全的设备映射
type DevicesMap struct {
	Map  map[string]*Device
	Lock sync.RWMutex
}

// SetDevice 添加或更新设备
func (m *DevicesMap) Set(uuid string, device *Device) {
	m.Lock.Lock()
	defer m.Lock.Unlock()
	m.Map[uuid] = device
}

// GetDevice 通过UUID获取设备
func (m *DevicesMap) Get(uuid string) (*Device, bool) {
	m.Lock.RLock()
	defer m.Lock.RUnlock()
	device, ok := m.Map[uuid]
	return device, ok
}

// GetByID 通过ID获取设备
func (m *DevicesMap) GetByID(id string) (*Device, bool) {
	m.Lock.RLock()
	defer m.Lock.RUnlock()
	for _, device := range m.Map {
		if device.ID == id {
			return device, true
		}
	}
	return nil, false
}

// Remove 移除设备
func (m *DevicesMap) Remove(uuid string) {
	m.Lock.Lock()
	defer m.Lock.Unlock()
	delete(m.Map, uuid)
}

// All 获取所有设备
func (m *DevicesMap) All() []*Device {
	m.Lock.RLock()
	defer m.Lock.RUnlock()
	devices := make([]*Device, 0, len(m.Map))
	for _, device := range m.Map {
		devices = append(devices, device)
	}
	return devices
}

// 全局设备列表（持久化版本）
var DeviceDB = &DevicesMap{
	Map: make(map[string]*Device),
}

// DeviceSessionCallback 设备会话回调函数类型
type DeviceSessionCallback func(deviceID string, session *melody.Session)

// 设备会话回调
var (
	deviceOnlineCallbacks  []DeviceSessionCallback
	deviceOfflineCallbacks []func(deviceID string)
	callbackMutex          sync.RWMutex
)

// RegisterDeviceOnlineCallback 注册设备上线回调
func RegisterDeviceOnlineCallback(callback DeviceSessionCallback) {
	callbackMutex.Lock()
	defer callbackMutex.Unlock()
	deviceOnlineCallbacks = append(deviceOnlineCallbacks, callback)
}

// RegisterDeviceOfflineCallback 注册设备离线回调
func RegisterDeviceOfflineCallback(callback func(deviceID string)) {
	callbackMutex.Lock()
	defer callbackMutex.Unlock()
	deviceOfflineCallbacks = append(deviceOfflineCallbacks, callback)
}

// CreateDeviceFromInfo 从modules.Device创建扩展的Device结构
func CreateDeviceFromInfo(device modules.Device, uuid string, ip string) *Device {
	now := time.Now().Format(time.RFC3339)
	name := device.Hostname
	if name == "" {
		name = "Unknown Device"
	}

	return &Device{
		Device:     device,
		UUID:       uuid,
		Name:       name,
		IP:         ip,
		Status:     DeviceStatusOnline,
		LastOnline: now,
		FirstSeen:  now,
	}
}

// Socks5Tunnel 表示一个SOCKS5代理隧道配置
type Socks5Tunnel struct {
	ID         int    `json:"id"`
	DeviceID   string `json:"device_id"`  // 关联的设备ID
	Port       int    `json:"port"`       // 服务器上的监听端口
	Username   string `json:"username"`   // 认证用户名
	Password   string `json:"password"`   // 认证密码
	Status     string `json:"status"`     // 状态：pending, active, error
	CreateTime int64  `json:"create_time"`
	UpdateTime int64  `json:"update_time"`
	Error      string `json:"error"`      // 错误信息
}

// 创建SOCKS5隧道表
func createSocks5TunnelTable() error {
	_, err := DB.Exec(`CREATE TABLE IF NOT EXISTS socks5_tunnels (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		device_id TEXT NOT NULL,
		port INTEGER NOT NULL,
		username TEXT NOT NULL,
		password TEXT NOT NULL,
		status TEXT NOT NULL DEFAULT 'pending',
		create_time INTEGER NOT NULL,
		update_time INTEGER NOT NULL,
		error TEXT
	)`)
	return err
}

// AddSocks5Tunnel 添加新的SOCKS5隧道配置
func AddSocks5Tunnel(tunnel *Socks5Tunnel) error {
	now := time.Now().Unix()
	tunnel.CreateTime = now
	tunnel.UpdateTime = now
	
	result, err := DB.Exec(`INSERT INTO socks5_tunnels 
		(device_id, port, username, password, status, create_time, update_time, error) 
		VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
		tunnel.DeviceID, tunnel.Port, tunnel.Username, tunnel.Password, 
		tunnel.Status, tunnel.CreateTime, tunnel.UpdateTime, tunnel.Error)
	
	if err != nil {
		return err
	}
	
	id, err := result.LastInsertId()
	if err != nil {
		return err
	}
	
	tunnel.ID = int(id)
	return nil
}

// UpdateSocks5TunnelStatus 更新SOCKS5隧道状态
func UpdateSocks5TunnelStatus(id int, status string, errorMsg string) error {
	_, err := DB.Exec(`UPDATE socks5_tunnels SET status = ?, error = ?, update_time = ? WHERE id = ?`,
		status, errorMsg, time.Now().Unix(), id)
	return err
}

// GetSocks5TunnelsByDevice 获取设备的所有SOCKS5隧道
func GetSocks5TunnelsByDevice(deviceID string) ([]*Socks5Tunnel, error) {
	rows, err := DB.Query(`SELECT id, device_id, port, username, password, status, create_time, update_time, error 
		FROM socks5_tunnels WHERE device_id = ? ORDER BY id DESC`, deviceID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	
	var tunnels []*Socks5Tunnel
	for rows.Next() {
		tunnel := &Socks5Tunnel{}
		err := rows.Scan(&tunnel.ID, &tunnel.DeviceID, &tunnel.Port, &tunnel.Username, &tunnel.Password,
			&tunnel.Status, &tunnel.CreateTime, &tunnel.UpdateTime, &tunnel.Error)
		if err != nil {
			return nil, err
		}
		tunnels = append(tunnels, tunnel)
	}
	
	return tunnels, nil
}

// GetAllSocks5Tunnels 获取所有SOCKS5隧道
func GetAllSocks5Tunnels() ([]*Socks5Tunnel, error) {
	rows, err := DB.Query(`SELECT id, device_id, port, username, password, status, create_time, update_time, error 
		FROM socks5_tunnels ORDER BY id DESC`)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	
	var tunnels []*Socks5Tunnel
	for rows.Next() {
		tunnel := &Socks5Tunnel{}
		err := rows.Scan(&tunnel.ID, &tunnel.DeviceID, &tunnel.Port, &tunnel.Username, &tunnel.Password,
			&tunnel.Status, &tunnel.CreateTime, &tunnel.UpdateTime, &tunnel.Error)
		if err != nil {
			return nil, err
		}
		tunnels = append(tunnels, tunnel)
	}
	
	return tunnels, nil
}

// DeleteSocks5Tunnel 删除SOCKS5隧道
func DeleteSocks5Tunnel(id int) error {
	_, err := DB.Exec(`DELETE FROM socks5_tunnels WHERE id = ?`, id)
	return err
}

// GetSocks5Tunnel 获取单个SOCKS5隧道
func GetSocks5Tunnel(id int) (*Socks5Tunnel, error) {
	tunnel := &Socks5Tunnel{}
	err := DB.QueryRow(`SELECT id, device_id, port, username, password, status, create_time, update_time, error 
		FROM socks5_tunnels WHERE id = ?`, id).Scan(
		&tunnel.ID, &tunnel.DeviceID, &tunnel.Port, &tunnel.Username, &tunnel.Password,
		&tunnel.Status, &tunnel.CreateTime, &tunnel.UpdateTime, &tunnel.Error)
	
	if err != nil {
		return nil, err
	}
	
	return tunnel, nil
}

// HandleDeviceOffline 处理设备离线逻辑
func HandleDeviceOffline(sessionUUID string) {
	if device, ok := Devices.Get(sessionUUID); ok {
		// 使用GORM更新设备状态为离线
		if GormDB != nil {
			GormDB.Model(&model.Device{}).
				Where("uuid = ?", sessionUUID).
				Updates(map[string]interface{}{
					"status":     "offline",
					"last_seen":  time.Now(),
					"updated_at": time.Now(),
				})
		}

		// 调用设备离线回调
		callbackMutex.RLock()
		for _, callback := range deviceOfflineCallbacks {
			go callback(device.ID)
		}
		callbackMutex.RUnlock()

		Info(nil, `CLIENT_OFFLINE`, ``, ``, map[string]any{
			`device`: map[string]any{
				`name`: device.Hostname,
				`ip`:   device.WAN,
			},
		})

		// 从内存映射中移除设备
		Devices.Remove(sessionUUID)
	}
}

// HandleDeviceOnline 处理设备上线逻辑
func HandleDeviceOnline(device modules.Device, sessionUUID string, ip string) {
	// 使用GORM创建或更新设备信息
	if GormDB != nil {
		var dbDevice model.Device

		// 使用硬件指纹查找现有设备：hostname + username + MAC地址
		// 如果MAC地址为空，则只用hostname + username
		var err error
		if device.MAC != "" {
			err = GormDB.Where("hostname = ? AND username = ? AND mac_address = ?",
				device.Hostname, device.Username, device.MAC).First(&dbDevice).Error
		} else {
			err = GormDB.Where("hostname = ? AND username = ?",
				device.Hostname, device.Username).First(&dbDevice).Error
		}

		if err != nil {
			// 没有找到现有设备，创建新设备
			dbDevice = model.Device{
				UUID:        sessionUUID,
				Hostname:    device.Hostname,
				Username:    device.Username,
				OS:          device.OS,
				Arch:        device.Arch,
				IPAddress:   ip,
				MACAddress:  device.MAC,
				Status:      "online",
				LastSeen:    time.Now(),
				FirstSeen:   time.Now(),
			}
			GormDB.Create(&dbDevice)
		} else {
			// 找到现有设备，更新其信息和连接UUID
			GormDB.Model(&dbDevice).Updates(map[string]interface{}{
				"uuid":        sessionUUID, // 更新为新的连接UUID
				"hostname":    device.Hostname,
				"username":    device.Username,
				"os":          device.OS,
				"arch":        device.Arch,
				"ip_address":  ip,
				"mac_address": device.MAC,
				"status":      "online",
				"last_seen":   time.Now(),
				"updated_at":  time.Now(),
			})
		}
	}

	// 添加到内存映射
	Devices.Set(sessionUUID, &device)

	// 获取设备的WebSocket会话并调用上线回调
	if session, ok := Melody.GetSessionByUUID(sessionUUID); ok {
		callbackMutex.RLock()
		for _, callback := range deviceOnlineCallbacks {
			go callback(device.ID, session)
		}
		callbackMutex.RUnlock()
	}

	Info(nil, `CLIENT_ONLINE`, ``, ``, map[string]any{
		`device`: map[string]any{
			`name`: device.Hostname,
			`ip`:   ip,
		},
	})
}

// GetOnlineDevices 获取在线设备列表（从内存）
func GetOnlineDevices() []Device {
	devices := make([]Device, 0)

	// 遍历内存中的设备连接
	Devices.IterCb(func(uuid string, device *modules.Device) bool {
		devices = append(devices, Device{
			Device: *device,
			UUID:   uuid,
			Status: DeviceStatusOnline,
		})
		return true // 继续遍历
	})

	return devices
}