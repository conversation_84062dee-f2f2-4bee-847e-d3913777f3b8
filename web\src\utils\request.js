import axios from 'axios';

// 创建请求实例
const request = axios.create({
    baseURL: '.',
    timeout: 10000,
});

// 请求拦截器
request.interceptors.request.use(
    (config) => {
        return config;
    },
    (error) => {
        return Promise.reject(error);
    }
);

// 响应拦截器
request.interceptors.response.use(
    (response) => {
        const data = response.data;
        // 统一处理新旧API格式
        if (data.code === 200) {
            return data;
        } else if (data.code === 0) {
            // 兼容旧格式
            return { code: 200, data: data.data, message: data.msg };
        }
        return data;
    },
    (error) => {
        if (error.response) {
            return Promise.reject(error.response.data);
        }
        return Promise.reject(error);
    }
);

export { request };
