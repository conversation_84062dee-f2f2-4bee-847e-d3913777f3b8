import React, { useState, useEffect } from 'react';
import { Button, message, Spin, Card } from 'antd';
import { EditOutlined, SaveOutlined, EyeOutlined } from '@ant-design/icons';
import MDEditor from '@uiw/react-md-editor';
import { deviceApi } from '../api/device';

const DeviceNotes = ({ deviceUuid, deviceName }) => {
  const [notes, setNotes] = useState('');
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [editing, setEditing] = useState(false);
  const [originalNotes, setOriginalNotes] = useState('');

  // 加载笔记
  const loadNotes = async () => {
    if (!deviceUuid) return;

    setLoading(true);
    try {
      const response = await deviceApi.getDeviceNotes(deviceUuid);
      console.log('笔记加载响应:', response);

      if (response.code === 200) {
        const notesContent = response.data.notes || '';
        setNotes(notesContent);
        setOriginalNotes(notesContent);
      } else {
        message.error('加载笔记失败: ' + (response.message || '未知错误'));
      }
    } catch (error) {
      console.error('加载笔记失败:', error);
      message.error('加载笔记失败: ' + (error.message || '网络错误'));
    } finally {
      setLoading(false);
    }
  };

  // 保存笔记
  const saveNotes = async () => {
    setSaving(true);
    try {
      const response = await deviceApi.updateDeviceNotes(deviceUuid, notes);
      console.log('笔记保存响应:', response);

      if (response.code === 200) {
        message.success('笔记保存成功');
        setOriginalNotes(notes);
        setEditing(false);
      } else {
        message.error('保存笔记失败: ' + (response.message || '未知错误'));
      }
    } catch (error) {
      console.error('保存笔记失败:', error);
      message.error('保存笔记失败: ' + (error.message || '网络错误'));
    } finally {
      setSaving(false);
    }
  };

  // 取消编辑
  const cancelEdit = () => {
    setNotes(originalNotes);
    setEditing(false);
  };

  // 检查是否有未保存的更改
  const hasUnsavedChanges = notes !== originalNotes;

  useEffect(() => {
    loadNotes();
  }, [deviceUuid]);

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
        <div style={{ marginTop: '16px' }}>加载笔记中...</div>
      </div>
    );
  }

  return (
    <Card
      title={`${deviceName || '设备'} 笔记`}
      extra={
        <div>
          {!editing ? (
            <Button
              type="primary"
              icon={<EditOutlined />}
              onClick={() => setEditing(true)}
            >
              编辑
            </Button>
          ) : (
            <div style={{ display: 'flex', gap: '8px' }}>
              <Button
                onClick={cancelEdit}
                disabled={saving}
              >
                取消
              </Button>
              <Button
                type="primary"
                icon={<SaveOutlined />}
                loading={saving}
                onClick={saveNotes}
                disabled={!hasUnsavedChanges}
              >
                保存
              </Button>
            </div>
          )}
        </div>
      }
      style={{ height: '100%' }}
      bodyStyle={{ height: 'calc(100% - 57px)', padding: '16px' }}
    >
      <div style={{ height: '100%' }}>
        {editing ? (
          <MDEditor
            value={notes}
            onChange={(value) => setNotes(value || '')}
            height={400}
            preview="edit"
            hideToolbar={false}
            data-color-mode="light"
          />
        ) : (
          <div style={{ height: '100%', overflow: 'auto' }}>
            {notes ? (
              <MDEditor.Markdown
                source={notes}
                style={{ 
                  backgroundColor: 'transparent',
                  padding: '16px',
                  minHeight: '400px'
                }}
              />
            ) : (
              <div 
                style={{ 
                  textAlign: 'center', 
                  color: '#999', 
                  padding: '50px',
                  fontSize: '16px'
                }}
              >
                <EyeOutlined style={{ fontSize: '48px', marginBottom: '16px' }} />
                <div>暂无笔记内容</div>
                <div style={{ fontSize: '14px', marginTop: '8px' }}>
                  点击"编辑"按钮开始记录设备相关信息
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </Card>
  );
};

export default DeviceNotes;
