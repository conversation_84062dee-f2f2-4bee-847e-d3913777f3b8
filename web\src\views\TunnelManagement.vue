<template>
  <div class="tunnel-management">
    <div class="header">
      <h2>隧道代理</h2>
      <p class="description">支持 TCP、UDP、HTTP、SOCKS5 隧道代理</p>
    </div>

    <!-- 操作栏 -->
    <div class="toolbar">
      <el-button type="primary" @click="showCreateDialog = true">
        <el-icon><Plus /></el-icon>
        新增隧道
      </el-button>
    </div>

    <!-- 隧道列表 -->
    <div class="tunnel-table">
      <el-table 
        :data="tunnels" 
        v-loading="loading"
        style="width: 100%"
        :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
      >
        <el-table-column prop="id" label="ID" width="60" />
        <el-table-column prop="tunnel_name" label="配置名" width="120" />
        <el-table-column prop="server_port" label="端口" width="80" />
        <el-table-column prop="tunnel_type" label="类型" width="80">
          <template #default="scope">
            <el-tag :type="getTypeTagType(scope.row.tunnel_type)">
              {{ scope.row.tunnel_type.toUpperCase() }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="server_port" label="服务端口" width="100" />
        <el-table-column prop="username" label="用户名" width="100">
          <template #default="scope">
            {{ scope.row.username || 'abc' }}
          </template>
        </el-table-column>
        <el-table-column prop="password" label="密码" width="100">
          <template #default="scope">
            {{ scope.row.password || 'abc' }}
          </template>
        </el-table-column>
        <el-table-column label="隧道状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.status === 'active' ? 'success' : 'info'">
              {{ scope.row.status === 'active' ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="客户端状态" width="120">
          <template #default="scope">
            <el-tag :type="scope.row.device_status === 'online' ? 'success' : 'danger'">
              {{ scope.row.device_status === 'online' ? '在线' : '离线' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button 
              :type="scope.row.enabled ? 'warning' : 'success'"
              size="small"
              @click="toggleTunnel(scope.row)"
            >
              {{ scope.row.enabled ? '停用' : '启用' }}
            </el-button>
            <el-button type="primary" size="small" @click="editTunnel(scope.row)">
              编辑
            </el-button>
            <el-button type="danger" size="small" @click="deleteTunnel(scope.row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 创建/编辑隧道对话框 -->
    <el-dialog
      :title="isEdit ? '编辑隧道' : '新增隧道'"
      v-model="showCreateDialog"
      width="500px"
      :before-close="handleDialogClose"
    >
      <el-form
        ref="tunnelFormRef"
        :model="tunnelForm"
        :rules="tunnelRules"
        label-width="100px"
      >
        <el-form-item label="设备" prop="device_uuid" v-if="!isEdit">
          <el-select v-model="tunnelForm.device_uuid" placeholder="请选择设备" style="width: 100%">
            <el-option
              v-for="device in onlineDevices"
              :key="device.uuid"
              :label="`${device.hostname} (${device.ip_address})`"
              :value="device.uuid"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="配置名" prop="tunnel_name">
          <el-input v-model="tunnelForm.tunnel_name" placeholder="请输入配置名" />
        </el-form-item>
        <el-form-item label="类型" prop="tunnel_type">
          <el-select v-model="tunnelForm.tunnel_type" placeholder="请选择类型" style="width: 100%">
            <el-option label="SOCKS5" value="socks5" />
            <el-option label="TCP" value="tcp" />
            <el-option label="HTTP" value="http" />
          </el-select>
        </el-form-item>
        <el-form-item label="用户名" prop="username">
          <el-input v-model="tunnelForm.username" placeholder="可选，用于认证" />
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <el-input v-model="tunnelForm.password" type="password" placeholder="可选，用于认证" />
        </el-form-item>
        <el-form-item label="启用">
          <el-switch v-model="tunnelForm.enabled" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showCreateDialog = false">取消</el-button>
          <el-button type="primary" @click="submitTunnel" :loading="submitting">
            {{ isEdit ? '更新' : '创建' }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { tunnelApi, deviceApi } from '@/api'

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const tunnels = ref([])
const onlineDevices = ref([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)

// 对话框相关
const showCreateDialog = ref(false)
const isEdit = ref(false)
const tunnelFormRef = ref()

// 表单数据
const tunnelForm = reactive({
  device_uuid: '',
  tunnel_name: '',
  tunnel_type: 'socks5',
  username: '',
  password: '',
  enabled: true
})

// 表单验证规则
const tunnelRules = {
  device_uuid: [
    { required: true, message: '请选择设备', trigger: 'change' }
  ],
  tunnel_name: [
    { required: true, message: '请输入配置名', trigger: 'blur' }
  ],
  tunnel_type: [
    { required: true, message: '请选择类型', trigger: 'change' }
  ]
}

// 获取隧道列表
const fetchTunnels = async () => {
  loading.value = true
  try {
    const response = await tunnelApi.getTunnels({
      page: currentPage.value,
      page_size: pageSize.value
    })
    tunnels.value = response.data.list
    total.value = response.data.total
  } catch (error) {
    ElMessage.error('获取隧道列表失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

// 获取在线设备列表
const fetchOnlineDevices = async () => {
  try {
    const response = await deviceApi.getDevices({ status: 'online' })
    onlineDevices.value = response.data.list
  } catch (error) {
    console.error('获取在线设备失败:', error)
  }
}

// 获取类型标签样式
const getTypeTagType = (type) => {
  const typeMap = {
    socks5: 'primary',
    tcp: 'success',
    http: 'warning'
  }
  return typeMap[type] || 'info'
}

// 分页处理
const handleSizeChange = (val) => {
  pageSize.value = val
  fetchTunnels()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  fetchTunnels()
}

// 重置表单
const resetForm = () => {
  Object.assign(tunnelForm, {
    device_uuid: '',
    tunnel_name: '',
    tunnel_type: 'socks5',
    username: '',
    password: '',
    enabled: true
  })
  tunnelFormRef.value?.clearValidate()
}

// 关闭对话框
const handleDialogClose = () => {
  resetForm()
  isEdit.value = false
  showCreateDialog.value = false
}

// 提交隧道
const submitTunnel = async () => {
  if (!tunnelFormRef.value) return
  
  try {
    await tunnelFormRef.value.validate()
    submitting.value = true
    
    if (isEdit.value) {
      await tunnelApi.updateTunnel(tunnelForm.tunnel_id, tunnelForm)
      ElMessage.success('隧道更新成功')
    } else {
      await tunnelApi.createTunnel(tunnelForm.device_uuid, tunnelForm)
      ElMessage.success('隧道创建成功')
    }
    
    showCreateDialog.value = false
    fetchTunnels()
  } catch (error) {
    if (error.message) {
      ElMessage.error(error.message)
    }
  } finally {
    submitting.value = false
  }
}

// 编辑隧道
const editTunnel = (tunnel) => {
  isEdit.value = true
  Object.assign(tunnelForm, tunnel)
  showCreateDialog.value = true
}

// 切换隧道状态
const toggleTunnel = async (tunnel) => {
  try {
    await tunnelApi.toggleTunnel(tunnel.tunnel_id)
    ElMessage.success('隧道状态切换成功')
    fetchTunnels()
  } catch (error) {
    ElMessage.error('操作失败: ' + error.message)
  }
}

// 删除隧道
const deleteTunnel = async (tunnel) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除隧道 "${tunnel.tunnel_name}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    await tunnelApi.deleteTunnel(tunnel.tunnel_id)
    ElMessage.success('隧道删除成功')
    fetchTunnels()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败: ' + error.message)
    }
  }
}

// 初始化
onMounted(() => {
  fetchTunnels()
  fetchOnlineDevices()
})
</script>

<style scoped>
.tunnel-management {
  padding: 20px;
}

.header {
  margin-bottom: 20px;
}

.header h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.description {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.toolbar {
  margin-bottom: 20px;
}

.tunnel-table {
  background: white;
  border-radius: 4px;
  overflow: hidden;
}

.pagination {
  padding: 20px;
  text-align: right;
  border-top: 1px solid #ebeef5;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
