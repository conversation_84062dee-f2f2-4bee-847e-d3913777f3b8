import React from 'react';
import {ModalForm, ProFormText} from '@ant-design/pro-form';
import {request} from "../../utils/utils";
import i18n from "../../locale/locale";
import {message} from "antd";

function Execute(props) {
	async function onFinish(form) {
		// 使用设备连接ID而不是数据库ID
		const deviceConnId = props.device.device_id || props.device.conn || props.device.uuid || props.device.id;
		form.device = deviceConnId;
		let basePath = location.origin + location.pathname + 'api/device/';
		request(basePath + 'exec', form).then(res => {
			if (res.data.code === 0) {
				message.success(i18n.t('EXECUTE.EXECUTION_SUCCESS'));
			}
		});
	}

	return (
		<ModalForm
			modalProps={{
				destroyOnClose: true,
				maskClosable: false,
			}}
			title={i18n.t('EXECUTE.TITLE')}
			width={380}
			onFinish={onFinish}
			onVisibleChange={open => {
				if (!open) props.onCancel();
			}}
			submitter={{
				render: (_, elems) => elems.pop()
			}}
			{...props}
		>
			<ProFormText
				width="md"
				name="cmd"
				label={i18n.t('EXECUTE.CMD_PLACEHOLDER')}
				rules={[{
					required: true
				}]}
			/>
			<ProFormText
				width="md"
				name="args"
				label={i18n.t('EXECUTE.ARGS_PLACEHOLDER')}
			/>
		</ModalForm>
	)
}

export default Execute;