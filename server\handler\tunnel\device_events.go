package tunnel

import (
	"fmt"
	"log"
	"Spark/server/common"
	"Spark/server/model"
	"Spark/server/service"
)

var (
	deviceService *service.DeviceService
)

// InitDeviceEvents 初始化设备事件处理
func InitDeviceEvents() {
	if common.GetGormDB() != nil {
		deviceService = service.NewDeviceService(common.GetGormDB())
	}
	// tunnelService 在 InitNewAPI 中已经初始化，这里不需要重复初始化
}

// OnDeviceConnect 设备连接事件处理
func OnDeviceConnect(deviceUUID string) {
	if deviceService == nil || tunnelService == nil {
		return
	}

	log.Printf("设备上线: %s", deviceUUID)

	// 从内存中获取设备信息
	if device, ok := common.Devices.Get(deviceUUID); ok {
		// 创建或更新设备信息到GORM数据库
		deviceInfo := &model.DeviceInfo{
			UUID:        deviceUUID,
			Hostname:    device.Hostname,
			Username:    device.Username,
			OS:          device.OS,
			Arch:        device.Arch,
			CPUInfo:     fmt.Sprintf("%s", device.CPU),
			MemoryTotal: int64(device.RAM.Total),
			IPAddress:   device.WAN,
		}

		if _, err := deviceService.CreateOrUpdateDevice(deviceInfo); err != nil {
			log.Printf("创建或更新设备失败: %v", err)
		}
	} else {
		// 如果内存中没有设备信息，只更新状态
		if err := deviceService.UpdateDeviceStatus(deviceUUID, "online"); err != nil {
			log.Printf("更新设备状态失败: %v", err)
			return
		}
	}

	// 启动该设备的所有启用隧道
	tunnelService.OnDeviceOnline(deviceUUID)
}

// OnDeviceDisconnect 设备断开连接事件处理
func OnDeviceDisconnect(deviceUUID string) {
	if deviceService == nil || tunnelService == nil {
		return
	}

	log.Printf("设备离线: %s", deviceUUID)

	// 更新设备状态为离线
	if err := deviceService.UpdateDeviceStatus(deviceUUID, "offline"); err != nil {
		log.Printf("更新设备状态失败: %v", err)
	}

	// 停止该设备的所有隧道
	tunnelService.OnDeviceOffline(deviceUUID)
}
