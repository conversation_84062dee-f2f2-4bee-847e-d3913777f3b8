package service

import (
	"context"
	"fmt"
	"io"
	"log"
	"net"
	"sync"
	"time"

	"Spark/modules"
	"Spark/server/common"
	"Spark/server/model"
	"github.com/things-go/go-socks5"
)

type SOCKS5TunnelManager struct {
	tunnels map[string]*SOCKS5Tunnel
	mutex   sync.RWMutex
}

type SOCKS5Tunnel struct {
	ID         string
	Config     *model.Tunnel
	Listener   net.Listener
	DeviceUUID string
	Server     *socks5.Server
	IsRunning  bool
	mutex      sync.RWMutex
	connections      map[string]*ConnectionInfo
	responseChannels map[string]chan bool
	connMutex        sync.RWMutex
}

type ConnectionInfo struct {
	ID         string
	TargetAddr string
	ClientConn io.Writer
	CreatedAt  time.Time
}

var socks5Manager *SOCKS5TunnelManager

func init() {
	socks5Manager = &SOCKS5TunnelManager{
		tunnels: make(map[string]*SOCKS5Tunnel),
	}
}

func StartSOCKS5Tunnel(tunnel *model.Tunnel) error {
	if tunnel.TunnelType != "socks5" {
		return fmt.Errorf("隧道类型不是SOCKS5")
	}

	socks5Manager.mutex.Lock()
	defer socks5Manager.mutex.Unlock()

	if existingTunnel, exists := socks5Manager.tunnels[tunnel.TunnelID]; exists {
		if existingTunnel.IsRunning {
			return fmt.Errorf("隧道已经在运行")
		}
	}

	socks5Tunnel := &SOCKS5Tunnel{
		ID:               tunnel.TunnelID,
		Config:           tunnel,
		DeviceUUID:       tunnel.DeviceUUID,
		IsRunning:        false,
		connections:      make(map[string]*ConnectionInfo),
		responseChannels: make(map[string]chan bool),
	}

	var opts []socks5.Option
	
	if tunnel.Username != "" && tunnel.Password != "" {
		creds := socks5.StaticCredentials{
			tunnel.Username: tunnel.Password,
		}
		opts = append(opts, socks5.WithCredential(creds))
	} else {
		opts = append(opts, socks5.WithAuthMethods([]socks5.Authenticator{
			&socks5.NoAuthAuthenticator{},
		}))
	}

	opts = append(opts, socks5.WithDial(socks5Tunnel.customDial))
	socks5Tunnel.Server = socks5.NewServer(opts...)

	listener, err := net.Listen("tcp", fmt.Sprintf(":%d", tunnel.ServerPort))
	if err != nil {
		return fmt.Errorf("创建监听器失败: %v", err)
	}
	socks5Tunnel.Listener = listener
	socks5Manager.tunnels[tunnel.TunnelID] = socks5Tunnel

	go func() {
		socks5Tunnel.IsRunning = true
		if err := socks5Tunnel.Server.Serve(listener); err != nil {
			log.Printf("SOCKS5隧道服务器错误: %v", err)
			socks5Tunnel.IsRunning = false
		}
	}()

	log.Printf("SOCKS5隧道启动成功: %s (端口: %d)", tunnel.TunnelName, tunnel.ServerPort)
	return nil
}

func (tunnel *SOCKS5Tunnel) customDial(ctx context.Context, network, address string) (net.Conn, error) {
	connID := fmt.Sprintf("%s_%d", tunnel.ID, time.Now().UnixNano())
	log.Printf("收到SOCKS5连接请求 [ConnID=%s, Target=%s]", connID, address)

	sessionUUID, found := tunnel.findDeviceSession()
	if !found {
		log.Printf("设备不在线，拒绝连接 [DeviceUUID=%s]", tunnel.DeviceUUID)
		return nil, fmt.Errorf("设备不在线")
	}

	clientConn, serverConn := net.Pipe()

	tunnel.connMutex.Lock()
	tunnel.connections[connID] = &ConnectionInfo{
		ID:         connID,
		TargetAddr: address,
		ClientConn: serverConn,
		CreatedAt:  time.Now(),
	}
	tunnel.connMutex.Unlock()

	packet := modules.Packet{
		Act: "SOCKS5_TUNNEL_CONNECT",
		Data: map[string]interface{}{
			"tunnel_id":     tunnel.ID,
			"connection_id": connID,
			"target_addr":   address,
		},
	}

	if !common.SendPackByUUID(packet, sessionUUID) {
		log.Printf("发送连接请求失败 [ConnID=%s]", connID)
		tunnel.cleanupConnection(connID)
		clientConn.Close()
		serverConn.Close()
		return nil, fmt.Errorf("发送连接请求失败")
	}

	log.Printf("SOCKS5连接请求已发送 [ConnID=%s, Target=%s]", connID, address)
	go tunnel.handleConnection(ctx, connID, address, clientConn, serverConn)
	return clientConn, nil
}

func (tunnel *SOCKS5Tunnel) findDeviceSession() (string, bool) {
	if _, exists := common.Devices.Get(tunnel.DeviceUUID); exists {
		return tunnel.DeviceUUID, true
	}
	
	var sessionUUID string
	var found bool
	common.Devices.IterCb(func(uuid string, device *modules.Device) bool {
		if device.ID == tunnel.DeviceUUID {
			sessionUUID = uuid
			found = true
			return false
		}
		return true
	})
	
	return sessionUUID, found
}

func (tunnel *SOCKS5Tunnel) cleanupConnection(connID string) {
	tunnel.connMutex.Lock()
	defer tunnel.connMutex.Unlock()
	delete(tunnel.connections, connID)
	delete(tunnel.responseChannels, connID)
}

func (tunnel *SOCKS5Tunnel) handleConnection(ctx context.Context, connID, targetAddr string, clientConn, serverConn net.Conn) {
	defer func() {
		clientConn.Close()
		serverConn.Close()
		tunnel.cleanupConnection(connID)
	}()

	success := tunnel.waitForConnectionResponse(ctx, connID, targetAddr)
	if !success {
		log.Printf("连接建立失败 [ConnID=%s, Target=%s]", connID, targetAddr)
		return
	}

	log.Printf("连接建立成功，开始数据转发 [ConnID=%s, Target=%s]", connID, targetAddr)
	go tunnel.forwardClientToDevice(connID, targetAddr, clientConn)
	tunnel.forwardDeviceToClient(connID, serverConn)
}

func (tunnel *SOCKS5Tunnel) waitForConnectionResponse(ctx context.Context, connID, targetAddr string) bool {
	responseChan := make(chan bool, 1)
	tunnel.connMutex.Lock()
	tunnel.responseChannels[connID] = responseChan
	tunnel.connMutex.Unlock()

	timeout := time.NewTimer(10 * time.Second)
	defer timeout.Stop()

	select {
	case success := <-responseChan:
		log.Printf("收到连接响应 [ConnID=%s, Target=%s, Success=%v]", connID, targetAddr, success)
		return success
	case <-timeout.C:
		log.Printf("连接响应超时 [ConnID=%s, Target=%s]", connID, targetAddr)
		return false
	case <-ctx.Done():
		log.Printf("连接上下文取消 [ConnID=%s, Target=%s]", connID, targetAddr)
		return false
	}
}

func (tunnel *SOCKS5Tunnel) forwardClientToDevice(connID, targetAddr string, clientConn net.Conn) {
	log.Printf("开始监听SOCKS5客户端数据 [ConnID=%s, Target=%s]", connID, targetAddr)
	buffer := make([]byte, 32*1024)
	for {
		n, err := clientConn.Read(buffer)
		if err != nil {
			log.Printf("SOCKS5客户端连接关闭 [ConnID=%s]: %v", connID, err)
			break
		}

		log.Printf("收到SOCKS5客户端数据 [ConnID=%s, Size=%d]", connID, n)
		data := make([]byte, n)
		copy(data, buffer[:n])

		packet := modules.Packet{
			Act: "SOCKS5_TUNNEL_DATA_TO_DEVICE",
			Data: map[string]interface{}{
				"tunnel_id":     tunnel.ID,
				"connection_id": connID,
				"target_addr":   targetAddr,
				"data":          data,
			},
		}

		sessionUUID, found := tunnel.findDeviceSession()
		if found && common.SendPackByUUID(packet, sessionUUID) {
			log.Printf("转发数据到设备 [ConnID=%s, Size=%d]", connID, len(data))
		} else {
			log.Printf("转发数据到设备失败 [ConnID=%s]", connID)
			break
		}
	}
	log.Printf("SOCKS5客户端数据转发结束 [ConnID=%s]", connID)
}

func (tunnel *SOCKS5Tunnel) forwardDeviceToClient(connID string, serverConn net.Conn) {
	buffer := make([]byte, 1)
	for {
		_, err := serverConn.Read(buffer)
		if err != nil {
			log.Printf("服务端连接关闭 [ConnID=%s]: %v", connID, err)
			break
		}
	}
}

func StopSOCKS5Tunnel(tunnelID string) error {
	socks5Manager.mutex.Lock()
	defer socks5Manager.mutex.Unlock()

	tunnel, exists := socks5Manager.tunnels[tunnelID]
	if !exists {
		log.Printf("SOCKS5隧道不在内存中，可能已经停止: %s", tunnelID)
		return nil
	}

	tunnel.mutex.Lock()
	defer tunnel.mutex.Unlock()

	if !tunnel.IsRunning {
		log.Printf("SOCKS5隧道未运行，清理内存记录: %s", tunnelID)
		delete(socks5Manager.tunnels, tunnelID)
		return nil
	}

	if tunnel.Listener != nil {
		tunnel.Listener.Close()
	}

	tunnel.IsRunning = false
	delete(socks5Manager.tunnels, tunnelID)

	log.Printf("SOCKS5隧道停止成功: %s", tunnelID)
	return nil
}

func NotifyConnectionResponse(tunnelID, connID string, success bool) error {
	socks5Manager.mutex.RLock()
	tunnel, exists := socks5Manager.tunnels[tunnelID]
	socks5Manager.mutex.RUnlock()

	if !exists {
		return fmt.Errorf("隧道不存在: %s", tunnelID)
	}

	tunnel.connMutex.RLock()
	responseChan, exists := tunnel.responseChannels[connID]
	tunnel.connMutex.RUnlock()

	if !exists {
		return fmt.Errorf("响应通道不存在: %s", connID)
	}

	select {
	case responseChan <- success:
		log.Printf("连接响应已发送 [TunnelID=%s, ConnID=%s, Success=%v]", tunnelID, connID, success)
	default:
		log.Printf("连接响应通道已满 [TunnelID=%s, ConnID=%s]", tunnelID, connID)
	}

	return nil
}

func ForwardDataToClient(tunnelID, connID string, data []byte) error {
	socks5Manager.mutex.RLock()
	tunnel, exists := socks5Manager.tunnels[tunnelID]
	socks5Manager.mutex.RUnlock()

	if !exists {
		return fmt.Errorf("隧道不存在: %s", tunnelID)
	}

	tunnel.connMutex.RLock()
	connInfo, exists := tunnel.connections[connID]
	tunnel.connMutex.RUnlock()

	if !exists {
		return fmt.Errorf("连接不存在: %s", connID)
	}

	_, err := connInfo.ClientConn.Write(data)
	if err != nil {
		log.Printf("写入SOCKS5客户端失败 [ConnID=%s]: %v", connID, err)
		tunnel.cleanupConnection(connID)
		return err
	}

	log.Printf("数据转发成功 [TunnelID=%s, ConnID=%s, Size=%d]", tunnelID, connID, len(data))
	return nil
}
