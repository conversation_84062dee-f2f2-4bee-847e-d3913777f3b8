//go:build linux

package process

import (
	"fmt"
	"runtime"
)

// Linux特定的沙箱检测方法
func (sd *SandboxDetector) DetectSandbox() (bool, string, int) {
	totalScore := 0
	var reasons []string
	
	// Linux平台的基础检测
	if runtime.NumCPU() < 2 {
		reasons = append(reasons, "CPU核心数过少")
		totalScore += 10
	}
	
	// 检查是否在容器中运行
	if sd.checkDockerContainer() {
		reasons = append(reasons, "检测到Docker容器环境")
		totalScore += 20
	}
	
	// 检查虚拟化环境
	if sd.checkVirtualization() {
		reasons = append(reasons, "检测到虚拟化环境")
		totalScore += 15
	}
	
	// 阈值判断
	threshold := 25
	isDetected := totalScore >= threshold
	
	reasonStr := ""
	if len(reasons) > 0 {
		reasonStr = fmt.Sprintf("Linux检测: %v", reasons)
	} else {
		reasonStr = "Linux环境检测通过"
	}
	
	return isDetected, reasonStr, totalScore
}

// 检查Docker容器
func (sd *SandboxDetector) checkDockerContainer() bool {
	// 简化实现：检查/.dockerenv文件是否存在
	// 实际实现应该检查cgroup、proc/1/cgroup等
	return false
}

// 检查虚拟化环境
func (sd *SandboxDetector) checkVirtualization() bool {
	// 简化实现：检查DMI信息、CPU特征等
	return false
}

// Linux特定的反调试检测方法
func (ade *AntiDebugEngine) DetectDebugger() (bool, string) {
	// Linux平台的反调试检测
	checks := []func() (bool, string){
		ade.checkPtraceAttach,
		ade.checkDebuggerProcesses,
		ade.checkProcStatus,
	}
	
	for _, check := range checks {
		if detected, reason := check(); detected {
			return true, reason
		}
	}
	
	return false, "Linux反调试检测通过"
}

// 公开的调试器检测方法
func (ade *AntiDebugEngine) CheckIsDebuggerPresentPublic() bool {
	detected, _ := ade.DetectDebugger()
	return detected
}

// 检查ptrace附加
func (ade *AntiDebugEngine) checkPtraceAttach() (bool, string) {
	// 简化实现：尝试ptrace自己
	// 实际实现需要调用ptrace系统调用
	return false, ""
}

// 检查调试器进程
func (ade *AntiDebugEngine) checkDebuggerProcesses() (bool, string) {
	debuggerProcesses := []string{
		"gdb", "strace", "ltrace", "objdump", "readelf",
		"hexdump", "xxd", "od", "strings", "nm",
	}
	
	for _, debugger := range debuggerProcesses {
		if ade.checkProcessRunning(debugger) {
			return true, fmt.Sprintf("检测到调试器进程: %s", debugger)
		}
	}
	
	return false, ""
}

// 检查/proc/self/status
func (ade *AntiDebugEngine) checkProcStatus() (bool, string) {
	// 简化实现：检查TracerPid字段
	// 实际实现需要读取/proc/self/status文件
	return false, ""
}

// 辅助函数：检查进程是否运行
func (ade *AntiDebugEngine) checkProcessRunning(processName string) bool {
	// 简化实现，实际应该检查/proc目录或使用ps命令
	return false
}

// Linux特定的高级注入引擎方法
func (aie *AdvancedInjectionEngine) InjectUltimate(pid int32, shellcode []byte, method string) error {
	// Linux平台使用标准注入
	selectedMethod, err := SelectOptimalMethod(method)
	if err != nil {
		return err
	}
	
	return ExecuteCrossPlatformInjection(pid, shellcode, selectedMethod)
}

func (aie *AdvancedInjectionEngine) testAPIAvailability(apiName string) bool {
	// 测试Linux API/系统调用是否可用
	switch apiName {
	case "ptrace":
		return checkPtracePermission()
	case "mmap", "dlopen":
		return true
	case "process_vm_readv", "process_vm_writev":
		return true // 假设内核支持
	default:
		return false
	}
}

// 检查ptrace权限
func checkPtracePermission() bool {
	// 简化实现：检查/proc/sys/kernel/yama/ptrace_scope
	// 实际实现需要读取该文件并检查权限
	return true
}
