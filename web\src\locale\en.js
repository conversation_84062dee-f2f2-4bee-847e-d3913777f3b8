export default {
	"COMMON.REQUEST_FAILED": "Request failed",
	"COMMON.REQUEST_TIMEOUT": "Request timeout",
	"COMMON.PAGE_NOT_FOUND": "Page Not Found",
	"COMMON.UNKNOWN_ERROR": "Unknown error",
	"COMMON.INVALID_PARAMETER": "Invalid parameter",
	"COMMON.OPERATION_NOT_SUPPORTED": "Operation is not supported",
	"COMMON.DEVICE_NOT_EXIST": "Device not exists or not online",
	"COMMON.RESPONSE_TIMEOUT": "Response timeout",
	"COMMON.RECONNECTING": "Reconnecting...",
	"COMMON.DISCONNECTED": "Session disconnected",
	"COMMON.CONNECTION_FAILED": "Connection failed",

	"COMMON.UPDATE_DETAILS": "View Details",
	"COMMON.UPDATE_DISMISS": "Dismiss",

	"COMMON.HOURS": "h",
	"COMMON.MINUTES": "m",
	"COMMON.COLON": ": ",
	"COMMON.YES": "Yes",
	"COMMON.NO": "No",
	"COMMON.REFRESH": "Refresh",
	"COMMON.SUBMIT": "Submit",
	"COMMON.CANCEL": "Cancel",
	"COMMON.FIELD_REQUIRED": "This field is required",
	"COMMON.USERNAME": "Username",
	"COMMON.PASSWORD": "Password",
	"COMMON.DESCRIPTION": "Description",
	"COMMON.ACTIONS": "Actions",
	"COMMON.DATA_NOT_FOUND": "Data not found",
	"COMMON.LOADING": "Loading...",

	"OVERVIEW.TITLE": "Overview",
	"OVERVIEW.HOSTNAME": "Hostname",
	"OVERVIEW.USERNAME": "Username",
	"OVERVIEW.CPU_USAGE": "CPU Usage",
	"OVERVIEW.CPU_LOGICAL_CORES": "Logical Cores",
	"OVERVIEW.CPU_PHYSICAL_CORES": "Physical Cores",
	"OVERVIEW.RAM_USAGE": "RAM Usage",
	"OVERVIEW.DISK_USAGE": "Disk Usage",
	"OVERVIEW.FREE": "Free",
	"OVERVIEW.USED": "Used",
	"OVERVIEW.TOTAL": "Total",
	"OVERVIEW.RAM": "RAM",
	"OVERVIEW.OS": "OS",
	"OVERVIEW.ARCH": "Arch",
	"OVERVIEW.UPTIME": "Uptime",
	"OVERVIEW.NETWORK": "Network",
	"OVERVIEW.OPERATIONS": "Operations",
	"OVERVIEW.TERMINAL": "Terminal",
	"OVERVIEW.PROC_MANAGER": "Process",
	"OVERVIEW.EXPLORER": "Explorer",
	"OVERVIEW.EXECUTE": "Execute",
	"OVERVIEW.SCREENSHOT": "Screenshot",
	"OVERVIEW.DESKTOP": "Desktop",
	"OVERVIEW.LOCK": "Lock",
	"OVERVIEW.LOGOFF": "Logoff",
	"OVERVIEW.HIBERNATE": "Hibernate",
	"OVERVIEW.SUSPEND": "Suspend",
	"OVERVIEW.RESTART": "Restart",
	"OVERVIEW.SHUTDOWN": "Shutdown",
	"OVERVIEW.OFFLINE": "Offline",
	"OVERVIEW.GENERATE": "Generate Client",
	"OVERVIEW.OPERATION_CONFIRM": "Are you sure to {0} this device?",
	"OVERVIEW.OPERATION_SUCCESS": "Operation executed",

	"EXPLORER.TITLE": "File Explorer",
	"EXPLORER.FILE_NAME": "Name",
	"EXPLORER.FILE_SIZE": "Size",
	"EXPLORER.DATE_MODIFIED": "Date Modified",
	"EXPLORER.FILE": "file",
	"EXPLORER.FOLDER": "folder",
	"EXPLORER.RENAME": "Rename",
	"EXPLORER.UPLOAD": "Upload",
	"EXPLORER.DELETE": "Delete",
	"EXPLORER.DOWNLOAD": "Download",
	"EXPLORER.EDIT_AS_TEXT": "Edit as text",
	"EXPLORER.UPLOADING": "Uploading...",
	"EXPLORER.UPLOAD_FAILED": "Upload failed",
	"EXPLORER.UPLOAD_ABORTED": "Upload aborted",
	"EXPLORER.UPLOAD_SUCCESS": "Upload success",
	"EXPLORER.UPLOAD_INVALID_PATH": "Cannot upload here",
	"EXPLORER.UPLOAD_CANCEL_CONFIRM": "Are you sure to cancel uploading?",
	"EXPLORER.DOWNLOAD_MULTI_CONFIRM": "It may take a long time, are you sure to continue?",
	"EXPLORER.DOWNLOAD_VOLUMES_ERROR": "Can not archive volumes",
	"EXPLORER.DELETE_MULTI_CONFIRM": "Are you sure to delete these items?",
	"EXPLORER.DELETE_CONFIRM": "Are you sure to delete this {0}?",
	"EXPLORER.DELETE_SUCCESS": "File or folder deleted",
	"EXPLORER.DELETE_INVALID_PATH": "Cannot delete disk partitions",
	"EXPLORER.DATE_TIME_FORMAT": "MMM D, YYYY h:mm A",
	"EXPLORER.MULTI_SELECT_LABEL": "Selected {0} item(s), {1} item(s) in total",
	"EXPLORER.FILE_OR_DIR_NOT_EXIST": "File or folder does not exist",
	"EXPLORER.OVERWRITE_CONFIRM": "File [ {0} ] already exists, overwrite?",
	"EXPLORER.OVERWRITE": "Overwrite",
	"EXPLORER.FILE_TOO_LARGE": "File is too large to read",
	"EXPLORER.UNSUPPORTED_ENCODING": "File encoding is not supported",
	"EXPLORER.NOT_SAVED_CONFIRM": "File is not saved, do you want to save it?",
	"EXPLORER.FILE_DO_NOT_SAVE": "Don't save",
	"EXPLORER.FILE_SAVE_SUCCESSFULLY": "File saved successfully",
	"EXPLORER.FILE_SAVE_FAILED": "Fail to save file",
	"EXPLORER.REACHED_MIN_FONT_SIZE": "Font size is already minimum",
	"EXPLORER.SAVE": "Save",
	"EXPLORER.SEARCH": "Search",
	"EXPLORER.REPLACE": "Replace",
	"EXPLORER.THEME": "Theme",
	"EXPLORER.FONT": "Font",
	"EXPLORER.ENLARGE": "Enlarge",
	"EXPLORER.SHRINK": "Shrink",
	"EXPLORER.CANCEL": "Cancel",

	"GENERATOR.HOST": "Host",
	"GENERATOR.PORT": "Port",
	"GENERATOR.PATH": "Path",
	"GENERATOR.OS_ARCH": "OS / Arch",
	"GENERATOR.NO_PREBUILT_FOUND": "The OS or Arch is not prebuilt",
	"GENERATOR.CONFIG_GENERATE_FAILED": "Failed to generate client config",
	"GENERATOR.CONFIG_TOO_LARGE": "Config is too large",

	"PROCMGR.TITLE": "Process Manager",
	"PROCMGR.PROCESS": "Process",
	"PROCMGR.KILL_PROCESS": "Kill",
	"PROCMGR.KILL_PROCESS_CONFIRM": "Are you sure to kill this process?",
	"PROCMGR.KILL_PROCESS_SUCCESSFULLY": "Process killed",

	"TERMINAL.TITLE": "Terminal",
	"TERMINAL.CREATE_SESSION_FAILED": "Failed to create terminal session",
	"TERMINAL.SESSION_CLOSED": "Terminal session closed",
	"TERMINAL.SPECIAL_KEYS": "Special Keys",
	"TERMINAL.FUNCTION_KEYS": "Function Keys",
	"TERMINAL.ZMODEM_FILE_TOO_LARGE": "File exceeds the size limit (16MB)",
	"TERMINAL.ZMODEM_TRANSFER_START": "File transfer started, please wait...",
	"TERMINAL.ZMODEM_TRANSFER_FAILED": "File transfer failed",
	"TERMINAL.ZMODEM_TRANSFER_SUCCESS": "File is transferred successfully",
	"TERMINAL.ZMODEM_TRANSFER_REJECTED": "Transfer request has been rejected",
	"TERMINAL.ZMODEM_UPLOADER_NO_FILE": "No file selected",
	"TERMINAL.ZMODEM_UPLOADER_CALL_FAILED": "Failed to pull up file uploader",
	"TERMINAL.ZMODEM_UPLOADER_CALL_TIMEOUT": "File selection timeout, please try again",
	"TERMINAL.ZMODEM_UPLOADER_TIP": "File selector will open, if not, please click 'Select File' button",
	"TERMINAL.ZMODEM_UPLOADER_WARNING": "If no file selected, please wait for 10 seconds to make session timeout",

	"DESKTOP.TITLE": "Desktop",
	"DESKTOP.CREATE_SESSION_FAILED": "Failed to create desktop session",
	"DESKTOP.SESSION_CLOSED": "Desktop session closed",
	"DESKTOP.FULLSCREEN_FAILED": "Failed to enter fullscreen",
	"DESKTOP.SCREENSHOT_FAILED": "Failed to take screenshot",
	"DESKTOP.FETCH_IMAGE_FAILED": "Failed to fetch screenshot image",
	"DESKTOP.NO_DISPLAY_FOUND": "No display found",

	"EXECUTE.TITLE": "Run",
	"EXECUTE.EXECUTION_SUCCESS": "Execution success",
	"EXECUTE.CMD_PLACEHOLDER": "Command",
	"EXECUTE.ARGS_PLACEHOLDER": "Arguments (separated by space)",

	"SOCKS5.TITLE": "SOCKS5 Tunnel Management",
	"SOCKS5.DEVICE_TITLE": "SOCKS5 Tunnels - {0}",
	"SOCKS5.LOCAL_PORT": "Local Port",
	"SOCKS5.REMOTE_HOST": "Remote Host",
	"SOCKS5.REMOTE_PORT": "Remote Port",
	"SOCKS5.STATUS": "Status",
	"SOCKS5.OPERATIONS": "Operations",
	"SOCKS5.CREATE_TUNNEL": "Create Tunnel",
	"SOCKS5.EDIT_TUNNEL": "Edit Tunnel",
	"SOCKS5.DELETE_TUNNEL": "Delete Tunnel",
	"SOCKS5.START_TUNNEL": "Start",
	"SOCKS5.STOP_TUNNEL": "Stop",
	"SOCKS5.TUNNEL_RUNNING": "Running",
	"SOCKS5.TUNNEL_STOPPED": "Stopped",
	"SOCKS5.TUNNEL_ERROR": "Error",
	"SOCKS5.CONFIRM_DELETE": "Are you sure to delete this tunnel?",
	"SOCKS5.TUNNEL_CREATED": "Tunnel created successfully",
	"SOCKS5.TUNNEL_UPDATED": "Tunnel updated successfully",
	"SOCKS5.TUNNEL_DELETED": "Tunnel deleted successfully",
	"SOCKS5.TUNNEL_STARTED": "Tunnel started successfully",
	"SOCKS5.TUNNEL_STOPPED": "Tunnel stopped successfully",
	"SOCKS5.ERROR_PORT_IN_USE": "Port already in use",
	"SOCKS5.ERROR_INVALID_PORT": "Invalid port number",
	"SOCKS5.ERROR_INVALID_HOST": "Invalid host",
	"SOCKS5.GLOBAL_MANAGER": "Global Tunnel Manager",
	"SOCKS5.DEVICE_MANAGER": "Device Tunnels",
	"SOCKS5.TUNNEL_MANAGER": "SOCKS5 Tunnel Manager",
	"SOCKS5.VIEW_DETAILS": "View Details",
	"SOCKS5.AUTHENTICATION": "Enable Authentication",
	"SOCKS5.BIND_ADDRESS": "Bind Address",
	"SOCKS5.NO_TUNNELS": "No tunnels found",
	"SOCKS5.NO_CONNECTIONS": "No active connections",
	"SOCKS5.ALL_TUNNELS": "All Tunnels",
	"SOCKS5.ACTIVE": "Active",
	"SOCKS5.INACTIVE": "Inactive",
	"SOCKS5.CLOSED": "Closed",
	"SOCKS5.TUNNEL_DETAILS": "Tunnel Details",
	"SOCKS5.BASIC_INFO": "Basic Information",
	"SOCKS5.CREATED_AT": "Created At",
	"SOCKS5.DEVICE_ID": "Device ID",
	"SOCKS5.STATISTICS": "Statistics",
	"SOCKS5.CONNECTIONS": "Connections",
	"SOCKS5.SOURCE": "Source",
	"SOCKS5.DESTINATION": "Destination",
	"SOCKS5.BYTES_SENT": "Bytes Sent",
	"SOCKS5.BYTES_RECEIVED": "Bytes Received",
	"SOCKS5.CONNECTED_SINCE": "Connected Since",
	"SOCKS5.TOTAL_CONNECTIONS": "Total Connections",
	"SOCKS5.ACTIVE_CONNECTIONS": "Active Connections",
	"SOCKS5.TOTAL_TRAFFIC": "Total Traffic",
	"SOCKS5.UPTIME": "Uptime",
	"SOCKS5.START_TIME": "Start Time",
	"SOCKS5.ENABLED": "Enabled",
	"SOCKS5.DISABLED": "Disabled",
	"SOCKS5.TUNNELS": "Tunnel List",
	"SOCKS5.ADD_TUNNEL": "Add Tunnel",
	"SOCKS5.REFRESH": "Refresh",
	"SOCKS5.DELETE": "Delete",
	"SOCKS5.CONNECT": "Connect",
	"SOCKS5.STATUS": {
		"PENDING": "Pending",
		"ACTIVE": "Connected",
		"ERROR": "Error"
	},
	"SOCKS5.FORM": {
		"DEVICE": "Device",
		"PORT": "Port",
		"USERNAME": "Username",
		"PASSWORD": "Password",
		"SELECT_DEVICE": "Select Device",
		"PORT_PLACEHOLDER": "Enter port number",
		"USERNAME_PLACEHOLDER": "Enter username",
		"PASSWORD_PLACEHOLDER": "Enter password"
	},
	"SOCKS5.TUNNEL_INFO": {
		"TITLE": "Tunnel Information",
		"ID": "ID",
		"DEVICE": "Device",
		"PORT": "Port",
		"USERNAME": "Username",
		"PASSWORD": "Password",
		"STATUS": "Status",
		"CREATE_TIME": "Create Time",
		"UPDATE_TIME": "Update Time",
		"ERROR": "Error Message",
		"PROXY_URL": "Proxy URL"
	},
	"SOCKS5.MESSAGES": {
		"CREATE_SUCCESS": "Tunnel created successfully",
		"CREATE_FAIL": "Failed to create tunnel",
		"DELETE_SUCCESS": "Tunnel deleted successfully",
		"DELETE_FAIL": "Failed to delete tunnel",
		"CONNECTION_ERROR": "Connection error"
	},

	"TUNNEL": {
		"TITLE": "Tunnel Management",
		"DESCRIPTION": "Manage TCP, HTTP and SOCKS5 tunnels for network penetration and proxy functionality",
		"TUNNEL_MANAGER": "Tunnel Manager",
		"ADD_TUNNEL": "Create Tunnel",
		"START": "Start",
		"STOP": "Stop",
		"STATUS": {
			"STOPPED": "Stopped",
			"STARTING": "Starting",
			"RUNNING": "Running",
			"ERROR": "Error"
		},
		"FORM": {
			"NAME": "Tunnel Name",
			"NAME_PLACEHOLDER": "Enter tunnel name",
			"DESCRIPTION": "Description",
			"DESCRIPTION_PLACEHOLDER": "Enter tunnel description",
			"TYPE": "Tunnel Type",
			"DEVICE": "Target Device",
			"REMOTE_PORT": "Server Port",
			"REMOTE_PORT_PLACEHOLDER": "Leave empty for auto allocation",
			"REMOTE_PORT_TOOLTIP": "Server listening port, leave empty for auto allocation",
			"LOCAL_HOST": "Local Host",
			"LOCAL_PORT": "Local Port",
			"LOCAL_PORT_PLACEHOLDER": "Enter local port",
			"LOCAL_TARGET": "Local Target",
			"AUTH_REQUIRED": "Enable Authentication",
			"USERNAME": "Username",
			"PASSWORD": "Password"
		},
		"CREATE_TIME": "Create Time",
		"ACCESS_URL": "Access URL",
		"NO_AUTH": "No Authentication",
		"STATS": {
			"TITLE": "Tunnel Statistics",
			"TOTAL_CONNECTIONS": "Total Connections",
			"BYTES_IN": "Bytes In",
			"BYTES_OUT": "Bytes Out",
			"LAST_CONNECTION": "Last Connection"
		},
		"MESSAGES": {
			"CREATE_SUCCESS": "Tunnel created successfully",
			"CREATE_FAIL": "Failed to create tunnel",
			"DELETE_SUCCESS": "Tunnel deleted successfully",
			"DELETE_FAIL": "Failed to delete tunnel",
			"START_SUCCESS": "Tunnel started successfully",
			"START_FAIL": "Failed to start tunnel",
			"STOP_SUCCESS": "Tunnel stopped successfully",
			"STOP_FAIL": "Failed to stop tunnel"
		}
	}
};