#!/bin/bash

# Spark 隧道功能演示脚本

echo "=== Spark 隧道功能演示 ==="

# 配置
SERVER_URL="http://localhost:8000"
API_BASE="$SERVER_URL/api"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查服务状态
check_service() {
    print_info "检查 Spark 服务状态..."
    
    if curl -s "$API_BASE/device/list" > /dev/null; then
        print_success "Spark 服务运行正常"
        return 0
    else
        print_error "Spark 服务未运行，请先启动服务"
        echo "启动命令: ./spark-server"
        return 1
    fi
}

# 获取设备列表
get_devices() {
    print_info "获取在线设备列表..."
    
    response=$(curl -s "$API_BASE/device/list")
    if echo "$response" | grep -q '"code":0'; then
        devices=$(echo "$response" | jq -r '.data.devices[]? | select(.online == true) | .id + " (" + .hostname + ")"' 2>/dev/null)
        if [ -n "$devices" ]; then
            print_success "发现在线设备:"
            echo "$devices" | while read device; do
                echo "  - $device"
            done
            echo "$response" | jq -r '.data.devices[]? | select(.online == true) | .id' | head -1
        else
            print_warning "没有在线设备"
            return 1
        fi
    else
        print_error "获取设备列表失败"
        return 1
    fi
}

# 创建演示隧道
create_demo_tunnel() {
    local device_id=$1
    local tunnel_name=$2
    local tunnel_type=$3
    local local_port=$4
    
    print_info "创建 $tunnel_type 隧道: $tunnel_name"
    
    tunnel_data=$(cat <<EOF
{
    "device_id": "$device_id",
    "name": "$tunnel_name",
    "description": "演示隧道 - 自动创建",
    "type": "$tunnel_type",
    "protocol": "tcp",
    "local_host": "localhost",
    "local_port": $local_port,
    "auto_start": true
}
EOF
)
    
    response=$(curl -s -X POST \
        -H "Content-Type: application/json" \
        -d "$tunnel_data" \
        "$API_BASE/tunnels")
    
    if echo "$response" | grep -q '"code":0'; then
        tunnel_id=$(echo "$response" | jq -r '.data.id' 2>/dev/null)
        remote_port=$(echo "$response" | jq -r '.data.remote_port' 2>/dev/null)
        access_url=$(echo "$response" | jq -r '.data.access_url' 2>/dev/null)
        
        print_success "隧道创建成功"
        echo "  隧道ID: $tunnel_id"
        echo "  远程端口: $remote_port"
        if [ "$access_url" != "null" ] && [ -n "$access_url" ]; then
            echo "  访问地址: $access_url"
        fi
        
        echo "$tunnel_id"
    else
        print_error "隧道创建失败"
        echo "响应: $response"
        return 1
    fi
}

# 显示隧道状态
show_tunnel_status() {
    local tunnel_id=$1
    
    print_info "查询隧道状态..."
    
    response=$(curl -s "$API_BASE/tunnels/$tunnel_id")
    if echo "$response" | grep -q '"code":0'; then
        name=$(echo "$response" | jq -r '.data.name' 2>/dev/null)
        status=$(echo "$response" | jq -r '.data.status' 2>/dev/null)
        type=$(echo "$response" | jq -r '.data.type' 2>/dev/null)
        remote_port=$(echo "$response" | jq -r '.data.remote_port' 2>/dev/null)
        
        print_success "隧道信息:"
        echo "  名称: $name"
        echo "  类型: $type"
        echo "  状态: $status"
        echo "  端口: $remote_port"
    else
        print_error "查询隧道状态失败"
    fi
}

# 删除隧道
delete_tunnel() {
    local tunnel_id=$1
    local tunnel_name=$2
    
    print_info "删除隧道: $tunnel_name"
    
    response=$(curl -s -X DELETE "$API_BASE/tunnels/$tunnel_id")
    if echo "$response" | grep -q '"code":0'; then
        print_success "隧道删除成功"
    else
        print_error "隧道删除失败"
    fi
}

# 演示端口统计
show_port_stats() {
    print_info "查看端口统计..."
    
    response=$(curl -s "$API_BASE/ports/stats")
    if echo "$response" | grep -q '"code":0'; then
        print_success "端口统计:"
        echo "$response" | jq '.data.stats' 2>/dev/null || echo "  (需要 jq 工具来格式化显示)"
    else
        print_error "获取端口统计失败"
    fi
}

# 演示可用端口
show_available_ports() {
    local type=$1
    
    print_info "查看 $type 类型的可用端口..."
    
    response=$(curl -s "$API_BASE/ports/available?type=$type&count=5")
    if echo "$response" | grep -q '"code":0'; then
        ports=$(echo "$response" | jq -r '.data.ports[]?' 2>/dev/null)
        if [ -n "$ports" ]; then
            print_success "可用端口:"
            echo "$ports" | while read port; do
                echo "  - $port"
            done
        else
            print_warning "没有可用端口"
        fi
    else
        print_error "获取可用端口失败"
    fi
}

# 主演示流程
main_demo() {
    echo
    print_info "开始 Spark 隧道功能演示..."
    echo
    
    # 检查服务
    if ! check_service; then
        exit 1
    fi
    echo
    
    # 获取设备
    device_id=$(get_devices)
    if [ -z "$device_id" ]; then
        print_error "没有可用设备，无法继续演示"
        exit 1
    fi
    echo
    
    # 显示端口统计
    show_port_stats
    echo
    
    # 显示可用端口
    show_available_ports "tcp"
    echo
    
    # 创建演示隧道
    print_info "创建演示隧道..."
    
    # TCP 隧道 (SSH)
    tunnel_id1=$(create_demo_tunnel "$device_id" "演示SSH隧道" "tcp" "22")
    if [ -n "$tunnel_id1" ]; then
        echo
        show_tunnel_status "$tunnel_id1"
        echo
        
        # 等待一下
        print_info "等待 3 秒..."
        sleep 3
        
        # 删除隧道
        delete_tunnel "$tunnel_id1" "演示SSH隧道"
    fi
    echo
    
    # 显示最终统计
    show_port_stats
    echo
    
    print_success "演示完成！"
    echo
    print_info "您可以通过以下方式使用隧道功能:"
    echo "1. 访问 Web 界面: $SERVER_URL/tunnels"
    echo "2. 使用 API 接口: $API_BASE/tunnels"
    echo "3. 查看文档: docs/TUNNEL_FEATURE.md"
}

# 显示帮助
show_help() {
    echo "Spark 隧道功能演示脚本"
    echo
    echo "用法: $0 [选项]"
    echo
    echo "选项:"
    echo "  demo     运行完整演示 (默认)"
    echo "  check    仅检查服务状态"
    echo "  devices  显示设备列表"
    echo "  ports    显示端口统计"
    echo "  help     显示帮助"
    echo
    echo "环境要求:"
    echo "  - Spark 服务运行在 localhost:8000"
    echo "  - 至少有一个在线设备"
    echo "  - 可选: jq 工具用于格式化 JSON 输出"
}

# 处理命令行参数
case "${1:-demo}" in
    "demo")
        main_demo
        ;;
    "check")
        check_service
        ;;
    "devices")
        get_devices > /dev/null
        ;;
    "ports")
        show_port_stats
        ;;
    "help")
        show_help
        ;;
    *)
        print_error "未知选项: $1"
        show_help
        exit 1
        ;;
esac
