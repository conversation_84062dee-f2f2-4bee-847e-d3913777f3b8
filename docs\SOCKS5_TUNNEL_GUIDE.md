# SOCKS5 隧道功能使用指南

## 功能概述

SOCKS5 隧道功能允许您通过 Spark 服务器创建 SOCKS5 代理，实现内网穿透和代理访问。通过连接到服务器的 SOCKS5 端口，您可以访问客户端内部网络的资源。

## 主要特性

- **通用代理协议**: 支持任意 TCP 连接
- **可选认证**: 支持用户名/密码认证
- **自动端口分配**: 服务器端口可自动分配
- **实时管理**: 支持隧道的启动、停止和删除
- **统计信息**: 提供连接数和流量统计

## 使用步骤

### 1. 创建 SOCKS5 隧道

在 Web 界面中：

1. 进入 "SOCKS5" 页面
2. 点击 "创建隧道" 按钮
3. 填写隧道信息：
   - **隧道名称**: 给隧道起一个有意义的名称
   - **描述**: 可选的隧道描述
   - **目标设备**: 选择要创建隧道的客户端设备
   - **服务器端口**: 留空自动分配，或指定端口
   - **启用认证**: 是否需要用户名/密码认证
   - **用户名/密码**: 如果启用认证，设置访问凭据

### 2. 启动隧道

创建隧道后：

1. 在隧道列表中找到您的隧道
2. 点击 "启动" 按钮
3. 等待状态变为 "运行中"

### 3. 使用 SOCKS5 代理

隧道启动后，您可以使用以下信息配置 SOCKS5 代理：

- **服务器地址**: Spark 服务器的 IP 地址
- **端口**: 隧道分配的端口号
- **认证**: 如果启用了认证，使用设置的用户名和密码

#### 代理 URL 格式

- 无认证: `socks5://服务器IP:端口`
- 有认证: `socks5://用户名:密码@服务器IP:端口`

### 4. 配置应用程序

将上述 SOCKS5 代理信息配置到您的应用程序中，例如：

- **浏览器**: 在网络设置中配置 SOCKS5 代理
- **命令行工具**: 使用 `--proxy` 参数
- **编程语言**: 使用相应的 SOCKS5 客户端库

## API 接口

### 创建隧道

```bash
POST /api/tunnels
Content-Type: application/json

{
  "name": "My SOCKS5 Tunnel",
  "description": "用于访问内网资源",
  "type": "socks5",
  "device_id": "device-uuid",
  "remote_port": 0,
  "auth_required": true,
  "username": "myuser",
  "password": "mypass123"
}
```

### 启动隧道

```bash
POST /api/tunnels/{tunnel_id}/start
```

### 停止隧道

```bash
POST /api/tunnels/{tunnel_id}/stop
```

### 删除隧道

```bash
DELETE /api/tunnels/{tunnel_id}
```

### 获取隧道列表

```bash
GET /api/tunnels
```

## 测试和验证

### 使用测试脚本

运行提供的测试脚本来验证功能：

```bash
chmod +x scripts/test_socks5_tunnel.sh
./scripts/test_socks5_tunnel.sh
```

### 手动测试

1. 创建并启动一个 SOCKS5 隧道
2. 使用 curl 测试代理连接：

```bash
# 无认证
curl --socks5 服务器IP:端口 http://httpbin.org/ip

# 有认证
curl --socks5 用户名:密码@服务器IP:端口 http://httpbin.org/ip
```

## 故障排除

### 常见问题

1. **隧道启动失败**
   - 检查客户端是否在线
   - 确认端口未被占用
   - 查看服务器日志

2. **连接被拒绝**
   - 验证认证信息是否正确
   - 检查防火墙设置
   - 确认隧道状态为 "运行中"

3. **数据传输失败**
   - 检查客户端网络连接
   - 验证目标服务是否可访问
   - 查看隧道统计信息

### 日志查看

- **服务器日志**: 查看 Spark 服务器的控制台输出
- **客户端日志**: 查看客户端的连接状态
- **数据库日志**: 检查隧道配置是否正确保存

## 安全注意事项

1. **认证**: 建议为 SOCKS5 隧道启用用户名/密码认证
2. **端口管理**: 避免使用已知的系统端口
3. **访问控制**: 限制可访问隧道的 IP 地址范围
4. **监控**: 定期检查隧道使用情况和异常连接

## 性能优化

1. **连接池**: 客户端使用连接池减少连接开销
2. **缓冲区**: 适当调整数据缓冲区大小
3. **超时设置**: 合理设置连接和读写超时
4. **资源清理**: 及时关闭不使用的隧道

## 更新和维护

- 定期更新 Spark 到最新版本
- 监控隧道使用情况和性能指标
- 清理不再使用的隧道配置
- 备份重要的隧道配置数据
