package common

import (
	"database/sql"
	"fmt"
)

// GetSocks5TunnelByID 根据ID获取SOCKS5隧道
func GetSocks5TunnelByID(id int) (*Socks5Tunnel, error) {
	if DB == nil {
		return nil, fmt.Errorf("数据库未初始化")
	}

	var tunnel Socks5Tunnel
	result := DB.QueryRow("SELECT id, device_id, port, username, password, status, create_time, update_time, error FROM socks5_tunnels WHERE id = ?", id)

	err := result.Scan(&tunnel.ID, &tunnel.DeviceID, &tunnel.Port, &tunnel.Username, &tunnel.Password, &tunnel.Status, &tunnel.CreateTime, &tunnel.UpdateTime, &tunnel.Error)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil // 没有找到记录，返回nil
		}
		return nil, err
	}

	return &tunnel, nil
}