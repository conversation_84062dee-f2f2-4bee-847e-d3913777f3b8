package core

import (
	"Spark/client/common"
	"Spark/client/config"
	"Spark/modules"
	"Spark/utils"
	"encoding/hex"
	"errors"
	"net/http"
	"os"
	"os/exec"
	"runtime"
	"strings"
	"time"

	ws "github.com/gorilla/websocket"
	"github.com/kataras/golog"
	"Spark/client/service/terminal"
	"Spark/client/service/process"
	"Spark/client/service/desktop"
	"Spark/client/service/file"
	"Spark/client/service/screenshot"
	"Spark/client/service/tunnel"
)

// simplified type of map
type smap map[string]any

var stop bool
var initialized bool
var connecting bool
var connected bool
var webSocket *common.Conn
var (
	errNoSecretHeader = errors.New(`can not find secret header`)
)

// Init initializes the client core.
func Init(token string) error {
	if initialized {
		return nil
	}
	initialized = true

	config.Init(token)

	// 使用 Start() 函数来启动客户端
	go Start()

	return nil
}

// Cleanup cleans up resources
func Cleanup() {
	if webSocket != nil {
		webSocket.Close()
	}
	
	// 清理各种服务资源
	cleanupServices()
	
	initialized = false
}

func Start() {
	StartWithRetryCallback(nil)
}

// StartWithRetryCallback 带重试回调的启动函数
func StartWithRetryCallback(retryCallback func() bool) error {
	// 初始化隧道管理器
	// tunnel.InitClientTunnelManager() 将在连接建立后调用

	for !stop {
		var err error
		if common.WSConn != nil {
			common.Mutex.Lock()
			common.WSConn.Close()
			common.Mutex.Unlock()
		}
		common.Mutex.Lock()
		common.WSConn, err = connectWS()
		common.Mutex.Unlock()
		if err != nil && !stop {
			golog.Error(`Connection error: `, err)

			// 如果有重试回调，调用它来决定是否继续重试
			if retryCallback != nil {
				if !retryCallback() {
					return err // 停止重试
				}
				continue
			}

			<-time.After(3 * time.Second)
			continue
		}

		// 初始化各种服务
		initializeServices(common.WSConn)

		err = reportWS(common.WSConn)
		if err != nil && !stop {
			golog.Error(`Register error: `, err)

			// 如果有重试回调，调用它来决定是否继续重试
			if retryCallback != nil {
				if !retryCallback() {
					return err // 停止重试
				}
				continue
			}

			<-time.After(3 * time.Second)
			continue
		}

		checkUpdate(common.WSConn)

		err = handleWS(common.WSConn)
		if err != nil && !stop {
			golog.Error(`Execution error: `, err)

			// 如果有重试回调，调用它来决定是否继续重试
			if retryCallback != nil {
				if !retryCallback() {
					return err // 停止重试
				}
				continue
			}

			<-time.After(3 * time.Second)
			continue
		}
	}

	return nil
}

func connectWS() (*common.Conn, error) {
	wsConn, wsResp, err := ws.DefaultDialer.Dial(config.GetBaseURL(true)+`/ws`, http.Header{
		`UUID`: []string{config.Config.UUID},
		`Key`:  []string{config.Config.Key},
	})
	if err != nil {
		return nil, err
	}
	header, find := wsResp.Header[`Secret`]
	if !find || len(header) == 0 {
		return nil, errNoSecretHeader
	}
	secret, err := hex.DecodeString(header[0])
	if err != nil {
		return nil, err
	}
	return common.CreateConn(wsConn, secret), nil
}

func reportWS(wsConn *common.Conn) error {
	device, err := GetDevice()
	if err != nil {
		return err
	}
	pack := modules.CommonPack{Act: `DEVICE_UP`, Data: *device}
	err = wsConn.SendPack(pack)
	common.WSConn.SetWriteDeadline(time.Time{})
	if err != nil {
		return err
	}
	common.WSConn.SetReadDeadline(utils.Now.Add(5 * time.Second))
	_, data, err := common.WSConn.ReadMessage()
	common.WSConn.SetReadDeadline(time.Time{})
	if err != nil {
		return err
	}
	data, err = utils.Decrypt(data, common.WSConn.GetSecret())
	if err != nil {
		return err
	}
	err = utils.JSON.Unmarshal(data, &pack)
	if err != nil {
		return err
	}
	if pack.Code != 0 {
		return errors.New(`${i18n|COMMON.UNKNOWN_ERROR}`)
	}
	return nil
}

func checkUpdate(wsConn *common.Conn) error {
	if len(config.Commit) == 0 {
		return nil
	}
	resp, err := common.HTTP.R().
		SetBody(config.ConfigBuffer).
		SetQueryParam(`os`, runtime.GOOS).
		SetQueryParam(`arch`, runtime.GOARCH).
		SetQueryParam(`commit`, config.Commit).
		SetHeader(`Secret`, wsConn.GetSecretHex()).
		Send(`POST`, config.GetBaseURL(false)+`/api/client/update`)
	if err != nil {
		return err
	}
	if resp == nil {
		return errors.New(`${i18n|COMMON.UNKNOWN_ERROR}`)
	}
	if strings.HasPrefix(resp.GetContentType(), `application/octet-stream`) {
		body := resp.Bytes()
		if len(body) > 0 {
			selfPath, err := os.Executable()
			if err != nil {
				selfPath = os.Args[0]
			}
			err = os.WriteFile(selfPath+`.tmp`, body, 0755)
			if err != nil {
				return err
			}
			cmd := exec.Command(selfPath+`.tmp`, `--update`)
			err = cmd.Start()
			if err != nil {
				return err
			}
			stop = true
			wsConn.Close()
			os.Exit(0)
		}
		return nil
	}
	return nil
}

func handleWS(wsConn *common.Conn) error {
	errCount := 0
	for {
		_, data, err := wsConn.ReadMessage()
		if err != nil {
			golog.Error(err)
			return nil
		}
		if service, op, isBinary := utils.CheckBinaryPack(data); isBinary && len(data) > 24 {
			event := hex.EncodeToString(data[6:22])
			switch service {
			case 20:
			case 21:
				switch op {
				case 0:
					inputRawTerminal(data[24:], event)
				}
			}
			continue
		}
		data, err = utils.Decrypt(data, wsConn.GetSecret())
		if err != nil {
			golog.Error(err)
			errCount++
			if errCount > 3 {
				break
			}
			continue
		}
		pack := modules.Packet{}
		utils.JSON.Unmarshal(data, &pack)
		if err != nil {
			golog.Error(err)
			errCount++
			if errCount > 3 {
				break
			}
			continue
		}
		errCount = 0
		if pack.Data == nil {
			pack.Data = smap{}
		}
		
		// 处理隧道相关事件（由新的隧道管理器处理）
		if strings.HasPrefix(pack.Act, "TUNNEL_") || strings.HasPrefix(pack.Act, "SOCKS5_TUNNEL_") {
			tunnel.HandleTunnelEvent(pack, wsConn)
			continue
		}
		
		go handleAct(pack, wsConn)
	}
	wsConn.Close()
	return nil
}

func handleAct(pack modules.Packet, wsConn *common.Conn) {
	if act, ok := handlers[pack.Act]; !ok {
		wsConn.SendCallback(modules.Packet{Code: 1, Msg: `${i18n|COMMON.OPERATION_NOT_SUPPORTED}`}, pack)
	} else {
		defer func() {
			if r := recover(); r != nil {
				golog.Error(`Panic: `, r)
			}
		}()
		act(pack, wsConn)
	}
}

// 注册各种服务
func registerServices() {
	// 服务将在连接建立后初始化
	// 这里只做基本的初始化
}

// 初始化各种服务（连接建立后）
func initializeServices(wsConn *common.Conn) {
	terminal.Init(wsConn)
	process.Init(wsConn)
	desktop.Init(wsConn)
	file.Init(wsConn)
	screenshot.Init(wsConn)
	tunnel.InitClientTunnelManager(wsConn)
}

// 清理各种服务资源
func cleanupServices() {
	terminal.Cleanup()
	process.Cleanup()
	desktop.Cleanup()
	file.Cleanup()
	screenshot.Cleanup()
	// tunnel 管理器不需要特殊清理
}

// inputRawTerminal 处理原始终端输入
func inputRawTerminal(data []byte, event string) {
	// 终端原始输入处理
	// TODO: 实现终端原始输入处理逻辑
	golog.Debugf("收到终端原始输入 [Event=%s, DataLen=%d]", event, len(data))
}
