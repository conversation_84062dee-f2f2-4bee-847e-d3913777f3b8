import React, { useState, useEffect } from 'react';
import { 
    Drawer, Descriptions, Tag, Card, Row, Col, 
    Statistic, Button, Space, Timeline, Table,
    Progress, Alert
} from 'antd';
import { 
    PlayCircleOutlined, PauseCircleOutlined,
    ReloadOutlined, LinkOutlined, CopyOutlined
} from '@ant-design/icons';
import { message } from 'antd';
import { request } from '../../../utils/request';

const TunnelDetailDrawer = ({ visible, tunnel, onClose }) => {
    const [loading, setLoading] = useState(false);
    const [sessions, setSessions] = useState([]);
    const [logs, setLogs] = useState([]);

    useEffect(() => {
        if (visible && tunnel) {
            loadTunnelSessions();
            loadTunnelLogs();
        }
    }, [visible, tunnel]);

    // 加载隧道会话
    const loadTunnelSessions = async () => {
        if (!tunnel) return;
        try {
            const response = await request.get(`/api/tunnels/${tunnel.id}/sessions`);
            if (response.code === 0) {
                setSessions(response.data.sessions || []);
            }
        } catch (error) {
            console.error('加载会话失败:', error);
        }
    };

    // 加载隧道日志
    const loadTunnelLogs = async () => {
        if (!tunnel) return;
        try {
            const response = await request.get(`/api/tunnels/${tunnel.id}/logs`);
            if (response.code === 0) {
                setLogs(response.data.logs || []);
            }
        } catch (error) {
            console.error('加载日志失败:', error);
        }
    };

    // 复制访问地址
    const copyAccessURL = () => {
        if (tunnel?.access_url) {
            navigator.clipboard.writeText(tunnel.access_url);
            message.success('访问地址已复制到剪贴板');
        }
    };

    // 获取状态颜色
    const getStatusColor = (status) => {
        switch (status) {
            case 'running': return 'green';
            case 'stopped': return 'default';
            case 'starting': return 'blue';
            case 'error': return 'red';
            default: return 'default';
        }
    };

    // 获取状态文本
    const getStatusText = (status) => {
        switch (status) {
            case 'running': return '运行中';
            case 'stopped': return '已停止';
            case 'starting': return '启动中';
            case 'error': return '错误';
            default: return '未知';
        }
    };

    // 格式化字节数
    const formatBytes = (bytes) => {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    };

    // 格式化时间
    const formatTime = (timestamp) => {
        return new Date(timestamp * 1000).toLocaleString();
    };

    // 会话表格列
    const sessionColumns = [
        {
            title: '客户端IP',
            dataIndex: 'client_ip',
            key: 'client_ip',
        },
        {
            title: '开始时间',
            dataIndex: 'start_time',
            key: 'start_time',
            render: (time) => formatTime(time),
        },
        {
            title: '入流量',
            dataIndex: 'bytes_in',
            key: 'bytes_in',
            render: (bytes) => formatBytes(bytes),
        },
        {
            title: '出流量',
            dataIndex: 'bytes_out',
            key: 'bytes_out',
            render: (bytes) => formatBytes(bytes),
        },
        {
            title: '最后活动',
            dataIndex: 'last_activity',
            key: 'last_activity',
            render: (time) => formatTime(time),
        },
    ];

    if (!tunnel) return null;

    return (
        <Drawer
            title={`隧道详情 - ${tunnel.name}`}
            placement="right"
            width={800}
            open={visible}
            onClose={onClose}
            extra={
                <Space>
                    <Button 
                        icon={<ReloadOutlined />}
                        onClick={() => {
                            loadTunnelSessions();
                            loadTunnelLogs();
                        }}
                    >
                        刷新
                    </Button>
                </Space>
            }
        >
            {/* 基本信息 */}
            <Card title="基本信息" style={{ marginBottom: 16 }}>
                <Descriptions column={2}>
                    <Descriptions.Item label="隧道ID">
                        {tunnel.id}
                    </Descriptions.Item>
                    <Descriptions.Item label="状态">
                        <Tag color={getStatusColor(tunnel.status)}>
                            {getStatusText(tunnel.status)}
                        </Tag>
                    </Descriptions.Item>
                    <Descriptions.Item label="类型">
                        <Tag color="blue">{(tunnel.type || '').toUpperCase() || 'UNKNOWN'}</Tag>
                    </Descriptions.Item>
                    <Descriptions.Item label="协议">
                        {tunnel.protocol || 'tcp'}
                    </Descriptions.Item>
                    <Descriptions.Item label="设备ID">
                        {tunnel.device_id}
                    </Descriptions.Item>
                    <Descriptions.Item label="分组">
                        {tunnel.group_id || 'default'}
                    </Descriptions.Item>
                    <Descriptions.Item label="本地地址">
                        {tunnel.local_host}:{tunnel.local_port}
                    </Descriptions.Item>
                    <Descriptions.Item label="远程端口">
                        {tunnel.remote_port}
                    </Descriptions.Item>
                    {tunnel.subdomain && (
                        <Descriptions.Item label="子域名">
                            {tunnel.subdomain}
                        </Descriptions.Item>
                    )}
                    {tunnel.custom_domain && (
                        <Descriptions.Item label="自定义域名">
                            {tunnel.custom_domain}
                        </Descriptions.Item>
                    )}
                    <Descriptions.Item label="创建时间">
                        {formatTime(tunnel.create_time)}
                    </Descriptions.Item>
                    <Descriptions.Item label="更新时间">
                        {formatTime(tunnel.update_time)}
                    </Descriptions.Item>
                </Descriptions>

                {tunnel.description && (
                    <div style={{ marginTop: 16 }}>
                        <strong>描述：</strong>
                        <p>{tunnel.description}</p>
                    </div>
                )}
            </Card>

            {/* 访问信息 */}
            {tunnel.access_url && (
                <Card title="访问信息" style={{ marginBottom: 16 }}>
                    <Alert
                        message="访问地址"
                        description={
                            <Space>
                                <span>{tunnel.access_url}</span>
                                <Button 
                                    icon={<CopyOutlined />}
                                    size="small"
                                    onClick={copyAccessURL}
                                >
                                    复制
                                </Button>
                                <Button 
                                    icon={<LinkOutlined />}
                                    size="small"
                                    onClick={() => window.open(tunnel.access_url, '_blank')}
                                >
                                    打开
                                </Button>
                            </Space>
                        }
                        type="info"
                    />
                </Card>
            )}

            {/* 统计信息 */}
            <Card title="统计信息" style={{ marginBottom: 16 }}>
                <Row gutter={16}>
                    <Col span={6}>
                        <Statistic 
                            title="总连接数" 
                            value={tunnel.stats?.total_connections || 0} 
                        />
                    </Col>
                    <Col span={6}>
                        <Statistic 
                            title="活跃连接" 
                            value={tunnel.stats?.active_connections || 0}
                            valueStyle={{ color: '#1890ff' }}
                        />
                    </Col>
                    <Col span={6}>
                        <Statistic 
                            title="入流量" 
                            value={formatBytes(tunnel.stats?.total_bytes_in || 0)}
                            valueStyle={{ color: '#3f8600' }}
                        />
                    </Col>
                    <Col span={6}>
                        <Statistic 
                            title="出流量" 
                            value={formatBytes(tunnel.stats?.total_bytes_out || 0)}
                            valueStyle={{ color: '#cf1322' }}
                        />
                    </Col>
                </Row>

                {tunnel.stats?.uptime && (
                    <div style={{ marginTop: 16 }}>
                        <Statistic 
                            title="运行时间" 
                            value={Math.floor(tunnel.stats.uptime / 3600)}
                            suffix="小时"
                        />
                    </div>
                )}
            </Card>

            {/* 活跃会话 */}
            <Card title="活跃会话" style={{ marginBottom: 16 }}>
                <Table
                    columns={sessionColumns}
                    dataSource={sessions}
                    rowKey="session_id"
                    size="small"
                    pagination={false}
                    locale={{ emptyText: '暂无活跃会话' }}
                />
            </Card>

            {/* 操作日志 */}
            <Card title="操作日志">
                <Timeline>
                    {logs.length > 0 ? logs.map((log, index) => (
                        <Timeline.Item key={index} color={log.level === 'error' ? 'red' : 'blue'}>
                            <div>
                                <strong>{formatTime(log.timestamp)}</strong>
                                <br />
                                {log.message}
                            </div>
                        </Timeline.Item>
                    )) : (
                        <Timeline.Item>
                            暂无日志记录
                        </Timeline.Item>
                    )}
                </Timeline>
            </Card>
        </Drawer>
    );
};

export default TunnelDetailDrawer;
