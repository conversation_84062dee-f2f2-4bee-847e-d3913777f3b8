import React, { useState, useEffect } from 'react';
import { 
    Table, Button, Space, Tag, Modal, message, 
    Drawer, Card, Statistic, Row, Col, Tooltip,
    Input, Select, Popconfirm
} from 'antd';
import { 
    PlusOutlined, PlayCircleOutlined, PauseCircleOutlined,
    DeleteOutlined, EditOutlined, EyeOutlined, ReloadOutlined,
    SearchOutlined, SettingOutlined
} from '@ant-design/icons';
import TunnelCreateForm from './components/TunnelCreateForm';
import TunnelDetailDrawer from './components/TunnelDetailDrawer';
import TunnelStatsCard from './components/TunnelStatsCard';
import { request } from '../../utils/request';

const { Search } = Input;
const { Option } = Select;

const TunnelManager = ({ deviceFilter = null }) => {
    const [tunnels, setTunnels] = useState([]);
    const [loading, setLoading] = useState(false);
    const [createModalVisible, setCreateModalVisible] = useState(false);
    const [detailDrawerVisible, setDetailDrawerVisible] = useState(false);
    const [selectedTunnel, setSelectedTunnel] = useState(null);
    const [globalStats, setGlobalStats] = useState({});
    const [searchText, setSearchText] = useState('');
    const [statusFilter, setStatusFilter] = useState('all');
    const [typeFilter, setTypeFilter] = useState('all');
    const [lastUpdateTime, setLastUpdateTime] = useState(null);

    useEffect(() => {
        loadTunnels();
        loadGlobalStats();
        // 定时刷新 - 更频繁的刷新以显示实时状态
        const interval = setInterval(() => {
            loadTunnels();
            loadGlobalStats();
        }, 5000); // 每5秒刷新一次
        return () => clearInterval(interval);
    }, []);

    // 加载隧道列表
    const loadTunnels = async () => {
        setLoading(true);
        try {
            const response = await request.get('/api/tunnels');
            if (response.code === 200) {
                setTunnels(response.data.list || []);
                setLastUpdateTime(new Date());
            } else {
                message.error('加载隧道列表失败: ' + response.message);
            }
        } catch (error) {
            message.error('加载隧道列表失败: ' + error.message);
        } finally {
            setLoading(false);
        }
    };

    // 加载全局统计 (简化版本)
    const loadGlobalStats = async () => {
        // 简化统计，直接从隧道列表计算
        setGlobalStats({});
    };



    // 切换隧道状态
    const handleToggleTunnel = async (tunnelId) => {
        try {
            const response = await request.post(`/api/tunnels/${tunnelId}/toggle`);
            if (response.code === 200) {
                message.success('隧道状态切换成功');
                loadTunnels();
            } else {
                message.error('操作失败: ' + response.message);
            }
        } catch (error) {
            message.error('操作失败: ' + error.message);
        }
    };

    // 删除隧道
    const handleDeleteTunnel = async (tunnelId) => {
        try {
            const response = await request.delete(`/api/tunnels/${tunnelId}`);
            if (response.code === 200) {
                message.success('隧道删除成功');
                loadTunnels();
            } else {
                message.error('删除失败: ' + response.message);
            }
        } catch (error) {
            message.error('删除失败: ' + error.message);
        }
    };

    // 编辑隧道
    const handleEditTunnel = (tunnel) => {
        setSelectedTunnel(tunnel);
        setCreateModalVisible(true);
    };

    // 查看详情
    const handleViewDetail = (tunnel) => {
        setSelectedTunnel(tunnel);
        setDetailDrawerVisible(true);
    };

    // 获取状态颜色
    const getStatusColor = (status) => {
        switch (status) {
            case 'active': return 'green';
            case 'inactive': return 'default';
            case 'error': return 'red';
            default: return 'default';
        }
    };

    // 获取状态文本
    const getStatusText = (status) => {
        switch (status) {
            case 'active': return '活跃';
            case 'inactive': return '非活跃';
            case 'error': return '错误';
            default: return '未知';
        }
    };

    // 获取类型颜色
    const getTypeColor = (type) => {
        switch (type) {
            case 'tcp': return 'blue';
            case 'http': return 'green';
            case 'socks5': return 'orange';
            default: return 'default';
        }
    };

    // 简化：移除字节格式化功能

    // 过滤隧道
    const filteredTunnels = tunnels.filter(tunnel => {
        // 如果指定了设备过滤，只显示该设备的隧道
        if (deviceFilter && tunnel.device_uuid !== deviceFilter.uuid) {
            return false;
        }

        const matchSearch = !searchText ||
            tunnel.tunnel_name.toLowerCase().includes(searchText.toLowerCase()) ||
            tunnel.device_uuid.toLowerCase().includes(searchText.toLowerCase()) ||
            tunnel.device_hostname.toLowerCase().includes(searchText.toLowerCase());
        const matchStatus = statusFilter === 'all' || tunnel.status === statusFilter;
        const matchType = typeFilter === 'all' || tunnel.tunnel_type === typeFilter;
        return matchSearch && matchStatus && matchType;
    });

    // 表格列定义
    const columns = [
        {
            title: '配置名',
            dataIndex: 'tunnel_name',
            key: 'tunnel_name',
            width: 120,
        },
        {
            title: '设备名称',
            key: 'device_name',
            width: 120,
            render: (_, record) => (
                <span>{record.device_hostname || record.device_uuid}</span>
            ),
        },
        {
            title: '端口',
            dataIndex: 'server_port',
            key: 'server_port',
            width: 80,
        },
        {
            title: '类型',
            dataIndex: 'tunnel_type',
            key: 'tunnel_type',
            width: 80,
            render: (type) => (
                <Tag color={getTypeColor(type)}>
                    {(type || '').toUpperCase() || 'UNKNOWN'}
                </Tag>
            ),
        },
        {
            title: '服务端口',
            dataIndex: 'server_port',
            key: 'server_port_display',
            width: 100,
        },
        {
            title: '用户名',
            dataIndex: 'username',
            key: 'username',
            width: 100,
            render: (username) => username || 'abc',
        },
        {
            title: '密码',
            dataIndex: 'password',
            key: 'password',
            width: 100,
            render: (password) => password || 'abc',
        },
        {
            title: '隧道状态',
            key: 'tunnel_status',
            width: 100,
            render: (_, record) => (
                <Tag color={record.enabled ? 'green' : 'default'}>
                    {record.enabled ? '启用' : '禁用'}
                </Tag>
            ),
        },
        {
            title: '客户端状态',
            key: 'device_status',
            width: 120,
            render: (_, record) => (
                <Tag color={record.device_status === 'online' ? 'green' : 'red'}>
                    {record.device_status === 'online' ? '在线' : '离线'}
                </Tag>
            ),
        },
        {
            title: '操作',
            key: 'actions',
            width: 200,
            fixed: 'right',
            render: (_, record) => (
                <Space>
                    <Button
                        type={record.enabled ? 'warning' : 'primary'}
                        size="small"
                        onClick={() => handleToggleTunnel(record.tunnel_id)}
                    >
                        {record.enabled ? '停用' : '启用'}
                    </Button>
                    <Button
                        type="primary"
                        size="small"
                        onClick={() => handleEditTunnel(record)}
                        icon={<EditOutlined />}
                    >
                        编辑
                    </Button>
                    <Popconfirm
                        title="确定要删除这个隧道吗？"
                        onConfirm={() => handleDeleteTunnel(record.tunnel_id)}
                        okText="确定"
                        cancelText="取消"
                    >
                        <Button
                            type="primary"
                            danger
                            size="small"
                            icon={<DeleteOutlined />}
                        >
                            删除
                        </Button>
                    </Popconfirm>
                </Space>
            ),
        },
    ];

    return (
        <div className="tunnel-manager">
            {/* 页面标题 */}
            {deviceFilter && (
                <div style={{ marginBottom: 16 }}>
                    <h2 style={{ margin: 0, fontSize: '20px', fontWeight: 600 }}>
                        设备 {deviceFilter.hostname} 的隧道
                    </h2>
                </div>
            )}

            {/* 统计卡片 */}
            <Row gutter={16} style={{ marginBottom: 16 }}>
                <Col span={6}>
                    <Card>
                        <Statistic 
                            title="总隧道数" 
                            value={tunnels.length} 
                        />
                    </Card>
                </Col>
                <Col span={6}>
                    <Card>
                        <Statistic
                            title="自动启动"
                            value={tunnels.filter(t => t.enabled).length}
                            valueStyle={{ color: '#3f8600' }}
                        />
                    </Card>
                </Col>
                <Col span={6}>
                    <Card>
                        <Statistic
                            title="活跃隧道"
                            value={tunnels.filter(t => t.status === 'active').length}
                            valueStyle={{ color: '#1890ff' }}
                        />
                    </Card>
                </Col>
                <Col span={6}>
                    <Card>
                        <Statistic
                            title="在线设备"
                            value={new Set(tunnels.filter(t => t.device_status === 'online').map(t => t.device_uuid)).size}
                        />
                    </Card>
                </Col>
            </Row>

            {/* 操作栏 */}
            <div style={{ marginBottom: 16 }}>
                <Row justify="space-between" align="middle">
                    <Col>
                        <Space>
                            <Button 
                                type="primary" 
                                icon={<PlusOutlined />}
                                onClick={() => setCreateModalVisible(true)}
                            >
                                创建隧道
                            </Button>
                            <Button
                                icon={<ReloadOutlined />}
                                onClick={loadTunnels}
                                loading={loading}
                            >
                                刷新
                            </Button>
                            {lastUpdateTime && (
                                <span style={{ color: '#666', fontSize: '12px' }}>
                                    最后更新: {lastUpdateTime.toLocaleTimeString()}
                                </span>
                            )}
                        </Space>
                    </Col>
                    <Col>
                        <Space>
                            <Search
                                placeholder="搜索隧道名称或设备ID"
                                value={searchText}
                                onChange={(e) => setSearchText(e.target.value)}
                                style={{ width: 200 }}
                            />
                            <Select
                                value={statusFilter}
                                onChange={setStatusFilter}
                                style={{ width: 100 }}
                            >
                                <Option value="all">全部状态</Option>
                                <Option value="active">活跃</Option>
                                <Option value="inactive">非活跃</Option>
                                <Option value="error">错误</Option>
                            </Select>
                            <Select
                                value={typeFilter}
                                onChange={setTypeFilter}
                                style={{ width: 100 }}
                            >
                                <Option value="all">全部类型</Option>
                                <Option value="tcp">TCP</Option>
                                <Option value="http">HTTP</Option>
                                <Option value="socks5">SOCKS5</Option>
                            </Select>
                        </Space>
                    </Col>
                </Row>
            </div>

            {/* 隧道列表 */}
            <Table
                columns={columns}
                dataSource={filteredTunnels}
                loading={loading}
                rowKey="id"
                pagination={{
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total) => `共 ${total} 条记录`,
                }}
            />

            {/* 创建隧道模态框 */}
            <Modal
                title={selectedTunnel ? "编辑隧道" : "创建隧道"}
                open={createModalVisible}
                onCancel={() => {
                    setCreateModalVisible(false);
                    setSelectedTunnel(null);
                }}
                footer={null}
                width={800}
            >
                <TunnelCreateForm
                    tunnel={selectedTunnel}
                    deviceFilter={deviceFilter}
                    onSuccess={() => {
                        setCreateModalVisible(false);
                        setSelectedTunnel(null);
                        loadTunnels();
                    }}
                    onCancel={() => {
                        setCreateModalVisible(false);
                        setSelectedTunnel(null);
                    }}
                />
            </Modal>

            {/* 隧道详情抽屉 */}
            <TunnelDetailDrawer
                visible={detailDrawerVisible}
                tunnel={selectedTunnel}
                onClose={() => {
                    setDetailDrawerVisible(false);
                    setSelectedTunnel(null);
                }}
            />
        </div>
    );
};

export default TunnelManager;
