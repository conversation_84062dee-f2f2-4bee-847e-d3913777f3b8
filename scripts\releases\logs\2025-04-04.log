[FTAL] 2025/04/04 13:14:24 {"event":"DATABASE_INIT","msg":"测试数据库连接失败: Binary was compiled with 'CGO_ENABLED=0', go-sqlite3 requires cgo to work. This is a stub","status":"fail"}
[FTAL] 2025/04/04 13:15:37 {"event":"DATABASE_INIT","msg":"测试数据库连接失败: Binary was compiled with 'CGO_ENABLED=0', go-sqlite3 requires cgo to work. This is a stub","status":"fail"}
[INFO] 2025/04/04 13:18:43 数据库初始化成功: ./data/spark.db
[INFO] 2025/04/04 13:18:43 {"event":"SERVICE_INIT","listen":":8000"}
[WARN] 2025/04/04 13:19:36 {"event":"LOGIN_ATTEMPT","from":"127.0.0.1","status":"fail","user":"<EMPTY>"}
[WARN] 2025/04/04 13:19:49 {"event":"LOGIN_ATTEMPT","from":"127.0.0.1","status":"success","user":"username"}
[INFO] 2025/04/04 13:20:15 {"device":{"ip":"127.0.0.1","name":"DESKTOP-L1R864K"},"event":"CLIENT_ONLINE"}
[INFO] 2025/04/04 13:20:15 {"client":{"arch":"amd64","commit":"05957eef0f9aa580df5712f417177d866444f1b2","os":"windows"},"event":"CLIENT_UPDATE","from":"127.0.0.1","msg":"updating","server":"","status":"success"}
[INFO] 2025/04/04 13:20:15 {"event":"CLIENT_DISCONNECT","ip":"127.0.0.1"}
[INFO] 2025/04/04 13:20:15 {"device":{"ip":"127.0.0.1","name":"DESKTOP-L1R864K"},"event":"CLIENT_OFFLINE"}
[INFO] 2025/04/04 13:20:23 {"device":{"ip":"127.0.0.1","name":"DESKTOP-L1R864K"},"event":"CLIENT_ONLINE"}
[WARN] 2025/04/04 13:20:36 {"event":"SERVICE_EXITING"}
[WARN] 2025/04/04 13:20:39 {"event":"SERVICE_EXIT","status":"success"}
[INFO] 2025/04/04 13:21:23 数据库初始化成功: ./data/spark.db
[INFO] 2025/04/04 13:21:23 {"event":"SERVICE_INIT","listen":":8000"}
[INFO] 2025/04/04 13:21:28 {"device":{"ip":"127.0.0.1","name":"DESKTOP-L1R864K"},"event":"CLIENT_ONLINE"}
[WARN] 2025/04/04 13:21:40 {"event":"LOGIN_ATTEMPT","from":"127.0.0.1","status":"fail","user":"<EMPTY>"}
[WARN] 2025/04/04 13:21:47 {"event":"LOGIN_ATTEMPT","from":"127.0.0.1","status":"success","user":"username"}
[INFO] 2025/04/04 13:23:25 {"event":"READ_TEXT_FILE","file":"D:\\1.py","from":"127.0.0.1","status":"success","target":{"ip":"127.0.0.1","name":"DESKTOP-L1R864K"}}
[WARN] 2025/04/04 13:58:50 {"event":"LOGIN_ATTEMPT","from":"127.0.0.1","status":"success","user":"username"}
[WARN] 2025/04/04 14:30:11 {"event":"LOGIN_ATTEMPT","from":"127.0.0.1","status":"success","user":"username"}
[INFO] 2025/04/04 14:30:28 {"event":"CLIENT_DISCONNECT","ip":"127.0.0.1"}
[INFO] 2025/04/04 14:30:28 {"device":{"ip":"127.0.0.1","name":"DESKTOP-L1R864K"},"event":"CLIENT_OFFLINE"}
[WARN] 2025/04/04 14:30:37 {"event":"SERVICE_EXITING"}
[WARN] 2025/04/04 14:30:40 {"event":"SERVICE_EXIT","status":"success"}
[INFO] 2025/04/04 14:56:33 数据库初始化成功: ./data/spark.db
[INFO] 2025/04/04 14:56:33 {"event":"SERVICE_INIT","listen":":8000"}
[WARN] 2025/04/04 14:56:33 {"event":"LOGIN_ATTEMPT","from":"127.0.0.1","status":"success","user":"username"}
[WARN] 2025/04/04 14:56:51 {"event":"LOGIN_ATTEMPT","from":"127.0.0.1","status":"fail","user":"<EMPTY>"}
[WARN] 2025/04/04 14:56:57 {"event":"LOGIN_ATTEMPT","from":"127.0.0.1","status":"success","user":"username"}
[INFO] 2025/04/04 14:57:07 {"device":{"ip":"127.0.0.1","name":"DESKTOP-L1R864K"},"event":"CLIENT_ONLINE"}
[WARN] 2025/04/04 14:59:42 {"event":"SERVICE_EXITING"}
[WARN] 2025/04/04 14:59:45 {"event":"SERVICE_EXIT","status":"success"}
[INFO] 2025/04/04 16:29:28 数据库初始化成功: ./data/spark.db
[INFO] 2025/04/04 16:29:28 {"event":"SERVICE_INIT","listen":":8000"}
[WARN] 2025/04/04 16:29:50 {"event":"LOGIN_ATTEMPT","from":"127.0.0.1","status":"fail","user":"<EMPTY>"}
[WARN] 2025/04/04 16:29:56 {"event":"LOGIN_ATTEMPT","from":"127.0.0.1","status":"success","user":"username"}
[WARN] 2025/04/04 16:30:19 {"event":"LOGIN_ATTEMPT","from":"127.0.0.1","status":"fail","user":"<EMPTY>"}
[WARN] 2025/04/04 16:30:22 {"event":"LOGIN_ATTEMPT","from":"127.0.0.1","status":"fail","user":"<EMPTY>"}
[WARN] 2025/04/04 16:30:25 {"event":"LOGIN_ATTEMPT","from":"127.0.0.1","status":"success","user":"username"}
[WARN] 2025/04/04 16:30:25 {"event":"LOGIN_ATTEMPT","from":"127.0.0.1","status":"success","user":"username"}
[INFO] 2025/04/04 16:31:00 {"device":{"ip":"127.0.0.1","name":"DESKTOP-L1R864K"},"event":"CLIENT_ONLINE"}
[WARN] 2025/04/04 16:31:17 {"event":"LOGIN_ATTEMPT","from":"127.0.0.1","status":"fail","user":"<EMPTY>"}
[WARN] 2025/04/04 16:31:22 {"event":"LOGIN_ATTEMPT","from":"127.0.0.1","status":"success","user":"username"}
[WARN] 2025/04/04 16:38:04 {"event":"SERVICE_EXITING"}
[WARN] 2025/04/04 16:38:07 {"event":"SERVICE_EXIT","status":"success"}
[INFO] 2025/04/04 16:38:26 数据库初始化成功: ./data/spark.db
[INFO] 2025/04/04 16:38:26 {"event":"SERVICE_INIT","listen":":8000"}
[WARN] 2025/04/04 16:38:31 {"event":"LOGIN_ATTEMPT","from":"127.0.0.1","status":"success","user":"username"}
[INFO] 2025/04/04 16:38:33 {"device":{"ip":"127.0.0.1","name":"DESKTOP-L1R864K"},"event":"CLIENT_ONLINE"}
[WARN] 2025/04/04 19:53:10 {"event":"SERVICE_EXITING"}
[WARN] 2025/04/04 19:53:13 {"event":"SERVICE_EXIT","status":"success"}
[INFO] 2025/04/04 19:54:07 数据库初始化成功: ./data/spark.db
[INFO] 2025/04/04 19:54:07 {"event":"SERVICE_INIT","listen":":8000"}
[WARN] 2025/04/04 19:54:12 {"event":"LOGIN_ATTEMPT","from":"127.0.0.1","status":"success","user":"username"}
[INFO] 2025/04/04 19:54:14 {"device":{"ip":"127.0.0.1","name":"DESKTOP-L1R864K"},"event":"CLIENT_ONLINE"}
[WARN] 2025/04/04 20:10:30 {"event":"SERVICE_EXITING"}
[WARN] 2025/04/04 20:10:33 {"event":"SERVICE_EXIT","status":"success"}
[INFO] 2025/04/04 20:17:35 数据库初始化成功: ./data/spark.db
[INFO] 2025/04/04 20:17:35 {"event":"SERVICE_INIT","listen":":8000"}
[WARN] 2025/04/04 20:17:38 {"event":"LOGIN_ATTEMPT","from":"127.0.0.1","status":"success","user":"username"}
[INFO] 2025/04/04 20:17:41 {"device":{"ip":"127.0.0.1","name":"DESKTOP-L1R864K"},"event":"CLIENT_ONLINE"}
[WARN] 2025/04/04 20:18:05 {"event":"LOGIN_ATTEMPT","from":"127.0.0.1","status":"fail","user":"<EMPTY>"}
[WARN] 2025/04/04 20:18:11 {"event":"LOGIN_ATTEMPT","from":"127.0.0.1","status":"success","user":"username"}
[WARN] 2025/04/04 20:57:15 {"event":"SERVICE_EXITING"}
[WARN] 2025/04/04 20:57:18 {"event":"SERVICE_EXIT","status":"success"}
[INFO] 2025/04/04 21:02:10 数据库初始化成功: ./data/spark.db
[INFO] 2025/04/04 21:02:10 {"event":"SERVICE_INIT","listen":":8000"}
[WARN] 2025/04/04 21:02:24 {"event":"LOGIN_ATTEMPT","from":"127.0.0.1","status":"fail","user":"<EMPTY>"}
[WARN] 2025/04/04 21:02:30 {"event":"LOGIN_ATTEMPT","from":"127.0.0.1","status":"success","user":"username"}
[WARN] 2025/04/04 21:02:47 {"event":"LOGIN_ATTEMPT","from":"127.0.0.1","status":"success","user":"username"}
[INFO] 2025/04/04 21:03:14 {"device":{"ip":"127.0.0.1","name":"DESKTOP-L1R864K"},"event":"CLIENT_ONLINE"}
[INFO] 2025/04/04 21:08:23 {"event":"SCREENSHOT","from":"127.0.0.1","status":"success","target":{"ip":"127.0.0.1","name":"DESKTOP-L1R864K"}}
[WARN] 2025/04/04 21:15:41 {"event":"LOGIN_ATTEMPT","from":"127.0.0.1","status":"fail","user":"<EMPTY>"}
[INFO] 2025/04/04 22:25:15 {"event":"CLIENT_DISCONNECT","ip":"127.0.0.1"}
[INFO] 2025/04/04 23:18:27 {"device":{"ip":"127.0.0.1","name":"DESKTOP-L1R864K"},"event":"CLIENT_OFFLINE"}
[WARN] 2025/04/04 23:18:29 {"event":"SERVICE_EXITING"}
[WARN] 2025/04/04 23:18:32 {"event":"SERVICE_EXIT","status":"success"}
