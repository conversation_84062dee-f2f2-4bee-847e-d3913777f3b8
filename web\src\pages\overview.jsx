import React, {useEffect, useRef, useState} from 'react';
import ProTable, {TableDropdown} from '@ant-design/pro-table';
import {Button, Form, Image, Input, message, Modal, Progress, Tooltip, Tabs, Card} from 'antd';
import {catchBlobReq, formatSize, request, tsToTime, waitTime} from "../utils/utils";
import {QuestionCircleOutlined} from "@ant-design/icons";
import i18n from "../locale/locale";
import axios from 'axios';
import TunnelManager from './Tunnel/TunnelManager';
import DeviceNotes from '../components/DeviceNotes';

// DO NOT EDIT OR DELETE THIS COPYRIGHT MESSAGE.
let ComponentMap = {
	Generate: null,
	Explorer: null,
	Terminal: null,
	ProcMgr: null,
	Desktop: null,
	Execute: null,
	Socks5: null,
};











// 内联执行表单组件
const ExecuteForm = ({ device }) => {
	const [form] = Form.useForm();
	const [loading, setLoading] = useState(false);

	const handleExecute = async (values) => {
		setLoading(true);
		try {
			// 使用设备连接ID而不是数据库ID
			const deviceConnId = device.device_id || device.conn || device.uuid || device.id;
			const formData = {
				...values,
				device: deviceConnId
			};

			let basePath = location.origin + location.pathname + 'api/device/';
			const response = await request(basePath + 'exec', formData);

			if (response.data.code === 0) {
				message.success('命令执行成功');
				form.resetFields();
			} else {
				message.error('命令执行失败: ' + response.data.msg);
			}
		} catch (error) {
			message.error('命令执行失败: ' + error.message);
		} finally {
			setLoading(false);
		}
	};

	return (
		<Card title="运行命令" size="small">
			<Form
				form={form}
				layout="vertical"
				onFinish={handleExecute}
			>
				<Form.Item
					name="cmd"
					label="命令"
					rules={[{ required: true, message: '请输入命令' }]}
				>
					<Input placeholder="请输入要执行的命令" />
				</Form.Item>
				<Form.Item
					name="args"
					label="参数 (以空格分隔)"
				>
					<Input placeholder="请输入命令参数" />
				</Form.Item>
				<Form.Item>
					<Button type="primary" htmlType="submit" loading={loading}>
						确定
					</Button>
				</Form.Item>
			</Form>
		</Card>
	);
};

function overview(props) {
	const [loading, setLoading] = useState(false);
	const [execute, setExecute] = useState(false);
	const [desktop, setDesktop] = useState(false);
	const [procMgr, setProcMgr] = useState(false);
	const [explorer, setExplorer] = useState(false);
	const [generate, setGenerate] = useState(false);
	const [terminal, setTerminal] = useState(false);
	const [socks5, setSocks5] = useState(false);
	const [screenBlob, setScreenBlob] = useState('');
	const [remarkModalVisible, setRemarkModalVisible] = useState(false);
	const [currentDevice, setCurrentDevice] = useState(null);
	const [remarkForm] = Form.useForm();
	const [searchKeyword, setSearchKeyword] = useState('');
	const [forceUseDbApi, setForceUseDbApi] = useState(false);
	const [deviceDetailVisible, setDeviceDetailVisible] = useState(false);
	const [selectedDevice, setSelectedDevice] = useState(null);

	// 备注编辑相关状态
	const [remarkEditMode, setRemarkEditMode] = useState(false);
	const [remarkEditValue, setRemarkEditValue] = useState('');
	const [remarkSaving, setRemarkSaving] = useState(false);

	const columns = [
		{
			key: 'id',
			title: 'ID',
			dataIndex: 'id',
			ellipsis: true,
			width: 60,
			hideInSearch: true,
			render: (_, record) => {
				// 获取UUID用于连接
				const actualUuid = record.conn || record.uuid || '';
				// 直接使用record.id，这是数据库自增ID
				const displayId = String(record.id || '[empty]');

				return (
					<span
						style={{
							fontFamily: 'monospace',
							fontSize: '13px',
							color: '#1890ff',
							cursor: 'pointer',
							textDecoration: 'underline',
							fontWeight: 'bold'
						}}
						title={`点击查看设备详情 - 数据库ID: ${displayId}, UUID: ${actualUuid}`}
						onClick={() => handleDeviceDetail(record)}
					>
						{displayId}
					</span>
				);
			}
		},
		{
			key: 'status',
			title: '状态',
			dataIndex: 'status',
			ellipsis: true,
			width: 80,
			hideInSearch: true,
			filters: [
				{ text: '在线', value: 'online' },
				{ text: '离线', value: 'offline' }
			],
			onFilter: (value, record) => record.status === value,
			render: (_, record) => {
				// 获取状态
				const actualStatus = record.status || 'offline';
				const isOnline = actualStatus === 'online';
				return (
					<span style={{
						display: 'inline-flex',
						alignItems: 'center',
						color: isOnline ? '#52c41a' : '#ff4d4f',
						fontSize: '12px',
						fontWeight: 'bold'
					}}>
						<span style={{
							display: 'inline-block',
							width: '8px',
							height: '8px',
							borderRadius: '50%',
							backgroundColor: isOnline ? '#52c41a' : '#ff4d4f',
							marginRight: '6px'
						}}></span>
						{isOnline ? '在线' : '离线'}
					</span>
				);
			}
		},
		{
			key: 'hostname',
			title: i18n.t('OVERVIEW.HOSTNAME'),
			dataIndex: 'hostname',
			ellipsis: true,
			width: 100,
			hideInSearch: true
		},
		{
			key: 'username',
			title: i18n.t('OVERVIEW.USERNAME'),
			dataIndex: 'username',
			ellipsis: true,
			width: 90,
			hideInSearch: true
		},
		{
			key: 'ping',
			title: 'Ping',
			dataIndex: 'latency',
			ellipsis: true,
			renderText: (v) => String(v) + 'ms',
			width: 60,
			hideInSearch: true
		},
		{
			key: 'cpu_usage',
			title: i18n.t('OVERVIEW.CPU_USAGE'),
			dataIndex: 'cpu_usage',
			ellipsis: true,
			render: (_, v) => <UsageBar title={renderCPUStat(v.cpu)} {...(v.cpu || {})} />,
			width: 100
		},
		{
			key: 'ram_usage',
			title: i18n.t('OVERVIEW.RAM_USAGE'),
			dataIndex: 'ram_usage',
			ellipsis: true,
			render: (_, v) => <UsageBar title={renderRAMStat(v.ram)} {...(v.ram || {})} />,
			width: 100
		},
		{
			key: 'disk_usage',
			title: i18n.t('OVERVIEW.DISK_USAGE'),
			dataIndex: 'disk_usage',
			ellipsis: true,
			render: (_, v) => <UsageBar title={renderDiskStat(v.disk)} {...(v.disk || {})} />,
			width: 100
		},
		{
			key: 'os',
			title: i18n.t('OVERVIEW.OS'),
			dataIndex: 'os',
			ellipsis: true,
			width: 80
		},
		{
			key: 'arch',
			title: i18n.t('OVERVIEW.ARCH'),
			dataIndex: 'arch',
			ellipsis: true,
			width: 70
		},
		{
			key: 'ram_total',
			title: i18n.t('OVERVIEW.RAM'),
			dataIndex: 'ram_total',
			ellipsis: true,
			renderText: formatSize,
			width: 70
		},
		{
			key: 'mac',
			title: 'MAC',
			dataIndex: 'mac',
			ellipsis: true,
			width: 100
		},
		{
			key: 'lan',
			title: 'LAN',
			dataIndex: 'lan',
			ellipsis: true,
			width: 100,
			hideInSearch: true
		},
		{
			key: 'wan',
			title: 'WAN',
			dataIndex: 'wan',
			ellipsis: true,
			width: 100,
			hideInSearch: true
		},
		{
			key: 'uptime',
			title: i18n.t('OVERVIEW.UPTIME'),
			dataIndex: 'uptime',
			ellipsis: true,
			renderText: tsToTime,
			width: 100
		},
		{
			key: 'remark',
			title: '备注',
			dataIndex: 'remark',
			ellipsis: true,
			width: 120,
			hideInSearch: true,
			render: (remark, device) => (
				<div style={{ cursor: 'pointer' }} onClick={() => handleEditRemark(device)}>
					{remark || <span style={{ color: '#999' }}>点击添加备注</span>}
				</div>
			)
		},

		{
			key: 'option',
			title: i18n.t('OVERVIEW.OPERATIONS'),
			dataIndex: 'id',
			valueType: 'option',
			ellipsis: false,
			render: (_, device) => renderOperation(device),
			width: 170
		},
	];
	const options = {
		show: true,
		density: true,
		setting: true,
	};
	const tableRef = useRef();
	const loadComponent = (component, callback) => {
		let element = null;
		component = component.toLowerCase();
		Object.keys(ComponentMap).forEach(k => {
			if (k.toLowerCase() === component.toLowerCase()) {
				element = k;
			}
		});
		if (!element) return;
		if (ComponentMap[element] === null) {
			if (component.toLowerCase() === 'socks5') {
				import('../components/socks5/index.jsx').then((m) => {
					ComponentMap[element] = m.default;
					callback();
				}).catch(err => {
					console.error('Failed to load Socks5 component:', err);
					// 尝试加载备选文件
					import('../components/socks5/socks5.jsx').then((m) => {
						ComponentMap[element] = m.default;
						callback();
					}).catch(err2 => {
						console.error('Failed to load alternative Socks5 component:', err2);
						callback();
					});
				});
			} else {
				import('../components/'+component+'/'+component).then((m) => {
					ComponentMap[element] = m.default;
					callback();
				}).catch(err => {
					console.error(`Failed to load ${component} component:`, err);
					callback();
				});
			}
		} else {
			callback();
		}
	}

	useEffect(() => {
		// auto update is only available when all modal are closed.
		if (!execute && !desktop && !procMgr && !explorer && !generate && !terminal && !socks5) {
			let id = setInterval(getData, 3000);
			return () => {
				clearInterval(id);
			};
		}
	}, [execute, desktop, procMgr, explorer, generate, terminal, socks5]);

	function renderCPUStat(cpu) {
		if (!cpu) {
			return <div>CPU信息不可用</div>;
		}
		let { model, usage, cores } = cpu;
		usage = Math.round((usage || 0) * 100) / 100;
		cores = cores || {};
		cores = {
			physical: Math.max(cores.physical || 1, 1),
			logical: Math.max(cores.logical || 1, 1),
		}
		return (
			<div>
				<div
					style={{
						fontSize: '10px',
					}}
				>
					{model || 'Unknown'}
				</div>
				{i18n.t('OVERVIEW.CPU_USAGE') + i18n.t('COMMON.COLON') + usage + '%'}
				<br />
				{i18n.t('OVERVIEW.CPU_LOGICAL_CORES') + i18n.t('COMMON.COLON') + cores.logical}
				<br />
				{i18n.t('OVERVIEW.CPU_PHYSICAL_CORES') + i18n.t('COMMON.COLON') + cores.physical}
			</div>
		);
	}
	function renderRAMStat(info) {
		if (!info) {
			return <div>内存信息不可用</div>;
		}
		let { usage, total, used } = info;
		usage = Math.round((usage || 0) * 100) / 100;
		total = total || 0;
		used = used || 0;
		return (
			<div>
				{i18n.t('OVERVIEW.RAM_USAGE') + i18n.t('COMMON.COLON') + usage + '%'}
				<br />
				{i18n.t('OVERVIEW.FREE') + i18n.t('COMMON.COLON') + formatSize(total - used)}
				<br />
				{i18n.t('OVERVIEW.USED') + i18n.t('COMMON.COLON') + formatSize(used)}
				<br />
				{i18n.t('OVERVIEW.TOTAL') + i18n.t('COMMON.COLON') + formatSize(total)}
			</div>
		);
	}
	function renderDiskStat(info) {
		if (!info) {
			return <div>磁盘信息不可用</div>;
		}
		let { usage, total, used } = info;
		usage = Math.round((usage || 0) * 100) / 100;
		total = total || 0;
		used = used || 0;
		return (
			<div>
				{i18n.t('OVERVIEW.DISK_USAGE') + i18n.t('COMMON.COLON') + usage + '%'}
				<br />
				{i18n.t('OVERVIEW.FREE') + i18n.t('COMMON.COLON') + formatSize(total - used)}
				<br />
				{i18n.t('OVERVIEW.USED') + i18n.t('COMMON.COLON') + formatSize(used)}
				<br />
				{i18n.t('OVERVIEW.TOTAL') + i18n.t('COMMON.COLON') + formatSize(total)}
			</div>
		);
	}

	function renderOperation(device) {
		let menus = [
			{key: 'execute', name: i18n.t('OVERVIEW.EXECUTE')},
			{key: 'desktop', name: i18n.t('OVERVIEW.DESKTOP')},
			{key: 'screenshot', name: i18n.t('OVERVIEW.SCREENSHOT')},
			{key: 'socks5', name: "隧道"},
			{key: 'lock', name: i18n.t('OVERVIEW.LOCK')},
			{key: 'logoff', name: i18n.t('OVERVIEW.LOGOFF')},
			{key: 'hibernate', name: i18n.t('OVERVIEW.HIBERNATE')},
			{key: 'suspend', name: i18n.t('OVERVIEW.SUSPEND')},
			{key: 'restart', name: i18n.t('OVERVIEW.RESTART')},
			{key: 'shutdown', name: i18n.t('OVERVIEW.SHUTDOWN')},
			{key: 'offline', name: i18n.t('OVERVIEW.OFFLINE')},
		];
		return [
			<a key='terminal' onClick={() => onMenuClick('terminal', device)}>{i18n.t('OVERVIEW.TERMINAL')}</a>,
			<a key='explorer' onClick={() => onMenuClick('explorer', device)}>{i18n.t('OVERVIEW.EXPLORER')}</a>,
			<a key='procmgr' onClick={() => onMenuClick('procmgr', device)}>{i18n.t('OVERVIEW.PROC_MANAGER')}</a>,
			<TableDropdown
				key='more'
				onSelect={key => onMenuClick(key, device)}
				menus={menus}
			/>,
		]
	}

	function onMenuClick(act, value) {
		const device = value;
		let hooksMap = {
			terminal: setTerminal,
			explorer: setExplorer,
			generate: setGenerate,
			procmgr: setProcMgr,
			execute: setExecute,
			desktop: setDesktop,
			socks5: setSocks5,
		};
		if (hooksMap[act]) {
			setLoading(true);
			loadComponent(act, () => {
				hooksMap[act](device);
				setLoading(false);
			});
			return;
		}
		if (act === 'screenshot') {
			// 使用设备连接ID而不是数据库ID
			const deviceConnId = device.device_id || device.conn || device.uuid;
			request('/api/device/screenshot/get', {device: deviceConnId}, {}, {
				responseType: 'blob'
			}).then(res => {
				if ((res.data.type ?? '').substring(0, 5) === 'image') {
					if (screenBlob.length > 0) {
						URL.revokeObjectURL(screenBlob);
					}
					setScreenBlob(URL.createObjectURL(res.data));
				}
			}).catch(catchBlobReq);
			return;
		}
		Modal.confirm({
			title: i18n.t('OVERVIEW.OPERATION_CONFIRM').replace('{0}', i18n.t('OVERVIEW.'+act.toUpperCase())),
			icon: <QuestionCircleOutlined/>,
			onOk() {
				// 使用设备连接ID而不是数据库ID
				const deviceConnId = device.device_id || device.conn || device.uuid;
				request('/api/device/' + act, {device: deviceConnId}).then(res => {
					let data = res.data;
					if (data.code === 0) {
						message.success(i18n.t('OVERVIEW.OPERATION_SUCCESS'));
						tableRef.current.reload();
					}
				});
			}
		});
	}

	// 处理备注编辑
	const handleEditRemark = (device) => {
		setCurrentDevice(device);
		remarkForm.setFieldsValue({ remark: device.remark || '' });
		setRemarkModalVisible(true);
	};

	// 保存备注
	const handleSaveRemark = async () => {
		try {
			const values = await remarkForm.validateFields();
			const response = await axios.put(`/api/devices/${currentDevice.conn}/remark`, {
				remark: values.remark
			}, {
				headers: {
					'Content-Type': 'application/json'
				}
			});

			if (response.data.code === 200) {
				message.success('备注更新成功');
				setRemarkModalVisible(false);
				// 强制使用数据库API刷新数据，确保备注显示
				setForceUseDbApi(true);
				tableRef.current.reload();
				// 3秒后恢复使用实时API
				setTimeout(() => setForceUseDbApi(false), 3000);
			} else {
				message.error('备注更新失败: ' + response.data.message);
			}
		} catch (error) {
			console.error('备注更新错误:', error);
			message.error('备注更新失败: ' + (error.response?.data?.message || error.message));
		}
	};

	// 处理设备详情
	const handleDeviceDetail = (device) => {
		setSelectedDevice(device);
		setDeviceDetailVisible(true);
		// 重置备注编辑状态
		setRemarkEditMode(false);
		setRemarkEditValue('');
	};

	// 设备详情页面备注编辑相关函数
	const handleDetailEditRemark = () => {
		setRemarkEditMode(true);
		setRemarkEditValue(selectedDevice.remark || '');
	};

	const handleDetailCancelRemark = () => {
		setRemarkEditMode(false);
		setRemarkEditValue('');
	};

	const handleDetailSaveRemark = async () => {
		if (remarkSaving) return;

		setRemarkSaving(true);
		try {
			const response = await axios.put(`/api/devices/${selectedDevice.conn || selectedDevice.uuid}/remark`, {
				remark: remarkEditValue
			}, {
				headers: {
					'Content-Type': 'application/json'
				}
			});

			if (response.data.code === 200) {
				message.success('备注更新成功');
				// 更新当前选中设备的备注
				setSelectedDevice(prev => ({ ...prev, remark: remarkEditValue }));
				// 刷新表格数据
				tableRef.current?.reload();
				// 退出编辑模式
				setRemarkEditMode(false);
			} else {
				message.error('备注更新失败: ' + response.data.message);
			}
		} catch (error) {
			console.error('备注更新错误:', error);
			message.error('备注更新失败: ' + (error.response?.data?.message || error.message));
		} finally {
			setRemarkSaving(false);
		}
	};

	// 更新备注
	const handleUpdateRemark = async (device, newRemark) => {
		if (device.remark === newRemark) return; // 没有变化则不更新

		try {
			const response = await axios.put(`/api/devices/${device.conn}/remark`, {
				remark: newRemark
			}, {
				headers: {
					'Content-Type': 'application/json'
				}
			});

			if (response.data.code === 200) {
				message.success('备注更新成功');
				// 更新当前选中设备的备注
				setSelectedDevice(prev => ({ ...prev, remark: newRemark }));
				// 刷新表格数据
				tableRef.current?.reload();
			} else {
				message.error('备注更新失败: ' + response.data.message);
			}
		} catch (error) {
			console.error('备注更新错误:', error);
			message.error('备注更新失败: ' + (error.response?.data?.message || error.message));
		}
	};



	function toolBar() {
		return (
			<div style={{ display: 'flex', gap: 8, alignItems: 'center' }}>
				<Input.Search
					placeholder="搜索主机名、用户名、IP、备注..."
					allowClear
					style={{ width: 300 }}
					value={searchKeyword}
					onChange={(e) => setSearchKeyword(e.target.value)}
					onSearch={() => {
						tableRef.current?.reload();
					}}
				/>
				<Button type='primary' onClick={() => onMenuClick('generate', true)}>
					{i18n.t('OVERVIEW.GENERATE')}
				</Button>
			</div>
		)
	}

	async function getData(params = {}) {
		await waitTime(300);

		// 如果有搜索关键词或强制使用数据库API，使用新的设备API
		if (searchKeyword || forceUseDbApi) {
			try {
				const searchParams = {
					page: params.current || 1,
					page_size: params.pageSize || 10,
					search: searchKeyword || '', // 使用统一的搜索关键词
				};

				console.log('搜索参数:', searchParams);
				const response = await axios.get('/api/devices', { params: searchParams });
				if (response.data.code === 200) {
					console.log('搜索结果:', response.data.data);
					return {
						data: response.data.data.list || [],
						success: true,
						total: response.data.data.total || 0,
					};
				}
			} catch (error) {
				console.error('搜索设备失败:', error);
			}
		}

		// 使用统一的设备API获取完整设备信息（包括实时数据和备注）
		try {
			const response = await axios.get('/api/devices', {
				params: {
					page: params.current || 1,
					page_size: params.pageSize || 100,
					search: searchKeyword || ''
				}
			});
			if (response.data.code === 200) {
				const devices = response.data.data.list || [];

				return {
					data: devices,
					success: true,
					total: response.data.data.total || devices.length,
				};
			}
		} catch (error) {
			console.error('获取设备列表失败:', error);
			return {
				data: [],
				success: false,
				total: 0,
			};
		}
	}

	return (
		<>
			<Image
				preview={{
					visible: !!screenBlob,
					src: screenBlob,
					onVisibleChange: () => {
						URL.revokeObjectURL(screenBlob);
						setScreenBlob('');
					}
				}}
			/>
			{
				ComponentMap.Generate &&
				<ComponentMap.Generate
					visible={generate}
					onVisibleChange={setGenerate}
				/>
			}
			{
				ComponentMap.Execute &&
				<ComponentMap.Execute
					visible={execute}
					device={execute}
					onCancel={setExecute.bind(null, false)}
				/>
			}
			{
				ComponentMap.Explorer &&
				<ComponentMap.Explorer
					open={explorer}
					device={explorer}
					onCancel={setExplorer.bind(null, false)}
				/>
			}
			{
				ComponentMap.ProcMgr &&
				<ComponentMap.ProcMgr
					open={procMgr}
					device={procMgr}
					onCancel={setProcMgr.bind(null, false)}
				/>
			}
			{
				ComponentMap.Desktop &&
				<ComponentMap.Desktop
					open={desktop}
					device={desktop}
					onCancel={setDesktop.bind(null, false)}
				/>
			}
			{
				ComponentMap.Terminal &&
				<ComponentMap.Terminal
					open={terminal}
					device={terminal}
					onCancel={setTerminal.bind(null, false)}
				/>
			}
			{
				ComponentMap.Socks5 &&
				<ComponentMap.Socks5
					open={socks5}
					device={socks5}
					onCancel={setSocks5.bind(null, false)}
				/>
			}

			{/* 设备详情Modal */}
			<Modal
				title={`设备详情 - ${selectedDevice?.hostname || 'Unknown'}`}
				open={deviceDetailVisible}
				onCancel={() => setDeviceDetailVisible(false)}
				width={1200}
				footer={null}
				destroyOnClose
			>
				{selectedDevice && (
					<Tabs
						defaultActiveKey="info"
						onChange={(activeKey) => {
							// 检查设备是否在线
							if (selectedDevice.status !== 'online' && activeKey !== 'info') {
								message.warning('设备离线，无法使用此功能');
								return;
							}

							// 点击标签页时直接打开对应功能窗口（隧道管理和执行除外）
							if (activeKey !== 'info' && activeKey !== 'tunnel' && activeKey !== 'execute') {
								const actionMap = {
									'files': 'explorer',
									'terminal': 'terminal',
									'processes': 'procmgr',
									'desktop': 'desktop',
									'screenshot': 'screenshot'
								};
								if (actionMap[activeKey]) {
									onMenuClick(actionMap[activeKey], selectedDevice);
								}
							}
						}}
						items={[
							{
								key: 'info',
								label: '设备信息',
								children: (
									<div style={{ padding: '16px 0' }}>
										<div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px' }}>
											<div>
												<h4>基本信息</h4>
												<p><strong>主机名:</strong> {selectedDevice.hostname}</p>
												<p><strong>用户名:</strong> {selectedDevice.username}</p>
												<p><strong>操作系统:</strong> {selectedDevice.os}</p>
												<p><strong>架构:</strong> {selectedDevice.arch}</p>
												<p><strong>状态:</strong>
													<span style={{ color: selectedDevice.status === 'online' ? '#52c41a' : '#ff4d4f' }}>
														{selectedDevice.status === 'online' ? '在线' : '离线'}
													</span>
												</p>
												<div style={{ display: 'flex', alignItems: 'flex-start', gap: '8px', marginTop: '8px' }}>
													<strong>备注:</strong>
													{remarkEditMode ? (
														<div style={{ display: 'flex', flexDirection: 'column', gap: '8px', flex: 1 }}>
															<Input.TextArea
																value={remarkEditValue}
																onChange={(e) => setRemarkEditValue(e.target.value)}
																placeholder="请输入备注"
																autoSize={{ minRows: 2, maxRows: 4 }}
																onPressEnter={handleDetailSaveRemark}
															/>
															<div style={{ display: 'flex', gap: '8px' }}>
																<Button
																	type="primary"
																	size="small"
																	onClick={handleDetailSaveRemark}
																	loading={remarkSaving}
																>
																	保存
																</Button>
																<Button
																	size="small"
																	onClick={handleDetailCancelRemark}
																>
																	取消
																</Button>
															</div>
														</div>
													) : (
														<div style={{ display: 'flex', alignItems: 'flex-start', gap: '8px', flex: 1 }}>
															<span style={{ flex: 1, minHeight: '22px' }}>
																{selectedDevice.remark || '无'}
															</span>
															<Button
																type="link"
																size="small"
																onClick={handleDetailEditRemark}
																style={{ padding: '0 4px', height: 'auto' }}
															>
																编辑
															</Button>
														</div>
													)}
												</div>
											</div>
											<div>
												<h4>网络信息</h4>
												<p><strong>内网IP:</strong> {selectedDevice.lan}</p>
												<p><strong>外网IP:</strong> {selectedDevice.wan}</p>
												<p><strong>MAC地址:</strong> {selectedDevice.mac_address}</p>
												<p><strong>延迟:</strong> {selectedDevice.latency}ms</p>
											</div>
										</div>
										<div style={{ marginTop: '16px' }}>
											<h4>硬件信息</h4>
											<p><strong>CPU:</strong> {selectedDevice.cpu?.model}</p>
											<p><strong>CPU使用率:</strong> {selectedDevice.cpu?.usage?.toFixed(2)}%</p>
											<p><strong>内存使用率:</strong> {selectedDevice.ram?.usage?.toFixed(2)}%</p>
											<p><strong>磁盘使用率:</strong> {selectedDevice.disk?.usage?.toFixed(2)}%</p>
										</div>
									</div>
								)
							},
							{
								key: 'files',
								label: (
									<span style={{ color: selectedDevice.status === 'online' ? 'inherit' : '#ccc' }}>
										文件管理
									</span>
								),
								disabled: selectedDevice.status !== 'online',
								children: (
									<div style={{ padding: '40px 0', textAlign: 'center', color: '#666' }}>
										{selectedDevice.status === 'online' ? (
											<p>📁 点击此标签页将打开文件管理器窗口</p>
										) : (
											<p style={{ color: '#ccc' }}>📁 设备离线，文件管理功能不可用</p>
										)}
									</div>
								)
							},
							{
								key: 'terminal',
								label: (
									<span style={{ color: selectedDevice.status === 'online' ? 'inherit' : '#ccc' }}>
										终端
									</span>
								),
								disabled: selectedDevice.status !== 'online',
								children: (
									<div style={{ padding: '40px 0', textAlign: 'center', color: '#666' }}>
										{selectedDevice.status === 'online' ? (
											<p>💻 点击此标签页将打开终端窗口</p>
										) : (
											<p style={{ color: '#ccc' }}>💻 设备离线，终端功能不可用</p>
										)}
									</div>
								)
							},
							{
								key: 'processes',
								label: (
									<span style={{ color: selectedDevice.status === 'online' ? 'inherit' : '#ccc' }}>
										进程管理
									</span>
								),
								disabled: selectedDevice.status !== 'online',
								children: (
									<div style={{ padding: '40px 0', textAlign: 'center', color: '#666' }}>
										{selectedDevice.status === 'online' ? (
											<p>⚙️ 点击此标签页将打开进程管理器窗口</p>
										) : (
											<p style={{ color: '#ccc' }}>⚙️ 设备离线，进程管理功能不可用</p>
										)}
									</div>
								)
							},
							{
								key: 'desktop',
								label: (
									<span style={{ color: selectedDevice.status === 'online' ? 'inherit' : '#ccc' }}>
										远程桌面
									</span>
								),
								disabled: selectedDevice.status !== 'online',
								children: (
									<div style={{ padding: '40px 0', textAlign: 'center', color: '#666' }}>
										{selectedDevice.status === 'online' ? (
											<p>🖥️ 点击此标签页将打开远程桌面窗口</p>
										) : (
											<p style={{ color: '#ccc' }}>🖥️ 设备离线，远程桌面功能不可用</p>
										)}
									</div>
								)
							},
							{
								key: 'screenshot',
								label: (
									<span style={{ color: selectedDevice.status === 'online' ? 'inherit' : '#ccc' }}>
										屏幕截图
									</span>
								),
								disabled: selectedDevice.status !== 'online',
								children: (
									<div style={{ padding: '40px 0', textAlign: 'center', color: '#666' }}>
										{selectedDevice.status === 'online' ? (
											<p>📸 点击此标签页将立即截图</p>
										) : (
											<p style={{ color: '#ccc' }}>📸 设备离线，屏幕截图功能不可用</p>
										)}
									</div>
								)
							},
							{
								key: 'execute',
								label: (
									<span style={{ color: selectedDevice.status === 'online' ? 'inherit' : '#ccc' }}>
										运行
									</span>
								),
								disabled: selectedDevice.status !== 'online',
								children: (
									<div style={{ padding: '20px' }}>
										<ExecuteForm device={selectedDevice} />
									</div>
								)
							},
							{
								key: 'tunnel',
								label: '隧道管理',
								children: (
									<div style={{ height: '500px', overflow: 'auto' }}>
										<TunnelManager deviceFilter={selectedDevice} />
									</div>
								)
							},
							{
								key: 'notes',
								label: '笔记',
								children: (
									<div style={{ height: '500px', overflow: 'auto' }}>
										<DeviceNotes
											deviceUuid={selectedDevice.uuid}
											deviceName={selectedDevice.hostname}
										/>
									</div>
								)
							}
						]}
					/>
				)}
			</Modal>

			{/* 设备状态统计 */}
			<DeviceStatusSummary />

			<ProTable
				scroll={{
					x: 'max-content',
					scrollToFirstRowOnChange: true
				}}
				rowKey='id'
				search={false}
				options={options}
				columns={columns}
				columnsState={{
					persistenceKey: 'columnsState',
					persistenceType: 'localStorage'
				}}
				onLoadingChange={setLoading}
				loading={loading}
				request={getData}
				pagination={{
					defaultPageSize: 10,
					showSizeChanger: true,
					showQuickJumper: true,
					showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/总共 ${total} 条`,
				}}
				actionRef={tableRef}
				toolBarRender={toolBar}
			/>

			{/* 备注编辑模态框 */}
			<Modal
				title="编辑设备备注"
				open={remarkModalVisible}
				onOk={handleSaveRemark}
				onCancel={() => setRemarkModalVisible(false)}
				okText="保存"
				cancelText="取消"
			>
				<Form form={remarkForm} layout="vertical">
					<Form.Item
						name="remark"
						label="备注信息"
						rules={[{ max: 200, message: '备注不能超过200个字符' }]}
					>
						<Input.TextArea
							rows={4}
							placeholder="请输入设备备注信息..."
							maxLength={200}
							showCount
						/>
					</Form.Item>
				</Form>
			</Modal>
		</>
	);
}
function UsageBar(props) {
	let {usage} = props;
	usage = usage || 0;
	usage = Math.round(usage * 100) / 100;

	return (
		<Tooltip
			title={props.title??`${usage}%`}
			overlayInnerStyle={{
				whiteSpace: 'nowrap',
				wordBreak: 'keep-all',
				maxWidth: '300px',
			}}
			overlayStyle={{
				maxWidth: '300px',
			}}
		>
			<Progress percent={usage} showInfo={false} strokeWidth={12} trailColor='#FFECFF'/>
		</Tooltip>
	);
}

// 设备状态统计组件
function DeviceStatusSummary() {
	const [statusStats, setStatusStats] = useState({ online: 0, offline: 0, total: 0 });

	useEffect(() => {
		// 获取设备状态统计
		const fetchStatusStats = async () => {
			try {
				const response = await axios.get('/api/devices', {
					params: { page: 1, page_size: 1000 } // 获取所有设备用于统计
				});
				if (response.data.code === 200) {
					const devices = response.data.data.list || [];
					const online = devices.filter(d => d.status === 'online').length;
					const offline = devices.filter(d => d.status === 'offline').length;
					setStatusStats({
						online,
						offline,
						total: devices.length
					});
				}
			} catch (error) {
				console.error('获取设备状态统计失败:', error);
			}
		};

		fetchStatusStats();
		// 每30秒更新一次统计
		const interval = setInterval(fetchStatusStats, 30000);
		return () => clearInterval(interval);
	}, []);

	return (
		<div style={{
			marginBottom: '16px',
			padding: '12px 16px',
			background: '#fafafa',
			borderRadius: '6px',
			display: 'flex',
			alignItems: 'center',
			gap: '24px'
		}}>
			<span style={{ fontWeight: 'bold', color: '#666' }}>设备状态统计：</span>
			<span style={{ color: '#52c41a', fontWeight: 'bold' }}>
				<span style={{
					display: 'inline-block',
					width: '8px',
					height: '8px',
					borderRadius: '50%',
					backgroundColor: '#52c41a',
					marginRight: '6px'
				}}></span>
				在线: {statusStats.online}
			</span>
			<span style={{ color: '#ff4d4f', fontWeight: 'bold' }}>
				<span style={{
					display: 'inline-block',
					width: '8px',
					height: '8px',
					borderRadius: '50%',
					backgroundColor: '#ff4d4f',
					marginRight: '6px'
				}}></span>
				离线: {statusStats.offline}
			</span>
			<span style={{ color: '#666' }}>
				总计: {statusStats.total}
			</span>
		</div>
	);
}

function wrapper(props) {
	let Component = overview;
	return (<Component {...props} key={Math.random()}/>)
}

export default wrapper;