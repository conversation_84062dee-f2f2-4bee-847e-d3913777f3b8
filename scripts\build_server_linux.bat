@echo off
echo Building Spark Server for Linux...

set GOOS=linux
set GOARCH=amd64
set CGO_ENABLED=0

echo Fixing dependencies...
go mod edit -droprequire github.com/mattn/go-sqlite3
go mod edit -droprequire gorm.io/driver/sqlite
go get modernc.org/sqlite
go get github.com/glebarez/sqlite

echo Cleaning and downloading dependencies...
if exist go.sum del go.sum
go mod tidy
go mod download

echo Building server...
go build -ldflags "-s -w -X 'Spark/server/config.Commit='" -tags=jsoniter -o ./releases/server_linux ./server

if %ERRORLEVEL% EQU 0 (
    echo Build completed successfully!
    echo Output: ./releases/server_linux
) else (
    echo Build failed!
    exit /b 1
)

pause
