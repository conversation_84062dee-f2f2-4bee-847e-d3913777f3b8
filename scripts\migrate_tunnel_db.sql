-- 数据库迁移脚本：为隧道表添加 SOCKS5 用户名和密码字段
-- 执行方式：sqlite3 spark.db < scripts/migrate_tunnel_db.sql

-- 检查是否已经存在 username 字段
-- 如果不存在则添加
ALTER TABLE tunnels ADD COLUMN username VARCHAR(64);

-- 检查是否已经存在 password 字段  
-- 如果不存在则添加
ALTER TABLE tunnels ADD COLUMN password VARCHAR(128);

-- 更新 local_port 字段允许为空（SOCKS5 隧道不需要本地端口）
-- SQLite 不支持直接修改列约束，需要重建表
-- 但由于我们已经在 database.go 中更新了表结构，新建的表会自动使用新结构

-- 为新字段创建索引（可选）
CREATE INDEX IF NOT EXISTS idx_tunnels_username ON tunnels(username);

-- 显示迁移完成信息
SELECT 'Database migration completed successfully' as message;
