import React from 'react';
import { Card, Statistic, Row, Col } from 'antd';
import { 
  LinkOutlined, 
  CloudUploadOutlined, 
  CloudDownloadOutlined, 
  ClockCircleOutlined 
} from '@ant-design/icons';
import i18n from '../../../locale';

const TunnelStatsCard = ({ stats }) => {
  const formatBytes = (bytes) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatTime = (timestamp) => {
    if (!timestamp) return '-';
    return new Date(timestamp * 1000).toLocaleString();
  };

  return (
    <Card title={i18n.t('TUNNEL.STATS.TITLE')} size="small">
      <Row gutter={16}>
        <Col span={6}>
          <Statistic
            title={i18n.t('TUNNEL.STATS.TOTAL_CONNECTIONS')}
            value={stats?.totalConnections || 0}
            prefix={<LinkOutlined />}
          />
        </Col>
        <Col span={6}>
          <Statistic
            title={i18n.t('TUNNEL.STATS.BYTES_IN')}
            value={formatBytes(stats?.totalBytesIn || 0)}
            prefix={<CloudDownloadOutlined />}
          />
        </Col>
        <Col span={6}>
          <Statistic
            title={i18n.t('TUNNEL.STATS.BYTES_OUT')}
            value={formatBytes(stats?.totalBytesOut || 0)}
            prefix={<CloudUploadOutlined />}
          />
        </Col>
        <Col span={6}>
          <Statistic
            title={i18n.t('TUNNEL.STATS.LAST_CONNECTION')}
            value={formatTime(stats?.lastConnectionTime)}
            prefix={<ClockCircleOutlined />}
          />
        </Col>
      </Row>
    </Card>
  );
};

export default TunnelStatsCard;
