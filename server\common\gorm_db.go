package common

import (
	"fmt"
	"log"
	"os"
	"path/filepath"
	"time"

	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	"github.com/glebarez/sqlite"
	"Spark/server/model"
)

var (
	// GormDB 是GORM数据库连接
	GormDB *gorm.DB
)

// InitGormDatabase 初始化GORM数据库
func InitGormDatabase(dbPath string) error {
	// 确保数据库目录存在
	dbDir := filepath.Dir(dbPath)
	if err := os.MkdirAll(dbDir, 0755); err != nil {
		return fmt.Errorf("创建数据库目录失败: %v", err)
	}

	// 配置GORM日志
	newLogger := logger.New(
		log.New(os.Stdout, "\r\n", log.LstdFlags), // io writer
		logger.Config{
			SlowThreshold:             5 * time.Second, // 慢SQL阈值提高到5秒
			LogLevel:                  logger.Error,    // 只记录错误日志
			IgnoreRecordNotFoundError: true,            // 忽略ErrRecordNotFound错误
			Colorful:                  false,           // 禁用彩色打印
		},
	)

	// 连接数据库
	db, err := gorm.Open(sqlite.Open(dbPath), &gorm.Config{
		Logger: newLogger,
	})
	if err != nil {
		return fmt.Errorf("连接数据库失败: %v", err)
	}

	// 获取底层sql.DB以配置连接池
	sqlDB, err := db.DB()
	if err != nil {
		return fmt.Errorf("获取底层数据库连接失败: %v", err)
	}

	// 设置数据库连接参数
	sqlDB.SetMaxOpenConns(10)
	sqlDB.SetMaxIdleConns(5)
	sqlDB.SetConnMaxLifetime(time.Hour)

	// 自动迁移数据库表
	if err := autoMigrate(db); err != nil {
		return fmt.Errorf("数据库迁移失败: %v", err)
	}

	GormDB = db
	log.Printf("GORM数据库初始化成功: %s", dbPath)
	return nil
}

// autoMigrate 自动迁移数据库表
func autoMigrate(db *gorm.DB) error {
	// 迁移设备表
	if err := db.AutoMigrate(&model.Device{}); err != nil {
		return fmt.Errorf("迁移设备表失败: %v", err)
	}

	// 迁移隧道表
	if err := db.AutoMigrate(&model.Tunnel{}); err != nil {
		return fmt.Errorf("迁移隧道表失败: %v", err)
	}

	log.Println("数据库表迁移完成")
	return nil
}

// GetGormDB 获取GORM数据库连接
func GetGormDB() *gorm.DB {
	return GormDB
}

// CloseGormDatabase 关闭GORM数据库连接
func CloseGormDatabase() {
	if GormDB != nil {
		sqlDB, err := GormDB.DB()
		if err == nil {
			sqlDB.Close()
		}
		GormDB = nil
	}
}
