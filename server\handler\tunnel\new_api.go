package tunnel

import (
	"fmt"
	"log"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"Spark/server/model"
	"Spark/server/service"
	"Spark/server/common"
	"Spark/modules"
	"gorm.io/gorm"
)

var (
	tunnelService *service.TunnelService
)

// InitNewAPI 初始化新的API服务
func InitNewAPI(db *gorm.DB) {
	tunnelService = service.NewTunnelService(db)
	// deviceService 在 device_events.go 中已经声明和初始化
}

// CreateTunnel 创建隧道
func CreateTunnel(ctx *gin.Context) {
	deviceUUID := ctx.Param("device_uuid")
	if deviceUUID == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"message": "设备UUID不能为空",
		})
		return
	}

	var config model.TunnelConfig
	if err := ctx.ShouldBindJSON(&config); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"message": "请求参数错误: " + err.Error(),
		})
		return
	}

	tunnel, err := tunnelService.CreateTunnel(deviceUUID, &config)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code": 500,
			"message": err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 200,
		"message": "隧道创建成功",
		"data": tunnel,
	})
}

// ListTunnels 获取隧道列表
func ListTunnels(ctx *gin.Context) {
	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("page_size", "10"))

	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 10
	}

	tunnels, total, err := tunnelService.GetTunnels(page, pageSize)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code": 500,
			"message": err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 200,
		"message": "获取成功",
		"data": gin.H{
			"list":      tunnels,
			"total":     total,
			"page":      page,
			"page_size": pageSize,
		},
	})
}

// UpdateTunnel 更新隧道
func UpdateTunnel(ctx *gin.Context) {
	tunnelID := ctx.Param("tunnel_id")
	if tunnelID == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"message": "隧道ID不能为空",
		})
		return
	}

	var config model.TunnelConfig
	if err := ctx.ShouldBindJSON(&config); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"message": "请求参数错误: " + err.Error(),
		})
		return
	}

	if err := tunnelService.UpdateTunnel(tunnelID, &config); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code": 500,
			"message": err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 200,
		"message": "隧道更新成功",
	})
}

// DeleteTunnel 删除隧道
func DeleteTunnel(ctx *gin.Context) {
	tunnelID := ctx.Param("tunnel_id")
	if tunnelID == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"message": "隧道ID不能为空",
		})
		return
	}

	log.Printf("尝试删除隧道: %s", tunnelID)

	if err := tunnelService.DeleteTunnel(tunnelID); err != nil {
		log.Printf("删除隧道失败: %v", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code": 500,
			"message": err.Error(),
		})
		return
	}

	log.Printf("隧道删除成功: %s", tunnelID)
	ctx.JSON(http.StatusOK, gin.H{
		"code": 200,
		"message": "隧道删除成功",
	})
}

// ToggleTunnel 切换隧道状态
func ToggleTunnel(ctx *gin.Context) {
	tunnelID := ctx.Param("tunnel_id")
	if tunnelID == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"message": "隧道ID不能为空",
		})
		return
	}

	if err := tunnelService.ToggleTunnel(tunnelID); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code": 500,
			"message": err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 200,
		"message": "隧道状态切换成功",
	})
}

// GetDevices 获取设备列表
func GetDevices(ctx *gin.Context) {
	status := ctx.Query("status")
	search := ctx.Query("search")
	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("page_size", "10"))

	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 10
	}

	devices, total, err := deviceService.GetDevices(page, pageSize, status, search)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code": 500,
			"message": err.Error(),
		})
		return
	}

	// 增强设备数据：合并数据库数据和实时数据
	enhancedDevices := enhanceDevicesWithRealTimeData(devices)

	ctx.JSON(http.StatusOK, gin.H{
		"code": 200,
		"message": "获取成功",
		"data": gin.H{
			"list":      enhancedDevices,
			"total":     total,
			"page":      page,
			"page_size": pageSize,
		},
	})
}

// GetDevice 获取设备详情
func GetDevice(ctx *gin.Context) {
	deviceUUID := ctx.Param("device_uuid")
	if deviceUUID == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"message": "设备UUID不能为空",
		})
		return
	}

	device, err := deviceService.GetDevice(deviceUUID)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code": 500,
			"message": err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 200,
		"message": "获取成功",
		"data": device,
	})
}

// UpdateDeviceRemark 更新设备备注
func UpdateDeviceRemark(ctx *gin.Context) {
	deviceUUID := ctx.Param("device_uuid")
	if deviceUUID == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"message": "设备UUID不能为空",
		})
		return
	}

	var req struct {
		Remark string `json:"remark"`
	}
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"message": "请求参数错误: " + err.Error(),
		})
		return
	}

	// 使用GORM系统更新备注
	if err := deviceService.UpdateDeviceRemark(deviceUUID, req.Remark); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code": 500,
			"message": err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 200,
		"message": "备注更新成功",
	})
}

// GetDeviceNotes 获取设备笔记
func GetDeviceNotes(ctx *gin.Context) {
	deviceUUID := ctx.Param("device_uuid")
	if deviceUUID == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"message": "设备UUID不能为空",
		})
		return
	}

	notes, err := deviceService.GetDeviceNotes(deviceUUID)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code": 500,
			"message": err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 200,
		"message": "获取成功",
		"data": gin.H{
			"notes": notes,
		},
	})
}

// UpdateDeviceNotes 更新设备笔记
func UpdateDeviceNotes(ctx *gin.Context) {
	deviceUUID := ctx.Param("device_uuid")
	if deviceUUID == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"message": "设备UUID不能为空",
		})
		return
	}

	var req struct {
		Notes string `json:"notes"`
	}
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"message": "请求参数错误: " + err.Error(),
		})
		return
	}

	if err := deviceService.UpdateDeviceNotes(deviceUUID, req.Notes); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code": 500,
			"message": err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 200,
		"message": "笔记更新成功",
	})
}

// GetTunnel 获取隧道详情
func GetTunnel(ctx *gin.Context) {
	tunnelID := ctx.Param("tunnel_id")
	if tunnelID == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"message": "隧道ID不能为空",
		})
		return
	}

	tunnel, err := tunnelService.GetTunnel(tunnelID)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code": 500,
			"message": err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 200,
		"message": "获取成功",
		"data": tunnel,
	})
}

// GetAvailablePorts 获取可用端口
func GetAvailablePorts(ctx *gin.Context) {
	// 返回可用端口范围
	ctx.JSON(http.StatusOK, gin.H{
		"code": 200,
		"message": "获取成功",
		"data": gin.H{
			"min_port": 10000,
			"max_port": 65535,
			"available_count": 55535,
		},
	})
}

// GetPortStats 获取端口统计
func GetPortStats(ctx *gin.Context) {
	// 获取端口使用统计
	tunnels, _, err := tunnelService.GetTunnels(1, 1000)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code": 500,
			"message": err.Error(),
		})
		return
	}

	usedPorts := make([]int, 0)
	activeTunnels := 0
	for _, tunnel := range tunnels {
		usedPorts = append(usedPorts, tunnel.ServerPort)
		if tunnel.Status == "active" {
			activeTunnels++
		}
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 200,
		"message": "获取成功",
		"data": gin.H{
			"total_tunnels": len(tunnels),
			"active_tunnels": activeTunnels,
			"used_ports": usedPorts,
		},
	})
}

// enhanceDevicesWithRealTimeData 增强设备数据，合并数据库数据和实时数据
func enhanceDevicesWithRealTimeData(devices []model.Device) []gin.H {
	result := make([]gin.H, 0, len(devices))

	// 获取内存中的在线设备数据，按设备特征建立映射
	onlineDevicesMap := make(map[string]*modules.Device) // key: hostname_username
	onlineDevicesByUUID := make(map[string]*modules.Device) // key: uuid
	common.Devices.IterCb(func(uuid string, device *modules.Device) bool {
		deviceKey := fmt.Sprintf("%s_%s", device.Hostname, device.Username)
		onlineDevicesMap[deviceKey] = device
		onlineDevicesByUUID[uuid] = device
		return true
	})

	for _, device := range devices {
		deviceData := gin.H{
			"id":          device.ID,          // 数据库自增ID
			"uuid":        device.UUID,
			"hostname":    device.Hostname,
			"username":    device.Username,
			"os":          device.OS,
			"arch":        device.Arch,
			"ip_address":  device.IPAddress,
			"mac_address": device.MACAddress,
			"remark":      device.Remark,
			"last_seen":   device.LastSeen,
			"first_seen":  device.FirstSeen,
			"created_at":  device.CreatedAt,
			"updated_at":  device.UpdatedAt,
		}

		// 检查设备是否在线：先按UUID查找，再按设备特征查找
		deviceKey := fmt.Sprintf("%s_%s", device.Hostname, device.Username)
		var onlineDevice *modules.Device
		var isOnline bool

		// 优先按UUID查找
		if onlineDevice, isOnline = onlineDevicesByUUID[device.UUID]; !isOnline {
			// UUID查找失败，按设备特征查找
			onlineDevice, isOnline = onlineDevicesMap[deviceKey]
		}

		if isOnline {
			// 设备在线，添加实时数据
			deviceData["device_id"] = onlineDevice.ID // 客户端设备ID（用于连接）
			deviceData["status"] = "online"
			deviceData["wan"] = onlineDevice.WAN
			deviceData["lan"] = onlineDevice.LAN
			deviceData["mac"] = onlineDevice.MAC
			deviceData["latency"] = onlineDevice.Latency
			deviceData["uptime"] = onlineDevice.Uptime
			deviceData["cpu"] = onlineDevice.CPU
			deviceData["ram"] = onlineDevice.RAM
			deviceData["disk"] = onlineDevice.Disk
			deviceData["net"] = onlineDevice.Net
			deviceData["conn"] = device.UUID // 兼容前端
		} else {
			// 设备离线
			deviceData["device_id"] = device.UUID // 客户端设备ID（用于连接）
			deviceData["status"] = "offline"
			deviceData["wan"] = device.IPAddress
			deviceData["lan"] = device.IPAddress
			deviceData["mac"] = ""
			deviceData["latency"] = 0
			deviceData["uptime"] = 0
			deviceData["cpu"] = gin.H{"usage": 0, "model": "Unknown"}
			deviceData["ram"] = gin.H{"usage": 0, "total": 0, "used": 0}
			deviceData["disk"] = gin.H{"usage": 0, "total": 0, "used": 0}
			deviceData["net"] = gin.H{"sent": 0, "recv": 0}
			deviceData["conn"] = device.UUID // 兼容前端
		}

		result = append(result, deviceData)
	}

	return result
}


