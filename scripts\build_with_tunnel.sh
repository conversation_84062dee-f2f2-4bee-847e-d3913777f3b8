#!/bin/bash

# Spark 隧道功能构建脚本

echo "=== 构建 Spark (包含隧道功能) ==="

# 检查依赖
check_dependencies() {
    echo "检查构建依赖..."
    
    # 检查 Go
    if ! command -v go &> /dev/null; then
        echo "✗ Go 未安装"
        exit 1
    fi
    echo "✓ Go 版本: $(go version)"
    
    # 检查 Node.js
    if ! command -v node &> /dev/null; then
        echo "✗ Node.js 未安装"
        exit 1
    fi
    echo "✓ Node.js 版本: $(node --version)"
    
    # 检查 npm
    if ! command -v npm &> /dev/null; then
        echo "✗ npm 未安装"
        exit 1
    fi
    echo "✓ npm 版本: $(npm --version)"
}

# 构建前端
build_frontend() {
    echo "构建前端..."
    
    cd web
    
    # 安装依赖
    echo "安装前端依赖..."
    if npm install; then
        echo "✓ 前端依赖安装成功"
    else
        echo "✗ 前端依赖安装失败"
        exit 1
    fi
    
    # 构建生产版本
    echo "构建前端生产版本..."
    if npm run build-prod; then
        echo "✓ 前端构建成功"
    else
        echo "✗ 前端构建失败"
        exit 1
    fi
    
    cd ..
}

# 嵌入静态资源
embed_static() {
    echo "嵌入静态资源..."
    
    # 检查 statik 工具
    if ! command -v statik &> /dev/null; then
        echo "安装 statik 工具..."
        if go install github.com/rakyll/statik@latest; then
            echo "✓ statik 工具安装成功"
        else
            echo "✗ statik 工具安装失败"
            exit 1
        fi
    fi
    
    # 嵌入静态资源
    if statik -m -src="./web/dist" -f -dest="./server/embed" -p web -ns web; then
        echo "✓ 静态资源嵌入成功"
    else
        echo "✗ 静态资源嵌入失败"
        exit 1
    fi
}

# 构建服务端
build_server() {
    echo "构建服务端..."
    
    cd server
    
    # 下载依赖
    echo "下载 Go 模块依赖..."
    if go mod tidy; then
        echo "✓ Go 依赖下载成功"
    else
        echo "✗ Go 依赖下载失败"
        exit 1
    fi
    
    # 构建服务端
    echo "编译服务端..."
    if go build -o ../spark-server .; then
        echo "✓ 服务端构建成功"
    else
        echo "✗ 服务端构建失败"
        exit 1
    fi
    
    cd ..
}

# 构建客户端
build_client() {
    echo "构建客户端..."
    
    cd client
    
    # 下载依赖
    echo "下载 Go 模块依赖..."
    if go mod tidy; then
        echo "✓ Go 依赖下载成功"
    else
        echo "✗ Go 依赖下载失败"
        exit 1
    fi
    
    # 构建客户端
    echo "编译客户端..."
    if go build -o ../spark-client .; then
        echo "✓ 客户端构建成功"
    else
        echo "✗ 客户端构建失败"
        exit 1
    fi
    
    cd ..
}

# 创建配置文件
create_config() {
    echo "创建配置文件..."
    
    if [ ! -f "config.json" ]; then
        cat > config.json << EOF
{
    "listen": ":8000",
    "salt": "$(openssl rand -hex 16)",
    "auth": {
        "admin": "$(openssl rand -base64 12)"
    },
    "log": {
        "level": "info",
        "path": "./logs",
        "days": 7
    },
    "database": {
        "path": "./spark.db"
    }
}
EOF
        echo "✓ 配置文件创建成功"
        echo "  默认用户名: admin"
        echo "  默认密码: $(grep -o '"admin": "[^"]*"' config.json | cut -d'"' -f4)"
    else
        echo "✓ 配置文件已存在"
    fi
}

# 运行测试
run_tests() {
    echo "运行基本测试..."
    
    # 检查服务端是否能正常启动
    echo "测试服务端启动..."
    timeout 5s ./spark-server &
    server_pid=$!
    sleep 2
    
    if kill -0 $server_pid 2>/dev/null; then
        echo "✓ 服务端启动正常"
        kill $server_pid
        wait $server_pid 2>/dev/null
    else
        echo "✗ 服务端启动失败"
    fi
    
    # 检查客户端是否能正常启动
    echo "测试客户端启动..."
    if timeout 2s ./spark-client --help > /dev/null 2>&1; then
        echo "✓ 客户端启动正常"
    else
        echo "⚠ 客户端测试跳过 (需要配置)"
    fi
}

# 清理构建产物
clean() {
    echo "清理构建产物..."
    rm -f spark-server spark-client
    rm -rf web/dist web/node_modules
    rm -rf server/embed/web
    echo "✓ 清理完成"
}

# 显示帮助
show_help() {
    echo "用法: $0 [选项]"
    echo "选项:"
    echo "  build    构建项目 (默认)"
    echo "  clean    清理构建产物"
    echo "  test     运行测试"
    echo "  help     显示帮助"
}

# 主构建流程
build_all() {
    echo "开始构建..."
    echo
    
    check_dependencies
    echo
    
    build_frontend
    echo
    
    embed_static
    echo
    
    build_server
    echo
    
    build_client
    echo
    
    create_config
    echo
    
    run_tests
    echo
    
    echo "=== 构建完成 ==="
    echo "服务端: ./spark-server"
    echo "客户端: ./spark-client"
    echo "配置文件: ./config.json"
    echo
    echo "启动服务端: ./spark-server"
    echo "访问地址: http://localhost:8000"
}

# 处理命令行参数
case "${1:-build}" in
    "build")
        build_all
        ;;
    "clean")
        clean
        ;;
    "test")
        if [ -f "./spark-server" ]; then
            chmod +x scripts/test_tunnel.sh
            ./scripts/test_tunnel.sh
        else
            echo "请先构建项目: $0 build"
        fi
        ;;
    "help")
        show_help
        ;;
    *)
        echo "未知选项: $1"
        show_help
        exit 1
        ;;
esac
