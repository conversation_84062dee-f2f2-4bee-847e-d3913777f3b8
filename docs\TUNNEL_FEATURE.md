# Spark 隧道功能使用指南

## 概述

Spark 隧道功能是基于 FRP 和 AnyProxy 设计理念实现的内网穿透解决方案，允许您通过 Spark 服务器安全地访问内网服务。

## 功能特性

### 🚀 支持的隧道类型

1. **TCP 隧道**
   - 直接端口映射
   - 适用于 SSH、数据库、RDP 等服务
   - 支持自动端口分配

2. **HTTP 隧道**
   - Web 服务代理
   - 支持子域名访问
   - 支持自定义域名

3. **SOCKS5 隧道**
   - 通用代理协议
   - 支持任意 TCP 连接
   - 适用于浏览器代理

### 🔧 核心功能

- ✅ **统一管理**: 与设备管理深度集成
- ✅ **动态端口分配**: 自动分配可用端口
- ✅ **实时监控**: 连接状态和流量统计
- ✅ **安全控制**: IP 白名单和认证机制
- ✅ **高性能**: 基于 WebSocket 长连接

## 快速开始

### 1. 构建项目

```bash
# 克隆项目
git clone <spark-repo>
cd Spark

# 构建项目（包含隧道功能）
chmod +x scripts/build_with_tunnel.sh
./scripts/build_with_tunnel.sh build
```

### 2. 启动服务

```bash
# 启动服务端
./spark-server

# 访问 Web 界面
open http://localhost:8000
```

### 3. 连接客户端

```bash
# 在目标设备上运行客户端
./spark-client -server ws://your-server:8000/ws
```

## 使用教程

### 创建 TCP 隧道

1. 在 Web 界面中点击 "隧道管理"
2. 点击 "创建隧道"
3. 填写隧道信息：
   - **隧道名称**: SSH 隧道
   - **隧道类型**: TCP
   - **目标设备**: 选择在线设备
   - **本地主机**: localhost
   - **本地端口**: 22
   - **远程端口**: 留空自动分配
4. 点击 "创建隧道"

创建成功后，您可以通过以下方式访问：
```bash
ssh user@your-server -p 20001
```

### 创建 HTTP 隧道

1. 创建隧道时选择 "HTTP" 类型
2. 配置参数：
   - **本地端口**: 80 (Web 服务端口)
   - **子域名**: myapp
3. 创建后可通过以下地址访问：
   ```
   http://myapp.tunnel.your-server.com
   ```

### 创建 SOCKS5 隧道

1. 选择 "SOCKS5" 类型
2. 配置本地 SOCKS5 服务端口
3. 在浏览器中配置代理：
   ```
   SOCKS5: your-server:30001
   ```

## API 接口

### 隧道管理

```bash
# 获取隧道列表
GET /api/tunnels

# 创建隧道
POST /api/tunnels
{
  "device_id": "device-uuid",
  "name": "隧道名称",
  "type": "tcp",
  "local_host": "localhost",
  "local_port": 22,
  "auto_start": true
}

# 启动隧道
POST /api/tunnels/{id}/start

# 停止隧道
POST /api/tunnels/{id}/stop

# 删除隧道
DELETE /api/tunnels/{id}
```

### 端口管理

```bash
# 获取可用端口
GET /api/ports/available?type=tcp&count=10

# 获取端口统计
GET /api/ports/stats
```

## 配置说明

### 端口范围配置

默认端口分配范围：
- TCP 隧道: 20000-25000
- HTTP 隧道: 25001-30000
- SOCKS5 隧道: 30001-35000

### 安全配置

```json
{
  "auth_required": true,
  "auth_username": "user",
  "auth_password": "password",
  "allowed_ips": ["***********/24"]
}
```

## 故障排除

### 常见问题

1. **隧道创建失败**
   - 检查设备是否在线
   - 确认端口未被占用
   - 查看服务端日志

2. **连接超时**
   - 检查防火墙设置
   - 确认网络连通性
   - 验证端口配置

3. **性能问题**
   - 检查网络带宽
   - 监控服务器资源
   - 优化隧道数量

### 日志查看

```bash
# 服务端日志
tail -f logs/spark.log

# 客户端日志
./spark-client -debug
```

## 性能优化

### 服务端优化

1. **资源配置**
   ```bash
   # 增加文件描述符限制
   ulimit -n 65536
   
   # 优化网络参数
   echo 'net.core.rmem_max = 16777216' >> /etc/sysctl.conf
   echo 'net.core.wmem_max = 16777216' >> /etc/sysctl.conf
   ```

2. **负载均衡**
   - 使用多个服务端实例
   - 配置反向代理
   - 实现会话粘性

### 客户端优化

1. **连接池配置**
   - 调整并发连接数
   - 优化重连策略
   - 配置心跳间隔

## 安全建议

1. **网络安全**
   - 使用 HTTPS/WSS 加密传输
   - 配置防火墙规则
   - 限制访问 IP 范围

2. **认证授权**
   - 启用隧道认证
   - 定期更换密码
   - 实施访问审计

3. **监控告警**
   - 监控异常连接
   - 设置流量告警
   - 记录访问日志

## 开发指南

### 扩展隧道类型

1. 实现协议处理器
2. 添加前端界面
3. 更新 API 接口
4. 编写测试用例

### 贡献代码

1. Fork 项目
2. 创建功能分支
3. 提交 Pull Request
4. 通过代码审查

## 许可证

本项目基于 BSD-2-Clause License 开源协议发布。

## 支持

- 问题反馈: [GitHub Issues]
- 文档: [项目文档]
- 社区: [讨论区]
