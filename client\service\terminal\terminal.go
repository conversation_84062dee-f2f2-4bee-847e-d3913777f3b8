package terminal

import (
	"errors"
	"Spark/client/common"
)

var (
	errDataNotFound = errors.New(`no input found in packet`)
	errDataInvalid  = errors.New(`can not parse data in packet`)
	errUUIDNotFound = errors.New(`can not find terminal identifier`)
)

// packet explanation:

// +---------+---------+----------+-------------+------+
// | magic   | op code | event id | data length | data |
// +---------+---------+----------+-------------+------+
// | 5 bytes | 1 byte  | 16 bytes | 2 bytes     | -    |
// +---------+---------+----------+-------------+------+

// magic:
// []byte{34, 22, 19, 17, 21}

// op code:
// 00: binary packet
// 01: JSON packet

// Init 初始化终端服务
func Init(conn *common.Conn) {
	// 初始化终端服务相关资源
}

// Cleanup 清理终端服务资源
func Cleanup() {
	// 清理终端服务相关资源
}
