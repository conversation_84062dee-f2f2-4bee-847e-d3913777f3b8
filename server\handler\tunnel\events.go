package tunnel

import (
	"Spark/modules"
	"Spark/server/common"
	"Spark/server/service"
	"Spark/utils/melody"
	"fmt"

	"github.com/kataras/golog"
)

// WebSocket 事件定义
const (
	// 服务端 -> 客户端
	EventTunnelCreate     = "TUNNEL_CREATE"      // 创建隧道
	EventTunnelStart      = "TUNNEL_START"       // 启动隧道
	EventTunnelStop       = "TUNNEL_STOP"        // 停止隧道
	EventTunnelDelete     = "TUNNEL_DELETE"      // 删除隧道
	EventTunnelUpdate     = "TUNNEL_UPDATE"      // 更新隧道配置

	// 连接管理
	EventConnectionNew    = "TUNNEL_CONNECTION_NEW"    // 新连接
	EventConnectionData   = "TUNNEL_CONNECTION_DATA"   // 连接数据
	EventConnectionClose  = "TUNNEL_CONNECTION_CLOSE"  // 关闭连接

	// 客户端 -> 服务端
	EventTunnelStatus     = "TUNNEL_STATUS"      // 隧道状态更新
	EventConnectionAccept = "TUNNEL_CONNECTION_ACCEPT"  // 接受连接
	EventConnectionReject = "TUNNEL_CONNECTION_REJECT"  // 拒绝连接
	EventConnectionResponse = "TUNNEL_CONNECTION_RESPONSE" // 连接响应

	// 双向事件
	EventTunnelStats      = "TUNNEL_STATS"       // 统计信息
	EventTunnelHeartbeat  = "TUNNEL_HEARTBEAT"   // 心跳检测
)

// 事件数据结构
type TunnelCreateEvent struct {
	TunnelID string `json:"tunnel_id"`
	Config   string `json:"config"`
}

type TunnelStatusEvent struct {
	TunnelID string `json:"tunnel_id"`
	Status   string `json:"status"`
	Error    string `json:"error,omitempty"`
}

type ConnectionEvent struct {
	TunnelID     string `json:"tunnel_id"`
	ConnectionID string `json:"connection_id"`
	ClientIP     string `json:"client_ip,omitempty"`
	ClientPort   int    `json:"client_port,omitempty"`
	Data         []byte `json:"data,omitempty"`
	Reason       string `json:"reason,omitempty"`
	Action       string `json:"action,omitempty"`
}

// HandleTunnelStatus 处理隧道状态更新
func HandleTunnelStatus(pack modules.Packet, session *melody.Session) {
	tunnelID, _ := pack.GetDataString("tunnel_id")
	status, _ := pack.GetDataString("status")
	errorMsg, _ := pack.GetDataString("error")

	golog.Infof("收到隧道状态更新 [ID=%s, Status=%s]", tunnelID, status)

	// TODO: 更新数据库中的状态
	// 这里可以添加数据库状态更新逻辑

	// 如果有错误，记录日志
	if errorMsg != "" {
		golog.Errorf("隧道状态错误 [ID=%s]: %s", tunnelID, errorMsg)
	}
}

// HandleConnectionResponse 处理连接响应
func HandleConnectionResponse(pack modules.Packet, session *melody.Session) {
	connectionID, _ := pack.GetDataString("connection_id")
	action, _ := pack.GetDataString("action")
	reason, _ := pack.GetDataString("reason")

	golog.Infof("收到连接响应 [ConnID=%s, Action=%s]", connectionID, action)

	if action == "reject" {
		golog.Warnf("客户端拒绝连接 [ConnID=%s]: %s", connectionID, reason)
	}

	// 触发等待的事件处理器
	common.CallEvent(modules.Packet{
		Act:   EventConnectionResponse,
		Event: connectionID,
		Data: map[string]any{
			"action": action,
			"reason": reason,
		},
	}, session)
}

// HandleTunnelConnectionData 处理隧道连接数据
func HandleTunnelConnectionData(pack modules.Packet, session *melody.Session) {
	connectionID, _ := pack.GetDataString("connection_id")
	data, _ := pack.GetDataBytes("data")

	if len(data) == 0 {
		return
	}

	// 触发数据处理事件
	common.CallEvent(modules.Packet{
		Act:   EventConnectionData,
		Event: connectionID,
		Data: map[string]any{
			"data": data,
		},
	}, session)
}

// HandleTunnelConnectionClose 处理隧道连接关闭
func HandleTunnelConnectionClose(pack modules.Packet, session *melody.Session) {
	connectionID, _ := pack.GetDataString("connection_id")
	tunnelID, _ := pack.GetDataString("tunnel_id")

	golog.Infof("客户端关闭连接 [TunnelID=%s, ConnID=%s]", tunnelID, connectionID)

	// 触发连接关闭事件
	common.CallEvent(modules.Packet{
		Act:   EventConnectionClose,
		Event: connectionID,
		Data: map[string]any{
			"tunnel_id":     tunnelID,
			"connection_id": connectionID,
		},
	}, session)
}

// SendTunnelCreateCommand 发送隧道创建命令
func SendTunnelCreateCommand(tunnelID string, config string, session *melody.Session) error {
	success := common.SendPack(modules.Packet{
		Act:   EventTunnelCreate,
		Event: tunnelID,
		Data: map[string]any{
			"tunnel_id": tunnelID,
			"config":    config,
		},
	}, session)

	if !success {
		return fmt.Errorf("发送隧道创建命令失败")
	}
	return nil
}

// SendTunnelStartCommand 发送隧道启动命令
func SendTunnelStartCommand(tunnelID string, session *melody.Session) error {
	success := common.SendPack(modules.Packet{
		Act:   EventTunnelStart,
		Event: tunnelID,
		Data: map[string]any{
			"tunnel_id": tunnelID,
		},
	}, session)

	if !success {
		return fmt.Errorf("发送隧道启动命令失败")
	}
	return nil
}

// SendTunnelStopCommand 发送隧道停止命令
func SendTunnelStopCommand(tunnelID string, session *melody.Session) error {
	success := common.SendPack(modules.Packet{
		Act:   EventTunnelStop,
		Event: tunnelID,
		Data: map[string]any{
			"tunnel_id": tunnelID,
		},
	}, session)

	if !success {
		return fmt.Errorf("发送隧道停止命令失败")
	}
	return nil
}

// SendTunnelDeleteCommand 发送隧道删除命令
func SendTunnelDeleteCommand(tunnelID string, session *melody.Session) error {
	success := common.SendPack(modules.Packet{
		Act:   EventTunnelDelete,
		Event: tunnelID,
		Data: map[string]any{
			"tunnel_id": tunnelID,
		},
	}, session)

	if !success {
		return fmt.Errorf("发送隧道删除命令失败")
	}
	return nil
}

// SendConnectionNewEvent 发送新连接事件
func SendConnectionNewEvent(tunnelID, connectionID, clientIP string, clientPort int, session *melody.Session) error {
	success := common.SendPack(modules.Packet{
		Act:   EventConnectionNew,
		Event: connectionID,
		Data: map[string]any{
			"tunnel_id":     tunnelID,
			"connection_id": connectionID,
			"client_ip":     clientIP,
			"client_port":   clientPort,
		},
	}, session)

	if !success {
		return fmt.Errorf("发送新连接事件失败")
	}
	return nil
}

// SendConnectionDataEvent 发送连接数据事件
func SendConnectionDataEvent(tunnelID, connectionID string, data []byte, session *melody.Session) error {
	success := common.SendPack(modules.Packet{
		Act:   EventConnectionData,
		Event: connectionID,
		Data: map[string]any{
			"tunnel_id":     tunnelID,
			"connection_id": connectionID,
			"data":          data,
		},
	}, session)

	if !success {
		return fmt.Errorf("发送连接数据事件失败")
	}
	return nil
}

// SendConnectionCloseEvent 发送连接关闭事件
func SendConnectionCloseEvent(tunnelID, connectionID string, session *melody.Session) error {
	success := common.SendPack(modules.Packet{
		Act:   EventConnectionClose,
		Event: connectionID,
		Data: map[string]any{
			"tunnel_id":     tunnelID,
			"connection_id": connectionID,
		},
	}, session)

	if !success {
		return fmt.Errorf("发送连接关闭事件失败")
	}
	return nil
}

// InitTunnelEventHandlers 初始化隧道事件处理器
func InitTunnelEventHandlers() {
	// 注册隧道状态更新处理器
	common.AddEventHandler(modules.Packet{
		Act: EventTunnelStatus,
	}, HandleTunnelStatus)

	// 注册连接响应处理器
	common.AddEventHandler(modules.Packet{
		Act: EventConnectionResponse,
	}, HandleConnectionResponse)

	// 注册连接数据处理器
	common.AddEventHandler(modules.Packet{
		Act: EventConnectionData,
	}, HandleTunnelConnectionData)

	// 注册连接关闭处理器
	common.AddEventHandler(modules.Packet{
		Act: EventConnectionClose,
	}, HandleTunnelConnectionClose)

	golog.Info("隧道事件处理器初始化完成")
}

// HandleSOCKS5ConnectResponse 处理SOCKS5连接响应
func HandleSOCKS5ConnectResponse(pack modules.Packet, session *melody.Session) {
	golog.Infof("收到SOCKS5连接响应事件: %+v", pack)

	tunnelID, _ := pack.GetDataString("tunnel_id")
	connID, _ := pack.GetDataString("connection_id")
	success, _ := pack.GetDataBool("success")
	errorMsg, _ := pack.GetDataString("error")

	golog.Infof("解析响应数据: TunnelID=%s, ConnID=%s, Success=%v, Error=%s", tunnelID, connID, success, errorMsg)

	if success {
		golog.Infof("SOCKS5连接建立成功 [TunnelID=%s, ConnID=%s]", tunnelID, connID)
	} else {
		golog.Errorf("SOCKS5连接建立失败 [TunnelID=%s, ConnID=%s]: %s", tunnelID, connID, errorMsg)
	}

	// 通知等待的连接
	if err := service.NotifyConnectionResponse(tunnelID, connID, success); err != nil {
		golog.Errorf("通知SOCKS5连接响应失败 [TunnelID=%s, ConnID=%s]: %v", tunnelID, connID, err)
	} else {
		golog.Infof("SOCKS5连接响应通知成功 [TunnelID=%s, ConnID=%s]", tunnelID, connID)
	}
}

// HandleSOCKS5TunnelDataFromDevice 处理来自设备的SOCKS5隧道数据
func HandleSOCKS5TunnelDataFromDevice(pack modules.Packet, session *melody.Session) {
	tunnelID, _ := pack.GetDataString("tunnel_id")
	connID, _ := pack.GetDataString("connection_id")
	data, _ := pack.GetDataBytes("data")

	golog.Debugf("收到SOCKS5隧道数据 [TunnelID=%s, ConnID=%s, Size=%d]", tunnelID, connID, len(data))

	// 转发数据给对应的SOCKS5客户端连接
	if err := service.ForwardDataToClient(tunnelID, connID, data); err != nil {
		golog.Errorf("转发SOCKS5数据失败 [TunnelID=%s, ConnID=%s]: %v", tunnelID, connID, err)
	}
}

// HandleSOCKS5TunnelData 处理SOCKS5隧道数据（兼容旧版本）
func HandleSOCKS5TunnelData(pack modules.Packet, session *melody.Session) {
	// 兼容处理，可以根据需要实现
	golog.Debug("收到SOCKS5隧道数据事件")
}

// forwardDataToSOCKS5Client 转发数据到SOCKS5客户端连接
func forwardDataToSOCKS5Client(tunnelID, targetAddr string, data []byte) error {
	// 导入service包以访问SOCKS5隧道管理器
	return service.ForwardDataToSOCKS5Client(tunnelID, targetAddr, data)
}
