package proxy

import (
	"Spark/modules"
	"Spark/server/common"
	"fmt"
	"net"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/kataras/golog"
)

// CreateSocks5TunnelForm 创建SOCKS5隧道的请求表单
type CreateSocks5TunnelForm struct {
	DeviceID string `json:"device_id" binding:"required"`
	Port     int    `json:"port" binding:"required"`
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
}

// ListSocks5Tunnels 获取所有SOCKS5隧道
func ListSocks5Tunnels(ctx *gin.Context) {
	deviceID := ctx.Param("deviceId")
	
	var tunnels []*common.Socks5Tunnel
	var err error
	
	if deviceID != "" {
		tunnels, err = common.GetSocks5TunnelsByDevice(deviceID)
	} else {
		tunnels, err = common.GetAllSocks5Tunnels()
	}
	
	if err != nil {
		golog.Error(err)
		ctx.JSON(http.StatusInternalServerError, modules.Packet{
			Code: 1,
			Msg:  "获取SOCKS5隧道列表失败: " + err.Error(),
		})
		return
	}
	
	ctx.JSON(http.StatusOK, modules.Packet{
		Code: 0,
		Data: map[string]any{
			"tunnels": tunnels,
		},
	})
}

// CreateSocks5Tunnel 创建SOCKS5隧道
func CreateSocks5Tunnel(ctx *gin.Context) {
	var form CreateSocks5TunnelForm
	if err := ctx.ShouldBindJSON(&form); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": -1,
			"msg":  "参数错误: " + err.Error(),
		})
		return
	}

	// 验证设备是否存在且在线
	deviceFound := false
	common.Devices.IterCb(func(uuid string, device *modules.Device) bool {
		if device.ID == form.DeviceID {
			deviceFound = true
			return false
		}
		return true
	})

	if !deviceFound {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": -1,
			"msg":  "设备不存在或离线",
		})
		return
	}

	// 检查端口是否可用
	if !isPortAvailable(form.Port) {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": -1,
			"msg":  fmt.Sprintf("端口 %d 已被占用", form.Port),
		})
		return
	}

	// 检查是否已存在相同端口的隧道
	existingTunnels, err := common.GetSocks5TunnelsByDevice(form.DeviceID)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code": -1,
			"msg":  "获取隧道信息失败: " + err.Error(),
		})
		return
	}

	for _, tunnel := range existingTunnels {
		if tunnel.Port == form.Port {
			ctx.JSON(http.StatusBadRequest, gin.H{
				"code": -1,
				"msg":  fmt.Sprintf("该设备已存在端口为 %d 的隧道", form.Port),
			})
			return
		}
	}

	// 创建隧道记录
	now := time.Now().Unix()
	tunnel := &common.Socks5Tunnel{
		DeviceID:   form.DeviceID,
		Port:       form.Port,
		Username:   form.Username,
		Password:   form.Password,
		Status:     "pending",
		CreateTime: now,
		UpdateTime: now,
	}

	// 保存到数据库
	if err := common.AddSocks5Tunnel(tunnel); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code": -1,
			"msg":  "创建隧道失败: " + err.Error(),
		})
		return
	}

	// 向客户端发送创建隧道命令
	sent := false
	common.Devices.IterCb(func(uuid string, device *modules.Device) bool {
		if device.ID == form.DeviceID {
			session, ok := common.Melody.GetSessionByUUID(uuid)
			if ok {
				// 发送创建隧道的命令
				common.SendPack(modules.Packet{
					Event: "TUNNEL_CREATE",
					Data: map[string]interface{}{
						"tunnel_id": tunnel.ID,
						"port":      form.Port,
						"username":  form.Username,
						"password":  form.Password,
					},
				}, session)
				sent = true
			}
			return false
		}
		return true
	})

	if !sent {
		// 如果没有发送成功，更新隧道状态为错误
		_ = common.UpdateSocks5TunnelStatus(tunnel.ID, "error", "设备离线或无法发送命令")
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code": -1,
			"msg":  "无法发送命令到设备",
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 0,
		"msg":  "隧道创建成功，正在等待设备响应",
		"data": tunnel,
	})
}

// DeleteSocks5Tunnel 删除SOCKS5隧道
func DeleteSocks5Tunnel(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": -1,
			"msg":  "无效的隧道ID",
		})
		return
	}
	
	// 获取隧道信息
	tunnel, err := common.GetSocks5Tunnel(id)
	if err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{
			"code": -1,
			"msg":  "隧道不存在",
		})
		return
	}
	
	// 向客户端发送关闭隧道命令
	sessionFound := false
	common.Devices.IterCb(func(uuid string, device *modules.Device) bool {
		if device.ID == tunnel.DeviceID {
			session, ok := common.Melody.GetSessionByUUID(uuid)
			if ok {
				common.SendPack(modules.Packet{
					Event: "TUNNEL_DELETE",
					Data: map[string]interface{}{
						"tunnel_id": id,
					},
				}, session)
				sessionFound = true
			}
			return false
		}
		return true
	})
	
	if !sessionFound {
		golog.Infof("设备 %s 不在线，无法发送删除隧道命令", tunnel.DeviceID)
	}
	
	// 即使设备不在线，也删除隧道记录
	if err := common.DeleteSocks5Tunnel(id); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code": -1,
			"msg":  "删除隧道失败: " + err.Error(),
		})
		return
	}
	
	ctx.JSON(http.StatusOK, gin.H{
		"code": 0,
		"msg":  "隧道已删除",
	})
}

// isPortAvailable 检查端口是否可用
func isPortAvailable(port int) bool {
	// 检查端口范围
	if port < 1024 || port > 65535 {
		return false
	}
	
	// 这里简单检测端口是否已被其他应用使用
	// 实际项目中可能需要更复杂的逻辑
	listener, err := net.Listen("tcp", fmt.Sprintf(":%d", port))
	if err != nil {
		return false
	}
	listener.Close()
	
	return true
} 