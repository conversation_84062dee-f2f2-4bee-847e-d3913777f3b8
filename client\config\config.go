package config

import (
	"fmt"
	"net/url"
)

// Localhost for my development only.
// Shall be commented out when development is done.
// var ConfigBuffer = "\x00\xCD\xE3\x1F\x72\x06\x13\x07\x97\xA7\xD6\xA2\xDC\x9F\x87\xA5\x4C\xE1\xDB\xC4\x62\x6D\xD8\x97\xCF\x55\x84\xF5\x9C\xFB\x60\x8F\x90\x91\x2B\x3E\x53\xDB\xF0\x00\x88\xB4\x16\x1E\xCE\xA0\x38\xE9\xE9\xAC\x10\x47\xE4\x68\x66\x37\x65\xE5\xB7\xBC\x0A\xC6\x5C\xE1\xB3\x29\x55\x01\x39\x37\xA8\x65\xEE\xB9\x78\xEE\x38\xEF\x82\x39\x2F\xB7\xD5\xB2\x26\x9E\xD8\xE2\x05\xEA\xF9\xC0\x8A\xBB\x64\xDC\xAF\xC7\x0E\x4A\x9E\x48\x54\x63\x5B\xE1\x8A\x21\xD1\xBF\xB6\x41\x29\x8A\x8F\xCD\xCD\x2A\x68\xA3\x8F\x8C\xB8\xC2\x63\x23\x88\x45\xA7\x7E\xD7\xC6\x53\x01\x8D\x57\x61\x9A\x40\x62\xBD\xC8\xC9\xF6\x30\x55\x14\x09\xF8\x9C\xB9\x0F\xE7\x76\xEC\x84\xDD\x26\x80\xF5\xBD\xD8\xFF\x46\xC7\x64\x13\x3D\x0B\x09\xB5\xB5\x35\x93\x11\x36\x24\x00\xBA\x17\x1B\x57\xDE\x14\xDC\xF3\x52\x26\xCD\xC1\x8E\xBD\x4C\xC1\x16\x46\xCF\xA4\x96\xB7\xE2\x2D\x18\xB7\xB6\x14\xB2\x03\xCD\xE2\x66\xFC\x02\x73\xED\xE2\xD8\x33\xCF\x90\x3D\xDC\x16\xB5\xB0\xDB\xFC\x36\x36\x54\x01\x31\xCF\x60\x12\x2C\xED\x7C\xC4\xC3\x69\x3D\x1B\x7F\x9C\x85\x68\x08\xCD\x1F\xA9\x26\xA4\xC7\x85\xC9\x5E\x36\xBE\xFE\x5B\x33\xD1\x2D\xA7\x8F\x37\xCD\x47\xDC\x16\x84\x12\x79\x8A\x33\x85\x94\xA8\x19\x70\x86\x65\x45\xAA\xAF\x79\xFD\x9E\xAC\xDD\x99\x27\x77\x49\xF6\x15\xD7\x33\x00\x98\xE5\xCF\xA2\xD2\xB8\xDE\x99\xBE\x43\x15\x98\x07\xBE\x88\xAF\x48\xDB\x36\x8B\x3E\x13\x1D\xEA\x7F\x67\x70\x68\xDC\x8E\x81\x0A\xFB\x04\xB7\x3D\x05\xF6\x78\xD1\x1E\xD4\xD5\x68\x9E\xE3\xE3\x6E\x97\x69\xCA\x1A\x98\x3D\xA1\xBB\x05\x0B\x02\xBF\xBD\x5E\x21\xB6\x25\x58\x8B\x96\x75\x8A\xE1\x63\xCC\x4C\xB5\xE2\x27\x8B\xF9\x55\xB8\x06\xA7\xCC"
var ConfigBuffer = "\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19\x19"
var Commit = ``
var Config struct {
	Secure bool   `json:"secure"`
	Host   string `json:"host"`
	Port   int    `json:"port"`
	Path   string `json:"path"`
	UUID   string `json:"uuid"`
	Key    string `json:"key"`
}

func GetBaseURL(ws bool) string {
	baseUrl := url.URL{
		Host: fmt.Sprintf(`%v:%d`, Config.Host, Config.Port),
		Path: Config.Path,
	}
	if ws {
		if Config.Secure {
			baseUrl.Scheme = `wss`
		} else {
			baseUrl.Scheme = `ws`
		}
	} else {
		if Config.Secure {
			baseUrl.Scheme = `https`
		} else {
			baseUrl.Scheme = `http`
		}
	}
	return baseUrl.String()
}

// Init 初始化配置
func Init(token string) {
	if len(token) > 0 {
		// 如果提供了token，使用它更新配置
		Config.Key = token
	}
	// 可能还需要其他初始化操作
}
