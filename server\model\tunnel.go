package model

import (
	"fmt"
	"strconv"
	"time"
)

// Tunnel 隧道模型
type Tunnel struct {
	ID           int       `json:"id" gorm:"primaryKey"`
	DeviceUUID   string    `json:"device_uuid" gorm:"not null;index"`
	TunnelID     string    `json:"tunnel_id" gorm:"uniqueIndex;not null"`
	TunnelName   string    `json:"tunnel_name"`
	TunnelType   string    `json:"tunnel_type" gorm:"not null"` // socks5, tcp, http
	ServerPort   int       `json:"server_port"`
	Username     string    `json:"username"`
	Password     string    `json:"password"`
	Enabled      bool      `json:"enabled" gorm:"default:true"`
	Status       string    `json:"status" gorm:"default:inactive"` // active, inactive, error
	ErrorMessage string    `json:"error_message"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
	
	// 关联设备（不包含在JSON中，避免循环引用）
	Device Device `json:"-" gorm:"foreignKey:DeviceUUID;references:UUID"`
}

// TunnelConfig 隧道配置（用于创建和更新）
type TunnelConfig struct {
	TunnelName   string      `json:"tunnel_name" binding:"required"`
	TunnelType   string      `json:"tunnel_type" binding:"required,oneof=socks5 tcp http"`
	ServerPort   interface{} `json:"server_port"` // 可以是字符串或数字，0表示自动分配
	Username     string      `json:"username"`
	Password     string      `json:"password"`
	Enabled      bool        `json:"enabled"`
}

// GetServerPort 获取服务器端口的整数值
func (tc *TunnelConfig) GetServerPort() int {
	switch v := tc.ServerPort.(type) {
	case int:
		return v
	case float64:
		return int(v)
	case string:
		if port, err := strconv.Atoi(v); err == nil {
			return port
		}
		return 0
	default:
		return 0
	}
}

// TunnelResponse 隧道响应（包含设备信息）
type TunnelResponse struct {
	ID           int    `json:"id"`
	DeviceUUID   string `json:"device_uuid"`
	TunnelID     string `json:"tunnel_id"`
	TunnelName   string `json:"tunnel_name"`
	TunnelType   string `json:"tunnel_type"`
	ServerPort   int    `json:"server_port"`
	Username     string `json:"username"`
	Password     string `json:"password"`
	Enabled      bool   `json:"enabled"`
	Status       string `json:"status"`
	ErrorMessage string `json:"error_message"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
	
	// 设备信息
	DeviceHostname string `json:"device_hostname"`
	DeviceStatus   string `json:"device_status"`
	
	// 代理URL（前端显示用）
	ProxyURL string `json:"proxy_url"`
}

// IsActive 检查隧道是否活跃
func (t *Tunnel) IsActive() bool {
	return t.Enabled && t.Status == "active"
}

// GetProxyURL 获取代理URL
func (t *Tunnel) GetProxyURL(serverHost string) string {
	if t.TunnelType == "socks5" && t.ServerPort > 0 {
		if t.Username != "" && t.Password != "" {
			return fmt.Sprintf("socks5://%s:%s@%s:%d", t.Username, t.Password, serverHost, t.ServerPort)
		}
		return fmt.Sprintf("socks5://%s:%d", serverHost, t.ServerPort)
	}
	return ""
}
