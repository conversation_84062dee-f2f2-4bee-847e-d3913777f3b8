#!/bin/bash

# Spark 隧道功能完整测试脚本

echo "=== Spark 隧道功能完整测试 ==="

# 配置
SERVER_URL="http://localhost:8000"
API_BASE="$SERVER_URL/api"
DB_FILE="spark.db"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
print_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
print_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
print_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 测试计数器
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

run_test() {
    local test_name="$1"
    local test_command="$2"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    print_info "测试 $TOTAL_TESTS: $test_name"
    
    if eval "$test_command"; then
        print_success "✓ $test_name"
        PASSED_TESTS=$((PASSED_TESTS + 1))
        return 0
    else
        print_error "✗ $test_name"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        return 1
    fi
}

# 1. 基础环境检查
test_environment() {
    print_info "=== 基础环境检查 ==="
    
    run_test "检查服务运行状态" "curl -s $API_BASE/device/list > /dev/null"
    run_test "检查数据库文件存在" "[ -f '$DB_FILE' ]"
    run_test "检查隧道表存在" "sqlite3 '$DB_FILE' '.tables' | grep -q tunnels"
    run_test "检查前端页面可访问" "curl -s $SERVER_URL/tunnels > /dev/null"
}

# 2. API 接口测试
test_api_endpoints() {
    print_info "=== API 接口测试 ==="
    
    run_test "获取隧道列表 API" "curl -s $API_BASE/tunnels | grep -q '\"code\":0'"
    run_test "获取可用端口 API" "curl -s '$API_BASE/ports/available?type=tcp&count=5' | grep -q '\"code\":0'"
    run_test "获取端口统计 API" "curl -s $API_BASE/ports/stats | grep -q '\"code\":0'"
    run_test "获取设备列表 API" "curl -s $API_BASE/device/list | grep -q '\"code\":0'"
}

# 3. 数据库结构测试
test_database_structure() {
    print_info "=== 数据库结构测试 ==="
    
    run_test "tunnels 表结构正确" "sqlite3 '$DB_FILE' '.schema tunnels' | grep -q 'tunnel_id'"
    run_test "tunnel_sessions 表存在" "sqlite3 '$DB_FILE' '.tables' | grep -q tunnel_sessions"
    run_test "port_allocations 表存在" "sqlite3 '$DB_FILE' '.tables' | grep -q port_allocations"
    run_test "索引创建正确" "sqlite3 '$DB_FILE' '.indices tunnels' | grep -q idx_tunnels"
}

# 4. 隧道生命周期测试
test_tunnel_lifecycle() {
    print_info "=== 隧道生命周期测试 ==="
    
    # 获取第一个在线设备
    local device_response=$(curl -s "$API_BASE/device/list")
    if ! echo "$device_response" | grep -q '"code":0'; then
        print_warning "无法获取设备列表，跳过隧道生命周期测试"
        return
    fi
    
    local device_id
    if command -v jq &> /dev/null; then
        device_id=$(echo "$device_response" | jq -r '.data.devices[]? | select(.online == true) | .id' | head -1)
    else
        device_id=$(echo "$device_response" | grep -o '"id":"[^"]*"' | head -1 | cut -d'"' -f4)
    fi
    
    if [ -z "$device_id" ] || [ "$device_id" = "null" ]; then
        print_warning "没有在线设备，跳过隧道生命周期测试"
        return
    fi
    
    print_info "使用设备: $device_id"
    
    # 创建测试隧道
    local tunnel_data='{
        "device_id": "'$device_id'",
        "name": "测试隧道_'$(date +%s)'",
        "description": "自动化测试隧道",
        "type": "tcp",
        "protocol": "tcp",
        "local_host": "localhost",
        "local_port": 22,
        "auto_start": false
    }'
    
    local create_response=$(curl -s -X POST \
        -H "Content-Type: application/json" \
        -d "$tunnel_data" \
        "$API_BASE/tunnels")
    
    if echo "$create_response" | grep -q '"code":0'; then
        print_success "✓ 隧道创建成功"
        PASSED_TESTS=$((PASSED_TESTS + 1))
        
        # 提取隧道ID
        local tunnel_id
        if command -v jq &> /dev/null; then
            tunnel_id=$(echo "$create_response" | jq -r '.data.id')
        else
            tunnel_id=$(echo "$create_response" | grep -o '"id":"[^"]*"' | cut -d'"' -f4)
        fi
        
        if [ -n "$tunnel_id" ] && [ "$tunnel_id" != "null" ]; then
            print_info "隧道ID: $tunnel_id"
            
            # 测试隧道查询
            run_test "查询隧道详情" "curl -s '$API_BASE/tunnels/$tunnel_id' | grep -q '\"code\":0'"
            
            # 测试隧道启动
            run_test "启动隧道" "curl -s -X POST '$API_BASE/tunnels/$tunnel_id/start' | grep -q '\"code\":0'"
            
            # 等待一下
            sleep 2
            
            # 测试隧道停止
            run_test "停止隧道" "curl -s -X POST '$API_BASE/tunnels/$tunnel_id/stop' | grep -q '\"code\":0'"
            
            # 测试隧道删除
            run_test "删除隧道" "curl -s -X DELETE '$API_BASE/tunnels/$tunnel_id' | grep -q '\"code\":0'"
        else
            print_error "✗ 无法提取隧道ID"
            FAILED_TESTS=$((FAILED_TESTS + 1))
        fi
    else
        print_error "✗ 隧道创建失败"
        print_error "响应: $create_response"
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
    
    TOTAL_TESTS=$((TOTAL_TESTS + 5))
}

# 5. 数据一致性测试
test_data_consistency() {
    print_info "=== 数据一致性测试 ==="
    
    # 获取数据库中的隧道数量
    local db_count=$(sqlite3 "$DB_FILE" "SELECT COUNT(*) FROM tunnels;" 2>/dev/null || echo "0")
    
    # 获取API中的隧道数量
    local api_response=$(curl -s "$API_BASE/tunnels")
    local api_count=0
    if echo "$api_response" | grep -q '"code":0'; then
        if command -v jq &> /dev/null; then
            api_count=$(echo "$api_response" | jq '.data.tunnels | length' 2>/dev/null || echo "0")
        else
            api_count=$(echo "$api_response" | grep -o '"id":' | wc -l)
        fi
    fi
    
    print_info "数据库隧道数: $db_count, API隧道数: $api_count"
    
    run_test "数据库与API隧道数量一致" "[ '$db_count' -eq '$api_count' ]"
    
    # 检查端口分配一致性
    local allocated_ports=$(sqlite3 "$DB_FILE" "SELECT COUNT(*) FROM port_allocations WHERE status='allocated';" 2>/dev/null || echo "0")
    local tunnel_ports=$(sqlite3 "$DB_FILE" "SELECT COUNT(*) FROM tunnels WHERE remote_port > 0;" 2>/dev/null || echo "0")
    
    print_info "已分配端口数: $allocated_ports, 隧道端口数: $tunnel_ports"
    run_test "端口分配数据一致" "[ '$allocated_ports' -ge '0' ]"
}

# 6. 性能测试
test_performance() {
    print_info "=== 性能测试 ==="
    
    # 测试API响应时间
    local start_time=$(date +%s%N)
    curl -s "$API_BASE/tunnels" > /dev/null
    local end_time=$(date +%s%N)
    local response_time=$(( (end_time - start_time) / 1000000 )) # 转换为毫秒
    
    print_info "API响应时间: ${response_time}ms"
    run_test "API响应时间合理 (<1000ms)" "[ '$response_time' -lt '1000' ]"
    
    # 测试数据库查询性能
    start_time=$(date +%s%N)
    sqlite3 "$DB_FILE" "SELECT COUNT(*) FROM tunnels;" > /dev/null 2>&1
    end_time=$(date +%s%N)
    local db_time=$(( (end_time - start_time) / 1000000 ))
    
    print_info "数据库查询时间: ${db_time}ms"
    run_test "数据库查询时间合理 (<100ms)" "[ '$db_time' -lt '100' ]"
}

# 7. 前端功能测试
test_frontend() {
    print_info "=== 前端功能测试 ==="
    
    run_test "隧道管理页面可访问" "curl -s '$SERVER_URL/tunnels' | grep -q 'html'"
    run_test "静态资源加载正常" "curl -s '$SERVER_URL/static/js/' | grep -q 'js' || curl -s '$SERVER_URL/' | grep -q 'script'"
    
    # 检查前端API调用
    if command -v node &> /dev/null; then
        run_test "前端构建文件存在" "[ -d 'web/dist' ] || [ -d 'web/build' ]"
    else
        print_warning "Node.js 未安装，跳过前端构建检查"
    fi
}

# 8. 错误处理测试
test_error_handling() {
    print_info "=== 错误处理测试 ==="
    
    # 测试无效API调用
    run_test "无效隧道ID处理" "curl -s '$API_BASE/tunnels/invalid-id' | grep -q '\"code\":1'"
    run_test "无效设备ID创建隧道" "curl -s -X POST -H 'Content-Type: application/json' -d '{\"device_id\":\"invalid\",\"name\":\"test\",\"type\":\"tcp\",\"local_host\":\"localhost\",\"local_port\":22}' '$API_BASE/tunnels' | grep -q '\"code\":1'"
    run_test "缺少参数处理" "curl -s -X POST -H 'Content-Type: application/json' -d '{}' '$API_BASE/tunnels' | grep -q '\"code\":1'"
}

# 主测试函数
main() {
    echo
    print_info "开始 Spark 隧道功能完整测试..."
    echo
    
    test_environment
    echo
    
    test_api_endpoints
    echo
    
    test_database_structure
    echo
    
    test_tunnel_lifecycle
    echo
    
    test_data_consistency
    echo
    
    test_performance
    echo
    
    test_frontend
    echo
    
    test_error_handling
    echo
    
    # 测试结果汇总
    print_info "=== 测试结果汇总 ==="
    echo "总测试数: $TOTAL_TESTS"
    echo "通过: $PASSED_TESTS"
    echo "失败: $FAILED_TESTS"
    echo "成功率: $(( PASSED_TESTS * 100 / TOTAL_TESTS ))%"
    
    if [ $FAILED_TESTS -eq 0 ]; then
        print_success "🎉 所有测试通过！"
        exit 0
    else
        print_warning "⚠️  有 $FAILED_TESTS 个测试失败"
        exit 1
    fi
}

# 显示帮助
show_help() {
    echo "Spark 隧道功能完整测试脚本"
    echo
    echo "用法: $0 [选项]"
    echo
    echo "选项:"
    echo "  test     运行完整测试 (默认)"
    echo "  env      仅测试环境"
    echo "  api      仅测试API"
    echo "  db       仅测试数据库"
    echo "  lifecycle 仅测试隧道生命周期"
    echo "  help     显示帮助"
    echo
    echo "环境要求:"
    echo "  - Spark 服务运行在 localhost:8000"
    echo "  - 数据库文件 spark.db 存在"
    echo "  - 至少有一个在线设备（用于生命周期测试）"
    echo "  - 可选: jq 工具用于 JSON 解析"
}

# 处理命令行参数
case "${1:-test}" in
    "test")
        main
        ;;
    "env")
        test_environment
        ;;
    "api")
        test_api_endpoints
        ;;
    "db")
        test_database_structure
        ;;
    "lifecycle")
        test_tunnel_lifecycle
        ;;
    "help")
        show_help
        ;;
    *)
        print_error "未知选项: $1"
        show_help
        exit 1
        ;;
esac
