package controller

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"spark/model"
	"spark/service"
)

type TunnelController struct {
	tunnelService *service.TunnelService
}

func NewTunnelController(tunnelService *service.TunnelService) *TunnelController {
	return &TunnelController{
		tunnelService: tunnelService,
	}
}

// CreateTunnel 创建隧道
// @Summary 创建隧道
// @Description 为指定设备创建隧道
// @Tags 隧道管理
// @Accept json
// @Produce json
// @Param device_uuid path string true "设备UUID"
// @Param tunnel body model.TunnelConfig true "隧道配置"
// @Success 200 {object} model.Tunnel
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/devices/{device_uuid}/tunnels [post]
func (c *TunnelController) CreateTunnel(ctx *gin.Context) {
	deviceUUID := ctx.Param("device_uuid")
	if deviceUUID == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "设备UUID不能为空"})
		return
	}

	var config model.TunnelConfig
	if err := ctx.ShouldBindJSON(&config); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "请求参数错误: " + err.Error()})
		return
	}

	tunnel, err := c.tunnelService.CreateTunnel(deviceUUID, &config)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 200,
		"message": "隧道创建成功",
		"data": tunnel,
	})
}

// GetTunnels 获取隧道列表
// @Summary 获取隧道列表
// @Description 分页获取隧道列表
// @Tags 隧道管理
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(10)
// @Success 200 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/tunnels [get]
func (c *TunnelController) GetTunnels(ctx *gin.Context) {
	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("page_size", "10"))

	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 10
	}

	tunnels, total, err := c.tunnelService.GetTunnels(page, pageSize)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 200,
		"message": "获取成功",
		"data": gin.H{
			"list":      tunnels,
			"total":     total,
			"page":      page,
			"page_size": pageSize,
		},
	})
}

// UpdateTunnel 更新隧道
// @Summary 更新隧道
// @Description 更新隧道配置
// @Tags 隧道管理
// @Accept json
// @Produce json
// @Param tunnel_id path string true "隧道ID"
// @Param tunnel body model.TunnelConfig true "隧道配置"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/tunnels/{tunnel_id} [put]
func (c *TunnelController) UpdateTunnel(ctx *gin.Context) {
	tunnelID := ctx.Param("tunnel_id")
	if tunnelID == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "隧道ID不能为空"})
		return
	}

	var config model.TunnelConfig
	if err := ctx.ShouldBindJSON(&config); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "请求参数错误: " + err.Error()})
		return
	}

	if err := c.tunnelService.UpdateTunnel(tunnelID, &config); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 200,
		"message": "隧道更新成功",
	})
}

// DeleteTunnel 删除隧道
// @Summary 删除隧道
// @Description 删除指定隧道
// @Tags 隧道管理
// @Accept json
// @Produce json
// @Param tunnel_id path string true "隧道ID"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/tunnels/{tunnel_id} [delete]
func (c *TunnelController) DeleteTunnel(ctx *gin.Context) {
	tunnelID := ctx.Param("tunnel_id")
	if tunnelID == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "隧道ID不能为空"})
		return
	}

	if err := c.tunnelService.DeleteTunnel(tunnelID); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 200,
		"message": "隧道删除成功",
	})
}

// ToggleTunnel 切换隧道状态
// @Summary 切换隧道状态
// @Description 启用或禁用隧道
// @Tags 隧道管理
// @Accept json
// @Produce json
// @Param tunnel_id path string true "隧道ID"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/tunnels/{tunnel_id}/toggle [post]
func (c *TunnelController) ToggleTunnel(ctx *gin.Context) {
	tunnelID := ctx.Param("tunnel_id")
	if tunnelID == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "隧道ID不能为空"})
		return
	}

	if err := c.tunnelService.ToggleTunnel(tunnelID); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 200,
		"message": "隧道状态切换成功",
	})
}
