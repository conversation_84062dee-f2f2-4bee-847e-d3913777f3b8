import React, { useState, useEffect } from 'react';
import {
    Form, Input, Select, Switch,
    Button, Space, Card, Alert,
    Row, Col, Tag
} from 'antd';
import { message } from 'antd';
import { request } from '../../../utils/request';

const { Option } = Select;

const TunnelCreateForm = ({ tunnel, deviceFilter, onSuccess, onCancel }) => {
    const [form] = Form.useForm();
    const [devices, setDevices] = useState([]);
    const [tunnelType, setTunnelType] = useState('socks5');
    const [loading, setLoading] = useState(false);
    const [authEnabled, setAuthEnabled] = useState(true);

    const tunnelTypes = [
        {
            value: 'socks5',
            label: 'SOCKS5 代理',
            description: '通用代理协议，支持任意应用',
            color: 'orange'
        }
    ];

    useEffect(() => {
        loadDevices();
        if (tunnel) {
            // 编辑模式，填充表单
            const hasAuth = tunnel.username && tunnel.password;
            form.setFieldsValue({
                device_uuid: tunnel.device_uuid,
                tunnel_name: tunnel.tunnel_name,
                tunnel_type: tunnel.tunnel_type,
                server_port: tunnel.server_port,
                auth_enabled: hasAuth,
                username: tunnel.username,
                password: tunnel.password,
                enabled: tunnel.enabled,
            });
            setTunnelType(tunnel.tunnel_type);
            setAuthEnabled(hasAuth);
        } else if (deviceFilter) {
            // 如果有设备过滤，自动选择该设备
            form.setFieldsValue({
                device_uuid: deviceFilter.uuid,
                tunnel_type: 'socks5',
                auth_enabled: true,
                enabled: true,
            });
        }
    }, [tunnel, deviceFilter, form]);

    // 加载设备列表
    const loadDevices = async () => {
        try {
            // 优先使用实时的设备列表API（这个API返回当前在线的设备）
            let response = await request.post('/api/device/list');
            if (response.code === 200 || response.code === 0) {
                const deviceData = response.data || {};
                const onlineDevices = [];

                // 遍历设备数据，筛选在线设备
                for (const uuid in deviceData) {
                    const device = deviceData[uuid];
                    if (device && (device.hostname || device.id)) {
                        onlineDevices.push({
                            uuid: uuid,
                            hostname: device.hostname || device.id || uuid,
                            status: 'online', // 在设备列表中的都是在线设备
                            os: device.os || 'Unknown',
                            arch: device.arch || 'Unknown',
                            ip: device.wan || device.ip || 'Unknown',
                            username: device.username || 'Unknown'
                        });
                    }
                }

                console.log('从实时API获取到设备:', onlineDevices);
                setDevices(onlineDevices);
                return;
            }

            // 如果实时API失败，尝试新的设备API（从数据库获取）
            response = await request.get('/api/devices?status=online');
            if (response.code === 200 && response.data && response.data.list) {
                console.log('从数据库API获取到设备:', response.data.list);
                setDevices(response.data.list || []);
                return;
            }

            // 如果都失败了，显示错误
            message.warning('无法获取在线设备列表，请确保有客户端在线');
            setDevices([]);
        } catch (error) {
            console.error('加载设备列表失败:', error);
            message.error('无法获取在线设备列表: ' + error.message);
            setDevices([]);
        }
    };

    const handleSubmit = async (values) => {
        setLoading(true);
        try {
            // 处理端口：如果为空则不传递，让后端随机生成
            const submitData = {
                ...values
            };

            // 如果端口有值，转换为数字；如果为空，删除该字段让后端随机生成
            if (values.server_port) {
                submitData.server_port = parseInt(values.server_port);
            } else if (!tunnel) {
                // 只在创建模式下删除端口字段，编辑模式保持原端口
                delete submitData.server_port;
            }

            // 如果未启用认证，清空用户名密码
            if (!values.auth_enabled) {
                submitData.username = '';
                submitData.password = '';
            }

            // 删除前端专用字段
            delete submitData.auth_enabled;

            let response;
            if (tunnel) {
                // 编辑模式
                response = await request.put(`/api/tunnels/${tunnel.tunnel_id}`, submitData);
            } else {
                // 创建模式
                response = await request.post(`/api/devices/${values.device_uuid}/tunnels`, submitData);
            }

            if (response.code === 200) {
                message.success(tunnel ? '隧道更新成功' : '隧道创建成功');
                onSuccess();
            } else {
                message.error((tunnel ? '更新' : '创建') + '失败: ' + response.message);
            }
        } catch (error) {
            message.error((tunnel ? '更新' : '创建') + '失败: ' + error.message);
        } finally {
            setLoading(false);
        }
    };

    const handleTypeChange = (value) => {
        setTunnelType(value);
    };

    return (
        <Form
            form={form}
            layout="vertical"
            onFinish={handleSubmit}
            initialValues={{
                tunnel_type: 'socks5',
                enabled: true,
            }}
        >
            {/* 基本信息 */}
            <Card title="基本信息" size="small" style={{ marginBottom: 16 }}>
                <Row gutter={16}>
                    {!tunnel && !deviceFilter && (
                        <Col span={12}>
                            <Form.Item
                                name="device_uuid"
                                label="目标设备"
                                rules={[{ required: true, message: '请选择设备' }]}
                            >
                                <Select placeholder="选择设备" showSearch>
                                    {devices.map(device => (
                                        <Option key={device.uuid} value={device.uuid}>
                                            <Space>
                                                <span>{device.hostname || device.uuid}</span>
                                                <Tag color={device.status === 'online' ? 'green' : 'red'}>
                                                    {device.status === 'online' ? '在线' : '离线'}
                                                </Tag>
                                            </Space>
                                        </Option>
                                    ))}
                                </Select>
                            </Form.Item>
                        </Col>
                    )}
                    {!tunnel && deviceFilter && (
                        <Col span={12}>
                            <Form.Item label="目标设备">
                                <Input
                                    value={`${deviceFilter.hostname} (${deviceFilter.uuid})`}
                                    disabled
                                    style={{ backgroundColor: '#f5f5f5' }}
                                />
                                <Form.Item name="device_uuid" hidden>
                                    <Input />
                                </Form.Item>
                            </Form.Item>
                        </Col>
                    )}
                    <Col span={tunnel ? 24 : 12}>
                        <Form.Item
                            name="tunnel_name"
                            label="配置名"
                            rules={[{ required: true, message: '请输入配置名' }]}
                        >
                            <Input placeholder="输入配置名" />
                        </Form.Item>
                    </Col>
                </Row>
            </Card>

            {/* 隧道配置 */}
            <Card title="隧道配置" size="small" style={{ marginBottom: 16 }}>
                <Row gutter={16}>
                    <Col span={12}>
                        <Form.Item
                            name="tunnel_type"
                            label="隧道类型"
                            rules={[{ required: true }]}
                        >
                            <Select onChange={handleTypeChange}>
                                {tunnelTypes.map(type => (
                                    <Option key={type.value} value={type.value}>
                                        {type.label}
                                    </Option>
                                ))}
                            </Select>
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item
                            name="server_port"
                            label="服务端口"
                            rules={[
                                {
                                    validator: (_, value) => {
                                        if (!value) return Promise.resolve(); // 允许为空，后端会随机生成
                                        const port = parseInt(value);
                                        if (port < 1024 || port > 65535) {
                                            return Promise.reject(new Error('端口范围应在1024-65535之间'));
                                        }
                                        return Promise.resolve();
                                    }
                                }
                            ]}
                        >
                            <Input
                                type="number"
                                placeholder="请输入端口号 (1024-65535)，留空则随机生成"
                                min={1024}
                                max={65535}
                            />
                        </Form.Item>
                    </Col>
                </Row>

                <Alert
                    message={`${tunnelTypes.find(t => t.value === tunnelType)?.label || '隧道'} 配置`}
                    description={tunnelTypes.find(t => t.value === tunnelType)?.description || ''}
                    type="info"
                    style={{ marginBottom: 16 }}
                />
            </Card>

            {/* 认证配置 */}
            <Card title="认证配置" size="small" style={{ marginBottom: 16 }}>
                <Form.Item
                    name="auth_enabled"
                    valuePropName="checked"
                    style={{ marginBottom: 16 }}
                >
                    <Switch
                        checkedChildren="启用认证"
                        unCheckedChildren="无认证"
                        onChange={setAuthEnabled}
                    />
                </Form.Item>

                {authEnabled && (
                    <Row gutter={16}>
                        <Col span={12}>
                            <Form.Item
                                name="username"
                                label="用户名"
                                rules={[{ required: authEnabled, message: '请输入用户名' }]}
                            >
                                <Input placeholder="请输入SOCKS5用户名" />
                            </Form.Item>
                        </Col>
                        <Col span={12}>
                            <Form.Item
                                name="password"
                                label="密码"
                                rules={[{ required: authEnabled, message: '请输入密码' }]}
                            >
                                <Input.Password placeholder="请输入SOCKS5密码" />
                            </Form.Item>
                        </Col>
                    </Row>
                )}
                <Form.Item
                    name="enabled"
                    valuePropName="checked"
                    label="自动启动"
                    tooltip="创建后是否自动启动隧道"
                    style={{ marginBottom: 0 }}
                >
                    <Switch checkedChildren="启用隧道" unCheckedChildren="禁用隧道" />
                </Form.Item>
            </Card>

            {/* 操作按钮 */}
            <div style={{ textAlign: 'right' }}>
                <Space>
                    <Button onClick={onCancel}>取消</Button>
                    <Button type="primary" htmlType="submit" loading={loading}>
                        {tunnel ? '更新隧道' : '创建隧道'}
                    </Button>
                </Space>
            </div>
        </Form>
    );
};

export default TunnelCreateForm;
