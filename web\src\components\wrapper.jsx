import React from 'react';
import ProLayout, {PageContainer} from '@ant-design/pro-layout';
import zhCN from 'antd/lib/locale/zh_CN';
import en from 'antd/lib/locale/en_US';
import {getLang, getLocale} from "../locale/locale";
import {Button, ConfigProvider, notification, Menu} from "antd";
import version from "../config/version.json";
import ReactMarkdown from "react-markdown";
import i18n from "i18next";
import axios from "axios";
import './wrapper.css';
import {useNavigate, useLocation} from 'react-router-dom';

function wrapper(props) {
	const navigate = useNavigate();
	const location = useLocation();
	
	const handleMenuClick = (e) => {
		navigate(e.key);
	};
	
	return (
		<ProLayout
			loading={false}
			title='Spark'
			logo={null}
			layout='top'
			navTheme='light'
			collapsed={true}
			fixedHeader={true}
			contentWidth='fluid'
			collapsedButtonRender={Title}
			menuItemRender={(item, dom) => <div onClick={() => navigate(item.path)}>{dom}</div>}
			headerContentRender={() => (
				<Menu
					mode="horizontal"
					selectedKeys={[location.pathname]}
					onClick={handleMenuClick}
					style={{border: 'none'}}
				>
					<Menu.Item key="/">
						{i18n.t('OVERVIEW.TITLE') || 'Overview'}
					</Menu.Item>
					<Menu.Item key="/tunnels">
						{i18n.t('TUNNELS.TITLE') || '隧道代理'}
					</Menu.Item>
				</Menu>
			)}
		>
			<PageContainer>
				<ConfigProvider locale={getLang()==='zh-CN'?zhCN:en}>
					{props.children}
				</ConfigProvider>
			</PageContainer>
		</ProLayout>
	);
}

function Title() {
	return (
		<div
			style={{
				userSelect: 'none',
				fontWeight: 500
			}}
		>
			Spark
		</div>
	)
}
export default wrapper;