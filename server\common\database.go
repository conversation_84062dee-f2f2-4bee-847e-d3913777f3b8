package common

import (
	"database/sql"
	"fmt"
	"os"
	"path/filepath"
	"sync"
	"time"

	"github.com/kataras/golog"
)

var (
	// DB是应用程序使用的数据库连接
	DB     *sql.DB
	dbLock sync.Mutex
)

// 初始化数据库
func InitDatabase(dbPath string) error {
	dbLock.Lock()
	defer dbLock.Unlock()

	// 确保数据库目录存在
	dbDir := filepath.Dir(dbPath)
	if err := os.MkdirAll(dbDir, 0755); err != nil {
		return fmt.Errorf("创建数据库目录失败: %v", err)
	}

	// 连接数据库
	db, err := sql.Open("sqlite3", dbPath)
	if err != nil {
		return fmt.Errorf("连接数据库失败: %v", err)
	}

	// 测试连接
	if err := db.Ping(); err != nil {
		db.Close()
		return fmt.Errorf("测试数据库连接失败: %v", err)
	}

	// 设置数据库连接参数
	db.SetMaxOpenConns(10)
	db.SetMaxIdleConns(5)
	db.SetConnMaxLifetime(time.Hour)

	// 创建表
	if err := createTables(db); err != nil {
		db.Close()
		return fmt.Errorf("创建数据库表失败: %v", err)
	}

	DB = db
	golog.Infof("数据库初始化成功: %s", dbPath)
	return nil
}

// 创建数据库表
func createTables(db *sql.DB) error {
	// 创建设备表
	_, err := db.Exec(`
	CREATE TABLE IF NOT EXISTS devices (
		id TEXT PRIMARY KEY,
		uuid TEXT NOT NULL,
		name TEXT NOT NULL,
		os TEXT NOT NULL,
		arch TEXT NOT NULL,
		ip TEXT NOT NULL,
		last_online TIMESTAMP NOT NULL,
		first_seen TIMESTAMP NOT NULL,
		status TEXT NOT NULL,
		remark TEXT DEFAULT '',
		notes TEXT DEFAULT ''
	)`)
	if err != nil {
		return err
	}

	// 创建设备历史记录表
	_, err = db.Exec(`
	CREATE TABLE IF NOT EXISTS device_history (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		device_id TEXT NOT NULL,
		event_type TEXT NOT NULL,
		timestamp TIMESTAMP NOT NULL,
		ip TEXT NOT NULL,
		details TEXT,
		FOREIGN KEY (device_id) REFERENCES devices(id)
	)`)
	if err != nil {
		return err
	}
	
	// 注意：旧的socks5_tunnels表已废弃，现在使用统一的tunnels表

	// 创建隧道配置表
	_, err = db.Exec(`
	CREATE TABLE IF NOT EXISTS tunnels (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		tunnel_id VARCHAR(64) UNIQUE NOT NULL,
		device_id VARCHAR(64) NOT NULL,
		group_id VARCHAR(32) DEFAULT 'default',
		name VARCHAR(128) NOT NULL,
		description TEXT,
		tunnel_type VARCHAR(16) NOT NULL,
		protocol VARCHAR(8) DEFAULT 'tcp',
		remote_port INTEGER,
		local_host VARCHAR(128) DEFAULT 'localhost',
		local_port INTEGER,
		subdomain VARCHAR(64),
		custom_domain VARCHAR(128),
		auth_required BOOLEAN DEFAULT FALSE,
		auth_username VARCHAR(64),
		auth_password VARCHAR(128),
		username VARCHAR(64),
		password VARCHAR(128),
		allowed_ips TEXT,
		status VARCHAR(16) DEFAULT 'stopped',
		auto_start BOOLEAN DEFAULT TRUE,
		total_connections INTEGER DEFAULT 0,
		total_bytes_in BIGINT DEFAULT 0,
		total_bytes_out BIGINT DEFAULT 0,
		last_connection_time INTEGER,
		create_time INTEGER NOT NULL,
		update_time INTEGER NOT NULL
	)`)
	if err != nil {
		return err
	}

	// 创建隧道会话表
	_, err = db.Exec(`
	CREATE TABLE IF NOT EXISTS tunnel_sessions (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		session_id VARCHAR(64) UNIQUE NOT NULL,
		tunnel_id VARCHAR(64) NOT NULL,
		client_ip VARCHAR(45) NOT NULL,
		client_port INTEGER,
		user_agent TEXT,
		bytes_in BIGINT DEFAULT 0,
		bytes_out BIGINT DEFAULT 0,
		start_time INTEGER NOT NULL,
		last_activity INTEGER NOT NULL,
		FOREIGN KEY (tunnel_id) REFERENCES tunnels(tunnel_id)
	)`)
	if err != nil {
		return err
	}

	// 创建端口分配表
	_, err = db.Exec(`
	CREATE TABLE IF NOT EXISTS port_allocations (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		port INTEGER UNIQUE NOT NULL,
		tunnel_id VARCHAR(64),
		allocation_type VARCHAR(16) NOT NULL,
		status VARCHAR(16) DEFAULT 'allocated',
		allocated_time INTEGER NOT NULL,
		released_time INTEGER,
		FOREIGN KEY (tunnel_id) REFERENCES tunnels(tunnel_id)
	)`)
	if err != nil {
		return err
	}

	// 创建索引
	indexes := []string{
		"CREATE INDEX IF NOT EXISTS idx_tunnels_device_id ON tunnels(device_id)",
		"CREATE INDEX IF NOT EXISTS idx_tunnels_group_id ON tunnels(group_id)",
		"CREATE INDEX IF NOT EXISTS idx_tunnels_remote_port ON tunnels(remote_port)",
		"CREATE INDEX IF NOT EXISTS idx_tunnels_status ON tunnels(status)",
		"CREATE INDEX IF NOT EXISTS idx_tunnel_sessions_tunnel_id ON tunnel_sessions(tunnel_id)",
		"CREATE INDEX IF NOT EXISTS idx_tunnel_sessions_start_time ON tunnel_sessions(start_time)",
		"CREATE INDEX IF NOT EXISTS idx_port_allocations_port ON port_allocations(port)",
		"CREATE INDEX IF NOT EXISTS idx_port_allocations_status ON port_allocations(status)",
	}

	for _, indexSQL := range indexes {
		if _, err := db.Exec(indexSQL); err != nil {
			return fmt.Errorf("failed to create index: %v", err)
		}
	}

	// 数据库迁移：为现有的devices表添加remark字段
	if err := migrateDatabase(db); err != nil {
		return fmt.Errorf("数据库迁移失败: %v", err)
	}

	return nil
}

// migrateDatabase 数据库迁移
func migrateDatabase(db *sql.DB) error {
	// 检查devices表是否有remark字段
	rows, err := db.Query("PRAGMA table_info(devices)")
	if err != nil {
		return err
	}
	defer rows.Close()

	hasRemarkField := false
	for rows.Next() {
		var cid int
		var name, dataType string
		var notNull, pk int
		var defaultValue interface{}

		err := rows.Scan(&cid, &name, &dataType, &notNull, &defaultValue, &pk)
		if err != nil {
			continue
		}

		if name == "remark" {
			hasRemarkField = true
			break
		}
	}

	// 如果没有remark字段，添加它
	if !hasRemarkField {
		_, err = db.Exec("ALTER TABLE devices ADD COLUMN remark TEXT DEFAULT ''")
		if err != nil {
			return fmt.Errorf("添加remark字段失败: %v", err)
		}
		fmt.Println("数据库迁移：已为devices表添加remark字段")
	}

	return nil
}

// createLegacyTables 创建GORM没有管理的遗留表
func createLegacyTables(db *sql.DB) error {
	// 创建设备历史记录表（如果GORM没有管理的话）
	_, err := db.Exec(`
	CREATE TABLE IF NOT EXISTS device_history (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		device_id TEXT NOT NULL,
		event_type TEXT NOT NULL,
		timestamp TIMESTAMP NOT NULL,
		ip TEXT NOT NULL,
		details TEXT
	)`)
	if err != nil {
		return err
	}

	// 创建其他需要的索引
	indexes := []string{
		`CREATE INDEX IF NOT EXISTS idx_device_history_device_id ON device_history(device_id)`,
		`CREATE INDEX IF NOT EXISTS idx_device_history_timestamp ON device_history(timestamp)`,
	}

	for _, indexSQL := range indexes {
		if _, err := db.Exec(indexSQL); err != nil {
			return fmt.Errorf("failed to create index: %v", err)
		}
	}

	return nil
}

// 设备事件类型
const (
	EventConnect    = "CONNECT"    // 设备上线
	EventDisconnect = "DISCONNECT" // 设备掉线
	EventUpdate     = "UPDATE"     // 设备更新信息
)

// 记录设备事件
func LogDeviceEvent(deviceID, eventType, ip, details string) error {
	if DB == nil {
		return fmt.Errorf("数据库未初始化")
	}

	_, err := DB.Exec(
		`INSERT INTO device_history (device_id, event_type, timestamp, ip, details) VALUES (?, ?, ?, ?, ?)`,
		deviceID, eventType, time.Now(), ip, details,
	)
	return err
}

// 更新设备状态
func UpdateDevice(device *Device, status string) error {
	if DB == nil {
		return fmt.Errorf("数据库未初始化")
	}

	dbLock.Lock()
	defer dbLock.Unlock()

	// 检查设备是否存在
	var count int
	err := DB.QueryRow("SELECT COUNT(*) FROM devices WHERE id = ?", device.ID).Scan(&count)
	if err != nil {
		return err
	}

	if count == 0 {
		// 新设备，插入记录
		_, err = DB.Exec(
			`INSERT INTO devices (id, uuid, name, os, arch, ip, last_online, first_seen, status, remark) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
			device.ID, device.UUID, device.Name, device.OS, device.Arch, device.IP, time.Now(), time.Now(), status, "",
		)
	} else {
		// 已存在的设备，更新记录（保留原有的remark）
		_, err = DB.Exec(
			`UPDATE devices SET name = ?, os = ?, arch = ?, ip = ?, last_online = ?, status = ? WHERE id = ?`,
			device.Name, device.OS, device.Arch, device.IP, time.Now(), status, device.ID,
		)
	}

	return err
}

// 获取设备列表
func GetDeviceList() ([]Device, error) {
	if DB == nil {
		return nil, fmt.Errorf("数据库未初始化")
	}

	rows, err := DB.Query(`
	SELECT id, uuid, name, os, arch, ip, last_online, first_seen, status, COALESCE(remark, '') as remark
	FROM devices
	ORDER BY last_online DESC
	`)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var devices []Device
	for rows.Next() {
		var d Device
		var lastOnline, firstSeen time.Time
		err := rows.Scan(&d.ID, &d.UUID, &d.Name, &d.OS, &d.Arch, &d.IP, &lastOnline, &firstSeen, &d.Status, &d.Remark)
		if err != nil {
			return nil, err
		}

		// 添加时间信息
		d.LastOnline = lastOnline.Format(time.RFC3339)
		d.FirstSeen = firstSeen.Format(time.RFC3339)

		devices = append(devices, d)
	}

	return devices, nil
}

// UpdateDeviceRemark 更新设备备注
func UpdateDeviceRemark(deviceUUID, remark string) error {
	if DB == nil {
		return fmt.Errorf("数据库未初始化")
	}

	result, err := DB.Exec("UPDATE devices SET remark = ? WHERE uuid = ?", remark, deviceUUID)
	if err != nil {
		return fmt.Errorf("更新设备备注失败: %v", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取影响行数失败: %v", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("设备不存在")
	}

	return nil
}

// 获取设备历史记录
func GetDeviceHistory(deviceID string, limit, offset int) ([]map[string]interface{}, error) {
	if DB == nil {
		return nil, fmt.Errorf("数据库未初始化")
	}

	if limit <= 0 {
		limit = 100 // 默认限制
	}
	
	rows, err := DB.Query(`
	SELECT id, device_id, event_type, timestamp, ip, details 
	FROM device_history 
	WHERE device_id = ? 
	ORDER BY timestamp DESC
	LIMIT ? OFFSET ?
	`, deviceID, limit, offset)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var history []map[string]interface{}
	for rows.Next() {
		var id int
		var deviceID, eventType, ip, details string
		var timestamp time.Time
		
		err := rows.Scan(&id, &deviceID, &eventType, &timestamp, &ip, &details)
		if err != nil {
			return nil, err
		}
		
		entry := map[string]interface{}{
			"id":         id,
			"device_id":  deviceID,
			"event_type": eventType,
			"timestamp":  timestamp.Format(time.RFC3339),
			"ip":         ip,
			"details":    details,
		}
		
		history = append(history, entry)
	}

	return history, nil
}

// 关闭数据库连接
func CloseDatabase() {
	if DB != nil {
		DB.Close()
		DB = nil
	}
}

// InitDB 初始化数据库连接并创建必要的表 - 提供给外部调用的主接口
func InitDB(dbPath string) error {
	if DB != nil {
		return nil
	}

	// 使用GORM初始化数据库
	err := InitGormDatabase(dbPath)
	if err != nil {
		Fatal(nil, "DATABASE_INIT", "fail", "初始化GORM数据库失败: "+err.Error(), nil)
		return err
	}

	// 从GORM获取原生数据库连接
	if GormDB != nil {
		sqlDB, err := GormDB.DB()
		if err != nil {
			Fatal(nil, "DATABASE_INIT", "fail", "获取原生数据库连接失败: "+err.Error(), nil)
			return err
		}
		DB = sqlDB

		// 创建原生数据库需要的表（GORM没有的）
		if err := createLegacyTables(DB); err != nil {
			Fatal(nil, "DATABASE_INIT", "fail", "创建遗留表失败: "+err.Error(), nil)
			return err
		}
	}

	Info(nil, "DATABASE_INIT", "success", "数据库初始化成功", nil)
	return nil
}

