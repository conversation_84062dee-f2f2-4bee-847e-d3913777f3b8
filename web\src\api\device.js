import { request } from '../utils/request'

// 设备管理API
export const deviceApi = {
  // 获取设备列表
  getDevices(params) {
    return request({
      url: '/api/devices',
      method: 'get',
      params
    })
  },

  // 获取设备详情
  getDeviceDetail(deviceUuid) {
    return request({
      url: `/api/devices/${deviceUuid}`,
      method: 'get'
    })
  },

  // 获取在线设备
  getOnlineDevices() {
    return request({
      url: '/api/devices',
      method: 'get',
      params: { status: 'online' }
    })
  },

  // 获取设备笔记
  getDeviceNotes(deviceUuid) {
    return request({
      url: `/api/devices/${deviceUuid}/notes`,
      method: 'get'
    })
  },

  // 更新设备笔记
  updateDeviceNotes(deviceUuid, notes) {
    return request({
      url: `/api/devices/${deviceUuid}/notes`,
      method: 'put',
      data: { notes }
    })
  }
}

export default deviceApi
