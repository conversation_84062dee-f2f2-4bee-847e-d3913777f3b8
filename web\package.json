{"name": "spark", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "set NODE_ENV=development | npx webpack serve --mode development", "build-dev": "set NODE_ENV=development | npx webpack --mode development", "build-prod": "set NODE_ENV=production | npx webpack --mode production"}, "dependencies": {"@ant-design/icons": "^4.6.2", "@ant-design/pro-form": "^1.32.1", "@ant-design/pro-layout": "^6.23.0", "@ant-design/pro-table": "^2.45.0", "@uiw/react-md-editor": "^3.20.0", "ace-builds": "^1.5.3", "antd": "^4.23.6", "axios": "^0.26.1", "crc-32": "^1.2.2", "dayjs": "^1.10.6", "i18next": "^24.2.3", "lodash": "^4.17.21", "qs": "^6.10.3", "react": "^17.0.2", "react-ace": "^10.1.0", "react-dom": "^17.0.2", "react-draggable": "^4.4.5", "react-i18next": "^15.4.1", "react-markdown": "^8.0.3", "react-router": "^6.2.2", "react-router-dom": "^6.2.2", "virtuallist-antd": "^0.7.4-beta.0", "wcwidth": "^1.0.1", "xterm": "^5.0.0", "xterm-addon-fit": "^0.6.0", "xterm-addon-web-links": "^0.7.0"}, "devDependencies": {"antd-dayjs-webpack-plugin": "^1.0.6", "clean-webpack-plugin": "^4.0.0", "compression-webpack-plugin": "^10.0.0", "copy-webpack-plugin": "^10.2.4", "css-loader": "^6.7.1", "esbuild": "^0.15.14", "esbuild-loader": "^2.20.0", "html-webpack-plugin": "^5.5.0", "less": "^4.1.1", "less-loader": "^10.0.1", "style-loader": "^3.3.1", "webpack": "^5.18.0", "webpack-cli": "^4.10.0", "webpack-dev-server": "^4.7.4"}, "author": "XZB", "license": "ISC"}