//go:build linux

package process

import (
	"fmt"
	"runtime"
)

// Linux平台的Windows注入函数存根实现
func injectWindowsAPC(pid int32, shellcode []byte) error {
	return fmt.Errorf("APC注入仅支持Windows系统，当前系统: %s", runtime.GOOS)
}

func injectWindowsDLL(pid int32, shellcode []byte) error {
	return fmt.Erro<PERSON>("DLL注入仅支持Windows系统，当前系统: %s", runtime.GOOS)
}

func injectWindowsDirect(pid int32, shellcode []byte) error {
	return fmt.Errorf("直接注入仅支持Windows系统，当前系统: %s", runtime.GOOS)
}
