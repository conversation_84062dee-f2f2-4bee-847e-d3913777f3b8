//go:build !windows && !linux

package process

import (
	"fmt"
	"runtime"
)

// 默认平台的Windows注入函数实现（用于非Windows平台）
func injectWindowsAPC(pid int32, shellcode []byte) error {
	return fmt.Errorf("APC注入仅支持Windows系统，当前系统: %s", runtime.GOOS)
}

func injectWindowsDLL(pid int32, shellcode []byte) error {
	return fmt.Errorf("DLL注入仅支持Windows系统，当前系统: %s", runtime.GOOS)
}

func injectWindowsDirect(pid int32, shellcode []byte) error {
	return fmt.Errorf("直接注入仅支持Windows系统，当前系统: %s", runtime.GOOS)
}

// 默认平台的Linux权限检查实现（用于非Linux平台）
func checkPtracePermission() bool {
	return false
}
