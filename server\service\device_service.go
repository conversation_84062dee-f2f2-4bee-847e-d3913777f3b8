package service

import (
	"fmt"
	"time"

	"gorm.io/gorm"
	"Spark/server/model"
)

type DeviceService struct {
	db *gorm.DB
}

func NewDeviceService(db *gorm.DB) *DeviceService {
	return &DeviceService{db: db}
}

// GetDevices 获取设备列表
func (s *DeviceService) GetDevices(page, pageSize int, status, search string) ([]model.Device, int64, error) {
	var devices []model.Device
	var total int64

	// 计算偏移量
	offset := (page - 1) * pageSize

	// 构建查询
	query := s.db.Model(&model.Device{})

	// 状态过滤
	if status != "" {
		query = query.Where("status = ?", status)
	}

	// 搜索过滤
	if search != "" {
		searchPattern := "%" + search + "%"
		query = query.Where(
			"hostname LIKE ? OR username LIKE ? OR ip_address LIKE ? OR remark LIKE ? OR uuid LIKE ?",
			searchPattern, searchPattern, searchPattern, searchPattern, searchPattern,
		)
	}

	// 获取总数（按UUID去重计数）
	if err := query.Group("uuid").Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取设备列表（按UUID去重）
	if err := query.Offset(offset).
		Limit(pageSize).
		Order("last_seen DESC").
		Group("uuid").  // 按UUID分组去重
		Find(&devices).Error; err != nil {
		return nil, 0, err
	}

	return devices, total, nil
}

// GetDevice 获取设备详情
func (s *DeviceService) GetDevice(deviceUUID string) (*model.Device, error) {
	var device model.Device
	if err := s.db.Where("uuid = ?", deviceUUID).First(&device).Error; err != nil {
		return nil, fmt.Errorf("设备不存在: %v", err)
	}
	return &device, nil
}

// UpdateDeviceNotes 更新设备笔记
func (s *DeviceService) UpdateDeviceNotes(deviceUUID, notes string) error {
	result := s.db.Model(&model.Device{}).Where("uuid = ?", deviceUUID).Update("notes", notes)
	if result.Error != nil {
		return fmt.Errorf("更新设备笔记失败: %v", result.Error)
	}
	if result.RowsAffected == 0 {
		return fmt.Errorf("设备不存在")
	}
	return nil
}

// GetDeviceNotes 获取设备笔记
func (s *DeviceService) GetDeviceNotes(deviceUUID string) (string, error) {
	var device model.Device
	if err := s.db.Select("notes").Where("uuid = ?", deviceUUID).First(&device).Error; err != nil {
		return "", fmt.Errorf("获取设备笔记失败: %v", err)
	}
	return device.Notes, nil
}



// CreateOrUpdateDevice 创建或更新设备
func (s *DeviceService) CreateOrUpdateDevice(deviceInfo *model.DeviceInfo) (*model.Device, error) {
	var device model.Device
	
	// 查找现有设备
	err := s.db.Where("uuid = ?", deviceInfo.UUID).First(&device).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return nil, fmt.Errorf("查询设备失败: %v", err)
	}

	// 更新设备信息
	device.UUID = deviceInfo.UUID
	device.Hostname = deviceInfo.Hostname
	device.Username = deviceInfo.Username
	device.OS = deviceInfo.OS
	device.Arch = deviceInfo.Arch
	device.CPUInfo = deviceInfo.CPUInfo
	device.MemoryTotal = deviceInfo.MemoryTotal
	device.IPAddress = deviceInfo.IPAddress
	device.Status = "online"
	device.LastSeen = time.Now()

	if err == gorm.ErrRecordNotFound {
		// 创建新设备
		device.CreatedAt = time.Now()
		device.UpdatedAt = time.Now()
		if err := s.db.Create(&device).Error; err != nil {
			return nil, fmt.Errorf("创建设备失败: %v", err)
		}
	} else {
		// 更新现有设备
		device.UpdatedAt = time.Now()
		if err := s.db.Save(&device).Error; err != nil {
			return nil, fmt.Errorf("更新设备失败: %v", err)
		}
	}

	return &device, nil
}

// UpdateDeviceStatus 更新设备状态
func (s *DeviceService) UpdateDeviceStatus(deviceUUID, status string) error {
	result := s.db.Model(&model.Device{}).
		Where("uuid = ?", deviceUUID).
		Updates(map[string]interface{}{
			"status":     status,
			"last_seen":  time.Now(),
			"updated_at": time.Now(),
		})

	if result.Error != nil {
		return fmt.Errorf("更新设备状态失败: %v", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("设备不存在")
	}

	return nil
}

// GetOnlineDevices 获取在线设备
func (s *DeviceService) GetOnlineDevices() ([]model.Device, error) {
	var devices []model.Device
	if err := s.db.Where("status = ?", "online").Find(&devices).Error; err != nil {
		return nil, err
	}
	return devices, nil
}

// DeleteDevice 删除设备
func (s *DeviceService) DeleteDevice(deviceUUID string) error {
	// 先删除相关的隧道
	if err := s.db.Where("device_uuid = ?", deviceUUID).Delete(&model.Tunnel{}).Error; err != nil {
		return fmt.Errorf("删除设备隧道失败: %v", err)
	}

	// 删除设备
	result := s.db.Where("uuid = ?", deviceUUID).Delete(&model.Device{})
	if result.Error != nil {
		return fmt.Errorf("删除设备失败: %v", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("设备不存在")
	}

	return nil
}

// CleanupOfflineDevices 清理长时间离线的设备
func (s *DeviceService) CleanupOfflineDevices(offlineThreshold time.Duration) error {
	cutoffTime := time.Now().Add(-offlineThreshold)

	// 将长时间未见的设备标记为离线
	if err := s.db.Model(&model.Device{}).
		Where("last_seen < ? AND status = ?", cutoffTime, "online").
		Update("status", "offline").Error; err != nil {
		return fmt.Errorf("清理离线设备失败: %v", err)
	}

	return nil
}

// UpdateDeviceRemark 更新设备备注
func (s *DeviceService) UpdateDeviceRemark(deviceUUID, remark string) error {
	result := s.db.Model(&model.Device{}).
		Where("uuid = ?", deviceUUID).
		Updates(map[string]interface{}{
			"remark":     remark,
			"updated_at": time.Now(),
		})

	if result.Error != nil {
		return fmt.Errorf("更新设备备注失败: %v", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("设备不存在")
	}

	return nil
}
