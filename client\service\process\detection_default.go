//go:build !windows && !linux

package process

import (
	"fmt"
	"runtime"
)

// 默认平台的沙箱检测方法（用于不支持的平台）
func (sd *SandboxDetector) DetectSandbox() (bool, string, int) {
	// 基础检测
	score := 0
	var reasons []string
	
	if runtime.NumCPU() < 2 {
		reasons = append(reasons, "CPU核心数过少")
		score += 10
	}
	
	reasonStr := fmt.Sprintf("默认平台检测 (%s)", runtime.GOOS)
	if len(reasons) > 0 {
		reasonStr += fmt.Sprintf(": %v", reasons)
	}
	
	return score >= 10, reasonStr, score
}

// 默认平台的反调试检测方法
func (ade *AntiDebugEngine) DetectDebugger() (bool, string) {
	return false, fmt.Sprintf("平台 %s 不支持调试器检测", runtime.GOOS)
}

// 公开的调试器检测方法
func (ade *AntiDebugEngine) CheckIsDebuggerPresentPublic() bool {
	return false
}

// 默认平台的高级注入引擎方法
func (aie *AdvancedInjectionEngine) InjectUltimate(pid int32, shellcode []byte, method string) error {
	return fmt.Errorf("平台 %s 不支持高级注入功能", runtime.GOOS)
}

func (aie *AdvancedInjectionEngine) testAPIAvailability(apiName string) bool {
	return false
}
