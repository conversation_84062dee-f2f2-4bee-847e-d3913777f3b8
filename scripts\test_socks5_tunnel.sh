#!/bin/bash

# SOCKS5 隧道功能测试脚本
# 测试完整的 SOCKS5 隧道创建、启动、数据传输和停止流程

set -e

# 配置
SERVER_URL="http://localhost:8000"
DB_FILE="spark.db"
TEST_DEVICE_ID="test-device-001"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查服务器是否运行
check_server() {
    print_info "检查服务器状态..."
    
    if curl -s "$SERVER_URL/api/health" > /dev/null 2>&1; then
        print_success "服务器运行正常"
        return 0
    else
        print_error "服务器未运行或无法访问"
        return 1
    fi
}

# 检查数据库
check_database() {
    print_info "检查数据库状态..."
    
    if [ ! -f "$DB_FILE" ]; then
        print_error "数据库文件不存在: $DB_FILE"
        return 1
    fi
    
    # 检查隧道表是否存在
    if sqlite3 "$DB_FILE" ".tables" | grep -q "tunnels"; then
        print_success "隧道表存在"
    else
        print_error "隧道表不存在"
        return 1
    fi
    
    # 检查新字段是否存在
    schema=$(sqlite3 "$DB_FILE" ".schema tunnels")
    if echo "$schema" | grep -q "username" && echo "$schema" | grep -q "password"; then
        print_success "SOCKS5 字段已添加"
    else
        print_warning "SOCKS5 字段缺失，尝试迁移数据库..."
        if [ -f "scripts/migrate_tunnel_db.sql" ]; then
            sqlite3 "$DB_FILE" < scripts/migrate_tunnel_db.sql
            print_success "数据库迁移完成"
        else
            print_error "迁移脚本不存在"
            return 1
        fi
    fi
    
    return 0
}

# 测试隧道 API
test_tunnel_api() {
    print_info "测试隧道管理 API..."
    
    # 1. 获取隧道列表
    print_info "1. 获取隧道列表"
    response=$(curl -s -w "%{http_code}" "$SERVER_URL/api/tunnels")
    http_code="${response: -3}"
    if [ "$http_code" = "200" ]; then
        print_success "获取隧道列表成功"
    else
        print_error "获取隧道列表失败 (HTTP $http_code)"
        return 1
    fi
    
    # 2. 创建 SOCKS5 隧道
    print_info "2. 创建 SOCKS5 隧道"
    tunnel_data='{
        "name": "Test SOCKS5 Tunnel",
        "description": "测试用的 SOCKS5 隧道",
        "type": "socks5",
        "device_id": "'$TEST_DEVICE_ID'",
        "remote_port": 0,
        "auth_required": true,
        "username": "testuser",
        "password": "testpass123"
    }'
    
    response=$(curl -s -w "%{http_code}" -X POST \
        -H "Content-Type: application/json" \
        -d "$tunnel_data" \
        "$SERVER_URL/api/tunnels")
    
    http_code="${response: -3}"
    response_body="${response%???}"
    
    if [ "$http_code" = "200" ]; then
        print_success "创建 SOCKS5 隧道成功"
        # 提取隧道 ID
        tunnel_id=$(echo "$response_body" | grep -o '"tunnel_id":"[^"]*"' | cut -d'"' -f4)
        if [ -n "$tunnel_id" ]; then
            print_info "隧道 ID: $tunnel_id"
            echo "$tunnel_id" > /tmp/test_tunnel_id
        else
            print_warning "无法提取隧道 ID"
        fi
    else
        print_error "创建 SOCKS5 隧道失败 (HTTP $http_code)"
        print_error "响应: $response_body"
        return 1
    fi
    
    return 0
}

# 测试隧道启动
test_tunnel_start() {
    if [ ! -f "/tmp/test_tunnel_id" ]; then
        print_error "隧道 ID 文件不存在，跳过启动测试"
        return 1
    fi
    
    tunnel_id=$(cat /tmp/test_tunnel_id)
    print_info "测试隧道启动 (ID: $tunnel_id)..."
    
    response=$(curl -s -w "%{http_code}" -X POST \
        "$SERVER_URL/api/tunnels/$tunnel_id/start")
    
    http_code="${response: -3}"
    response_body="${response%???}"
    
    if [ "$http_code" = "200" ]; then
        print_success "隧道启动成功"
        
        # 等待一段时间让隧道完全启动
        sleep 2
        
        # 检查隧道状态
        response=$(curl -s "$SERVER_URL/api/tunnels")
        if echo "$response" | grep -q '"status":"running"'; then
            print_success "隧道状态为运行中"
        else
            print_warning "隧道可能未完全启动"
        fi
    else
        print_error "隧道启动失败 (HTTP $http_code)"
        print_error "响应: $response_body"
        return 1
    fi
    
    return 0
}

# 测试隧道停止
test_tunnel_stop() {
    if [ ! -f "/tmp/test_tunnel_id" ]; then
        print_error "隧道 ID 文件不存在，跳过停止测试"
        return 1
    fi
    
    tunnel_id=$(cat /tmp/test_tunnel_id)
    print_info "测试隧道停止 (ID: $tunnel_id)..."
    
    response=$(curl -s -w "%{http_code}" -X POST \
        "$SERVER_URL/api/tunnels/$tunnel_id/stop")
    
    http_code="${response: -3}"
    if [ "$http_code" = "200" ]; then
        print_success "隧道停止成功"
    else
        print_error "隧道停止失败 (HTTP $http_code)"
        return 1
    fi
    
    return 0
}

# 清理测试数据
cleanup() {
    print_info "清理测试数据..."
    
    if [ -f "/tmp/test_tunnel_id" ]; then
        tunnel_id=$(cat /tmp/test_tunnel_id)
        
        # 删除测试隧道
        response=$(curl -s -w "%{http_code}" -X DELETE \
            "$SERVER_URL/api/tunnels/$tunnel_id")
        
        http_code="${response: -3}"
        if [ "$http_code" = "200" ]; then
            print_success "测试隧道删除成功"
        else
            print_warning "删除测试隧道失败 (HTTP $http_code)"
        fi
        
        rm -f /tmp/test_tunnel_id
    fi
}

# 主测试流程
main() {
    print_info "开始 SOCKS5 隧道功能测试..."
    
    # 检查前置条件
    if ! check_server; then
        exit 1
    fi
    
    if ! check_database; then
        exit 1
    fi
    
    # 执行测试
    if test_tunnel_api; then
        print_success "隧道 API 测试通过"
    else
        print_error "隧道 API 测试失败"
        cleanup
        exit 1
    fi
    
    if test_tunnel_start; then
        print_success "隧道启动测试通过"
    else
        print_warning "隧道启动测试失败（可能是因为客户端未连接）"
    fi
    
    if test_tunnel_stop; then
        print_success "隧道停止测试通过"
    else
        print_warning "隧道停止测试失败"
    fi
    
    # 清理
    cleanup
    
    print_success "SOCKS5 隧道功能测试完成！"
}

# 捕获退出信号，确保清理
trap cleanup EXIT

# 运行主程序
main "$@"
