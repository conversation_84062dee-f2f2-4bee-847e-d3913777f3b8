/**
 * 格式化字节数为人类可读的字符串
 * @param {number} bytes - 字节数
 * @param {number} decimals - 小数位数
 * @returns {string} 格式化后的字符串
 */
export const formatBytes = (bytes, decimals = 2) => {
  if (bytes === 0) return '0 B';
  
  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
  
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
};

/**
 * 格式化Unix时间戳为本地日期时间字符串
 * @param {number} timestamp - Unix时间戳（秒）
 * @returns {string} 格式化后的日期时间
 */
export const formatTimestamp = (timestamp) => {
  if (!timestamp) return '-';
  
  const date = new Date(timestamp * 1000);
  return date.toLocaleString();
};

/**
 * 格式化持续时间（秒）为可读字符串
 * @param {number} seconds - 持续时间（秒）
 * @returns {string} 格式化后的持续时间
 */
export const formatDuration = (seconds) => {
  if (!seconds || seconds <= 0) return '-';
  
  const days = Math.floor(seconds / 86400);
  const hours = Math.floor((seconds % 86400) / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = Math.floor(seconds % 60);
  
  const parts = [];
  
  if (days > 0) parts.push(`${days}d`);
  if (hours > 0) parts.push(`${hours}h`);
  if (minutes > 0) parts.push(`${minutes}m`);
  if (remainingSeconds > 0 || parts.length === 0) parts.push(`${remainingSeconds}s`);
  
  return parts.join(' ');
};

/**
 * 格式化IP地址和端口
 * @param {string} host - 主机地址
 * @param {number|string} port - 端口号
 * @returns {string} 格式化后的地址
 */
export const formatAddress = (host, port) => {
  if (!host) return '-';
  if (!port) return host;
  return `${host}:${port}`;
};

/**
 * 格式化状态为颜色
 * @param {string} status - 状态字符串
 * @returns {string} 对应的颜色
 */
export const getStatusColor = (status) => {
  switch (status?.toLowerCase()) {
    case 'active':
    case 'running':
    case 'online':
    case 'success':
      return 'green';
    case 'inactive':
    case 'stopped':
    case 'warning':
      return 'orange';
    case 'error':
    case 'offline':
    case 'failed':
      return 'red';
    default:
      return 'gray';
  }
}; 