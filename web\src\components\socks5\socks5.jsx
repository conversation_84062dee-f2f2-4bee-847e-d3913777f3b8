import React, { useEffect } from 'react';
import TunnelManager from '../../pages/Tunnel/TunnelManager';
import DraggableModal from '../modal';

const Socks5 = ({ open, device, onCancel, ...props }) => {
  if (!open) return null;

  return (
    <DraggableModal
      draggable={props.draggable !== false}
      maskClosable={false}
      destroyOnClose={true}
      modalTitle={`隧道管理 - ${device?.hostname || '未知设备'}`}
      footer={null}
      width={1200}
      bodyStyle={{
        padding: 0,
        height: '600px',
        overflow: 'hidden'
      }}
      open={open}
      onCancel={onCancel}
      {...props}
    >
      <div style={{ height: '100%', overflow: 'auto', padding: '16px' }}>
        <TunnelManager deviceFilter={device} />
      </div>
    </DraggableModal>
  );
};

export default Socks5;