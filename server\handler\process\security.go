package process

import (
	"strings"
)

// 进程安全分析器
type ProcessSecurityAnalyzer struct {
	edrProcesses       []string
	avProcesses        []string
	systemProcesses    []string
	sensitiveProcesses []string // 包含敏感信息的进程
}

// 创建进程安全分析器
func NewProcessSecurityAnalyzer() *ProcessSecurityAnalyzer {
	return &ProcessSecurityAnalyzer{
		edrProcesses: []string{
			// ===== Microsoft Defender =====
			"MsMpEng.exe", "MsSense.exe", "SenseIR.exe", "SenseNdr.exe",
			"SenseCncProxy.exe", "SenseSampleUploader.exe", "HealthService.exe",
			"MonitoringHost.exe", "MpCmdRun.exe", "MpDlpService.exe",
			
			// ===== SentinelOne =====
			"SentinelAgent.exe", "SentinelAgentWorker.exe", "SentinelServiceHost.exe",
			"SentinelStaticEngine.exe", "LogProcessorService.exe", "SentinelStaticEngineScanner.exe",
			"SentinelHelperService.exe", "SentinelBrowserNativeHost.exe", "LogCollector.exe",
			"SentinelMemoryScanner.exe", "SentinelRanger.exe", "SentinelRemediation.exe",
			
			// ===== CrowdStrike Falcon =====
			"CSFalconContainer.exe", "CSFalconService.exe",
			
			// ===== Carbon Black =====
			"cb.exe", "RepMgr.exe", "RepUtils.exe", "RepUx.exe", "RepWAV.exe", "RepWSC.exe",
			
			// ===== Tanium =====
			"TaniumClient.exe", "TaniumCX.exe", "TaniumDetectEngine.exe",
			
			// ===== Palo Alto Networks =====
			"Traps.exe", "cyserver.exe", "CyveraService.exe", "CyvrFsFlt.exe",
			
			// ===== FortiEDR =====
			"fortiedr.exe", "FortiEDRCollectorService.exe",
			
			// ===== Cylance =====
			"CylanceSvc.exe",
			
			// ===== Cybereason =====
			"AmSvc.exe", "CrAmTray.exe", "CrsSvc.exe", "ExecutionPreventionSvc.exe", "CybereasonAV.exe",
			
			// ===== Elastic EDR =====
			"winlogbeat.exe", "elastic-agent.exe", "elastic-endpoint.exe", "filebeat.exe",
			
			// ===== Trellix EDR =====
			"xagt.exe",
			
			// ===== Qualys EDR =====
			"QualysAgent.exe",
			
			// ===== Cisco Secure Endpoint =====
			"sfc.exe",
			
			// ===== ESET =====
			"EIConnector.exe", "ekrn.exe",
			
			// ===== Harfanglab EDR =====
			"hurukai.exe",
			
			// ===== TrendMicro Apex One =====
			"CETASvc.exe", "WSCommunicator.exe", "EndpointBasecamp.exe", "TmListen.exe",
			"Ntrtscan.exe", "TmWSCSvc.exe", "PccNTMon.exe", "TMBMSRV.exe", "CNTAoSMgr.exe", "TmCCSF.exe",
		},
		avProcesses: []string{
			// ===== 360安全卫士 =====
			"360Safe.exe", "360tray.exe", "360sd.exe", "360rp.exe", "360rps.exe",
			"360AI.exe", "360apploader.exe", "360BoxLd64.exe", "360boxmain.exe",
			"360DeskAna.exe", "360DeskAna64.exe", "360DiagnoseScan.exe", "360fab.exe",
			"360Feedback.exe", "360FileGuard.exe", "360HImmu.exe", "360hips.exe",
			"360hotfix.exe", "360Inst.exe", "360LogCenter.exe", "360MsgCenter.exe",
			"360netcfg.exe", "360netcfg64.exe", "360netman.exe", "360NetRepair.exe",
			"360PatchMgr.exe", "360PatchMgr64.exe", "360PayInsure.exe", "360QandAExpert.exe",
			"360QMachine.exe", "360realpro.exe", "360Restore.exe", "360sclog.exe",
			"360ScreenCapture.exe", "360sctblist.exe", "360sdrun.exe", "360sdSetup.exe",
			"360sdToasts.exe", "360sdupd.exe", "360SettingCenter.exe", "360ShellPro.exe",
			"360skininstall.exe", "360SkinMgr.exe", "360SPTool.exe", "360taskmgr.exe",
			"360Toasts.exe", "360UDiskCheck.exe", "360UDiskGuard.exe", "360UHelper.exe",
			"ZhuDongFangYu.exe",
			
			// ===== 百度杀毒 =====
			"BaiduSdSvc.exe",
			
			// ===== 安全狗 =====
			"SafeDogGuardCenter.exe", "safedogupdatecenter.exe", "safedogguardcenter.exe",
			"SafeDogSiteIIS.exe", "SafeDogTray.exe", "SafeDogServerUI.exe",
			
			// ===== D盾 =====
			"D_Safe_Manage.exe", "d_manage.exe",
			
			// ===== 云锁 =====
			"yunsuo_agent_service.exe", "yunsuo_agent_daemon.exe",
			
			// ===== 护卫神 =====
			"HwsPanel.exe", "hws_ui.exe", "hws.exe", "hwsd.exe",
			
			// ===== HIPS =====
			"wsctrlsvc.exe", "HipsTray.exe", "HipsDaemon.exe", "wsctrl.exe", "usysdiag.exe","PopBlock.exe",
		},
		systemProcesses: []string{
			// ===== 关键系统进程 =====
			"System", "smss.exe", "csrss.exe", "wininit.exe", "winlogon.exe",
			"services.exe", "lsass.exe", "lsm.exe", "svchost.exe", "spoolsv.exe",
			"dwm.exe", "audiodg.exe", "conhost.exe", "userinit.exe",
			"LogonUI.exe", "fontdrvhost.exe", "sihost.exe", "ctfmon.exe",
			"WmiPrvSE.exe", "msdtc.exe","[System Process]","Secure System",
			"Registry","Memory Compression",
		},
		sensitiveProcesses: []string{
			// ===== 浏览器进程 =====
			"firefox.exe", "iexplore.exe", "chrome.exe", "msedge.exe",
			"opera.exe", "brave.exe", "vivaldi.exe", "360se.exe", "360chrome.exe",
			"sogouexplorer.exe", "liebao.exe", "maxthon.exe", "qqbrowser.exe",
			"ucbrowser.exe", "2345explorer.exe", "theworld.exe",

			// ===== 文本编辑器 =====
			"notepad.exe", "notepad++.exe", "sublime_text.exe", "code.exe",
			"atom.exe", "vim.exe", "gvim.exe", "emacs.exe",

			// ===== 远程控制软件 =====
			"SunloginClient.exe", "sunloginclient.exe", // 向日葵
			"ToDesk.exe", "todesk.exe", "ToDesk_Service.exe",
			"TeamViewer.exe", "tv_w32.exe", "tv_x64.exe",
			"AnyDesk.exe", "anydesk.exe",
			"VNC-Viewer.exe", "vncviewer.exe", "winvnc.exe",
			"RustDesk.exe", "rustdesk.exe",
			"Parsec.exe", "parsec.exe",

			// ===== 数据库管理工具 =====
			"Navicat.exe", "navicat.exe", "NavicatPremium.exe",
			"dbeaver.exe", "DBeaver.exe",
			"phpMyAdmin.exe", "phpmyadmin.exe",
			"SQLyog.exe", "sqlyog.exe",
			"HeidiSQL.exe", "heidisql.exe",
			"DataGrip.exe", "datagrip.exe", "datagrip64.exe",
			"Toad.exe", "toad.exe",
			"SSMS.exe", "ssms.exe", // SQL Server Management Studio

			// ===== SSH/FTP客户端 =====
			"WindTerm.exe", "windterm.exe",
			"FinalShell.exe", "finalshell.exe",
			"Xshell.exe", "xshell.exe", "Xshell7.exe",
			"Xftp.exe", "xftp.exe", "Xftp7.exe",
			"FileZilla.exe", "filezilla.exe",
			"WinSCP.exe", "winscp.exe",
			"PuTTY.exe", "putty.exe",
			"SecureCRT.exe", "securecrt.exe",
			"MobaXterm.exe", "mobaxterm.exe",
			"Termius.exe", "termius.exe",
			"FlashFXP.exe", "flashfxp.exe",
			"CuteFTP.exe", "cuteftp.exe",
			"SmartFTP.exe", "smartftp.exe",

			// ===== 即时通讯软件 =====
			"WeChat.exe", "wechat.exe", "WeChatApp.exe", // 微信
			"DingTalk.exe", "dingtalk.exe", "DingtalkLauncher.exe", // 钉钉
			"QQ.exe", "qq.exe", "QQScLauncher.exe", "TIM.exe", "tim.exe",
			"WhatsApp.exe", "whatsapp.exe",
			"Telegram.exe", "telegram.exe",
			"Signal.exe", "signal.exe",
			"Discord.exe", "discord.exe",
			"Slack.exe", "slack.exe",
			"Skype.exe", "skype.exe",
			"Zoom.exe", "zoom.exe", "ZoomLauncher.exe",
			"Teams.exe", "teams.exe", "msteams.exe",
			"Feishu.exe", "feishu.exe", "ByteDance.exe", // 飞书
			"YuanFudao.exe", "yuanfudao.exe", // 腾讯会议
			"VooVMeeting.exe", "voovmeeting.exe", // 腾讯会议
			"Tencent.exe", "tencent.exe",

			// ===== 邮件客户端 =====
			"Outlook.exe", "outlook.exe", "OUTLOOK.EXE",
			"Thunderbird.exe", "thunderbird.exe",
			"Foxmail.exe", "foxmail.exe",
			"MailMaster.exe", "mailmaster.exe", // 网易邮箱大师

			// ===== 密码管理器 =====
			"1Password.exe", "1password.exe",
			"KeePass.exe", "keepass.exe", "KeePassXC.exe",
			"Bitwarden.exe", "bitwarden.exe",
			"LastPass.exe", "lastpass.exe",
			"Dashlane.exe", "dashlane.exe",

			// ===== 开发工具 =====
			"devenv.exe", // Visual Studio
			"idea64.exe", "idea.exe", // IntelliJ IDEA
			"pycharm64.exe", "pycharm.exe", // PyCharm
			"webstorm64.exe", "webstorm.exe", // WebStorm
			"phpstorm64.exe", "phpstorm.exe", // PhpStorm
			"goland64.exe", "goland.exe", // GoLand
			"rider64.exe", "rider.exe", // Rider
			"clion64.exe", "clion.exe", // CLion

			// ===== 云存储客户端 =====
			"OneDrive.exe", "onedrive.exe",
			"Dropbox.exe", "dropbox.exe",
			"GoogleDriveFS.exe", "googledrivefs.exe",
			"BaiduNetdisk.exe", "baidunetdisk.exe", // 百度网盘
			"115pc.exe", "115.exe", // 115网盘
			"360yunpan.exe", "360.exe", // 360云盘

			// ===== 虚拟机软件 =====
			"vmware.exe", "vmware-vmx.exe", "vmware-authd.exe",
			"VirtualBox.exe", "virtualbox.exe", "VBoxManage.exe",
			"Hyper-V.exe", "vmms.exe", "vmwp.exe",

			// ===== 其他敏感应用 =====
			"Steam.exe", "steam.exe", // Steam游戏平台
			"Epic Games Launcher.exe", "epicgameslauncher.exe",
			"Battle.net.exe", "battle.net.exe", // 暴雪战网
			"WeGame.exe", "wegame.exe", // 腾讯WeGame
			"everything",
		},
	}
}

// 分析进程风险等级
func (psa *ProcessSecurityAnalyzer) AnalyzeProcess(processName string) ProcessRiskInfo {
	processNameLower := strings.ToLower(processName)

	// 检查EDR进程
	for _, edr := range psa.edrProcesses {
		if strings.ToLower(edr) == processNameLower {
			return ProcessRiskInfo{
				RiskLevel:   "high",
				Category:    "edr",
				Description: "🚫 EDR/高级威胁检测",
				CanInject:   false,
				CanKill:     false,
				Reason:      "EDR进程，禁止操作",
			}
		}
	}

	// 检查AV进程
	for _, av := range psa.avProcesses {
		if strings.ToLower(av) == processNameLower {
			return ProcessRiskInfo{
				RiskLevel:   "high",
				Category:    "antivirus",
				Description: "🚫 杀毒软件",
				CanInject:   false,
				CanKill:     false,
				Reason:      "杀毒软件进程，禁止操作",
			}
		}
	}

	// 检查敏感信息进程
	for _, sensitive := range psa.sensitiveProcesses {
		if strings.ToLower(sensitive) == processNameLower {
			return ProcessRiskInfo{
				RiskLevel:   "sensitive",
				Category:    "sensitive",
				Description: "💎 敏感信息进程",
				CanInject:   true,
				CanKill:     true,
				Reason:      "包含敏感信息，高价值目标",
			}
		}
	}

	// 检查系统进程
	for _, sys := range psa.systemProcesses {
		if strings.ToLower(sys) == processNameLower {
			return ProcessRiskInfo{
				RiskLevel:   "medium",
				Category:    "system",
				Description: "⚠️ 系统关键进程",
				CanInject:   false,
				CanKill:     false,
				Reason:      "系统关键进程，禁止操作",
			}
		}
	}
	
	// 关键词检测
	edrKeywords := []string{"defender", "sentinel", "crowdstrike", "falcon", "cylance", "carbon", "tanium", "traps", "fortiedr"}
	for _, keyword := range edrKeywords {
		if strings.Contains(processNameLower, keyword) {
			return ProcessRiskInfo{
				RiskLevel:   "high",
				Category:    "edr_keyword",
				Description: "🚫 疑似EDR进程",
				CanInject:   false,
				CanKill:     false,
				Reason:      "进程名包含EDR关键词",
			}
		}
	}

	avKeywords := []string{"360", "antivirus", "kaspersky", "norton", "mcafee", "avast", "avg"}
	for _, keyword := range avKeywords {
		if strings.Contains(processNameLower, keyword) {
			return ProcessRiskInfo{
				RiskLevel:   "high",
				Category:    "av_keyword",
				Description: "🚫 疑似杀毒软件",
				CanInject:   false,
				CanKill:     false,
				Reason:      "进程名包含杀毒软件关键词",
			}
		}
	}

	// 敏感信息进程关键词检测
	sensitiveKeywords := []string{
		"chrome", "firefox", "edge", "opera", "brave", // 浏览器
		"notepad", "sublime", "vscode", "atom", // 编辑器
		"navicat", "dbeaver", "datagrip", "heidi", // 数据库工具
		"xshell", "xftp", "putty", "winscp", "filezilla", "finalshell", "windterm", // SSH/FTP
		"wechat", "dingtalk", "telegram", "whatsapp", "signal", "discord", "qq", // 通讯软件
		"sunlogin", "todesk", "teamviewer", "anydesk", // 远程控制
		"outlook", "thunderbird", "foxmail", // 邮件客户端
	}
	for _, keyword := range sensitiveKeywords {
		if strings.Contains(processNameLower, keyword) {
			return ProcessRiskInfo{
				RiskLevel:   "sensitive",
				Category:    "sensitive_keyword",
				Description: "💎 疑似敏感信息进程",
				CanInject:   true,
				CanKill:     true,
				Reason:      "进程名包含敏感信息关键词，可能包含有价值数据",
			}
		}
	}
	
	// 普通进程
	return ProcessRiskInfo{
		RiskLevel:   "low",
		Category:    "normal",
		Description: "✅ 普通进程",
		CanInject:   true,
		CanKill:     true,
		Reason:      "安全进程，可以操作",
	}
}

// 进程风险信息
type ProcessRiskInfo struct {
	RiskLevel   string `json:"risk_level"`   // high, medium, low
	Category    string `json:"category"`     // edr, antivirus, system, normal
	Description string `json:"description"`  // 显示描述
	CanInject   bool   `json:"can_inject"`   // 是否可以注入
	CanKill     bool   `json:"can_kill"`     // 是否可以结束
	Reason      string `json:"reason"`       // 原因说明
}

// 批量分析进程
func (psa *ProcessSecurityAnalyzer) AnalyzeProcessList(processes []map[string]interface{}) []map[string]interface{} {
	for i, proc := range processes {
		if name, ok := proc["name"].(string); ok {
			riskInfo := psa.AnalyzeProcess(name)
			processes[i]["risk_info"] = riskInfo
		}
	}
	return processes
}

// 获取安全统计信息
func (psa *ProcessSecurityAnalyzer) GetSecurityStats(processes []map[string]interface{}) map[string]interface{} {
	stats := map[string]interface{}{
		"total":           len(processes),
		"safe":            0,
		"medium":          0,
		"high":            0,
		"sensitive":       0,
		"edr_count":       0,
		"av_count":        0,
		"sys_count":       0,
		"sensitive_count": 0,
	}

	for _, proc := range processes {
		if riskInfo, ok := proc["risk_info"].(ProcessRiskInfo); ok {
			switch riskInfo.RiskLevel {
			case "low":
				stats["safe"] = stats["safe"].(int) + 1
			case "medium":
				stats["medium"] = stats["medium"].(int) + 1
			case "high":
				stats["high"] = stats["high"].(int) + 1
			case "sensitive":
				stats["sensitive"] = stats["sensitive"].(int) + 1
			}

			switch riskInfo.Category {
			case "edr", "edr_keyword":
				stats["edr_count"] = stats["edr_count"].(int) + 1
			case "antivirus", "av_keyword":
				stats["av_count"] = stats["av_count"].(int) + 1
			case "system":
				stats["sys_count"] = stats["sys_count"].(int) + 1
			case "sensitive", "sensitive_keyword":
				stats["sensitive_count"] = stats["sensitive_count"].(int) + 1
			}
		}
	}

	return stats
}

// 全局分析器实例
var globalAnalyzer = NewProcessSecurityAnalyzer()

// 获取全局分析器
func GetSecurityAnalyzer() *ProcessSecurityAnalyzer {
	return globalAnalyzer
}
