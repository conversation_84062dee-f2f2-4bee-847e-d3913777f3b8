package tunnel

import (
	"Spark/client/common"
	"Spark/modules"
	"Spark/utils"
	"fmt"
	"io"
	"net"
	"reflect"
	"sync"
	"time"

	"github.com/kataras/golog"
)

// ClientTunnelManager 客户端隧道管理器
type ClientTunnelManager struct {
	wsConn      *common.Conn               // WebSocket连接
	tunnels     map[string]*ClientTunnel   // tunnelID -> ClientTunnel
	connections map[string]net.Conn        // connectionID -> net.Conn
	mutex       sync.RWMutex
}

// ClientTunnel 客户端隧道实例
type ClientTunnel struct {
	ID          string                     // 隧道ID
	Config      *TunnelConfig              // 隧道配置
	Status      TunnelStatus               // 隧道状态
	Connections map[string]*TunnelConn     // 活跃连接
	Stats       *TunnelStats               // 统计信息
	mutex       sync.RWMutex
}

// TunnelConfig 隧道配置
type TunnelConfig struct {
	Name         string   `json:"name"`
	Description  string   `json:"description"`
	Type         string   `json:"type"`          // tcp/http/socks5
	Protocol     string   `json:"protocol"`      // tcp/udp
	RemotePort   int      `json:"remote_port"`
	LocalHost    string   `json:"local_host"`
	LocalPort    int      `json:"local_port"`
	Subdomain    string   `json:"subdomain"`
	CustomDomain string   `json:"custom_domain"`
	AuthRequired bool     `json:"auth_required"`
	AuthUsername string   `json:"auth_username"`
	AuthPassword string   `json:"auth_password"`
	Username     string   `json:"username"`      // SOCKS5用户名
	Password     string   `json:"password"`      // SOCKS5密码
	AllowedIPs   []string `json:"allowed_ips"`
	AutoStart    bool     `json:"auto_start"`
}

// TunnelStatus 隧道状态
type TunnelStatus string

const (
	TunnelStatusStopped  TunnelStatus = "stopped"
	TunnelStatusStarting TunnelStatus = "starting"
	TunnelStatusRunning  TunnelStatus = "running"
	TunnelStatusError    TunnelStatus = "error"
)

// TunnelConn 隧道连接
type TunnelConn struct {
	ID          string        // 连接ID
	LocalConn   net.Conn      // 本地连接
	RemoteAddr  string        // 远程地址
	StartTime   time.Time     // 开始时间
	BytesIn     int64         // 入流量
	BytesOut    int64         // 出流量
}

// TunnelStats 隧道统计
type TunnelStats struct {
	TotalConnections   int   `json:"total_connections"`
	ActiveConnections  int   `json:"active_connections"`
	TotalBytesIn      int64 `json:"total_bytes_in"`
	TotalBytesOut     int64 `json:"total_bytes_out"`
	LastConnectionTime int64 `json:"last_connection_time"`
	Uptime            int64 `json:"uptime"`
}

// 全局客户端隧道管理器
var GlobalClientTunnelManager *ClientTunnelManager

// InitClientTunnelManager 初始化客户端隧道管理器
func InitClientTunnelManager(wsConn *common.Conn) {
	GlobalClientTunnelManager = &ClientTunnelManager{
		wsConn:      wsConn,
		tunnels:     make(map[string]*ClientTunnel),
		connections: make(map[string]net.Conn),
	}

	// 注册事件处理器
	GlobalClientTunnelManager.registerEventHandlers()
	golog.Info("客户端隧道管理器初始化完成")
}

// registerEventHandlers 注册事件处理器
func (ctm *ClientTunnelManager) registerEventHandlers() {
	// 隧道创建请求
	ctm.wsConn.AddEventHandler(modules.Packet{
		Act: "TUNNEL_CREATE",
	}, func(pack modules.Packet, conn *common.Conn) {
		ctm.handleTunnelCreate(pack, conn)
	})

	// 隧道启动请求
	ctm.wsConn.AddEventHandler(modules.Packet{
		Act: "TUNNEL_START",
	}, func(pack modules.Packet, conn *common.Conn) {
		ctm.handleTunnelStart(pack, conn)
	})

	// 隧道停止请求
	ctm.wsConn.AddEventHandler(modules.Packet{
		Act: "TUNNEL_STOP",
	}, func(pack modules.Packet, conn *common.Conn) {
		ctm.handleTunnelStop(pack, conn)
	})

	// 隧道删除请求
	ctm.wsConn.AddEventHandler(modules.Packet{
		Act: "TUNNEL_DELETE",
	}, func(pack modules.Packet, conn *common.Conn) {
		ctm.handleTunnelDelete(pack, conn)
	})

	// 新连接建立
	ctm.wsConn.AddEventHandler(modules.Packet{
		Act: "TUNNEL_CONNECTION_NEW",
	}, func(pack modules.Packet, conn *common.Conn) {
		ctm.handleNewConnection(pack, conn)
	})

	// 连接数据传输
	ctm.wsConn.AddEventHandler(modules.Packet{
		Act: "TUNNEL_CONNECTION_DATA",
	}, func(pack modules.Packet, conn *common.Conn) {
		ctm.handleConnectionData(pack, conn)
	})

	// 连接关闭
	ctm.wsConn.AddEventHandler(modules.Packet{
		Act: "TUNNEL_CONNECTION_CLOSE",
	}, func(pack modules.Packet, conn *common.Conn) {
		ctm.handleConnectionClose(pack, conn)
	})

	// SOCKS5隧道连接请求
	ctm.wsConn.AddEventHandler(modules.Packet{
		Act: "SOCKS5_TUNNEL_CONNECT",
	}, func(pack modules.Packet, conn *common.Conn) {
		ctm.handleSOCKS5TunnelConnect(pack, conn)
	})

	// SOCKS5隧道数据传输（服务端到设备）
	ctm.wsConn.AddEventHandler(modules.Packet{
		Act: "SOCKS5_TUNNEL_DATA_TO_DEVICE",
	}, func(pack modules.Packet, conn *common.Conn) {
		ctm.handleSOCKS5TunnelDataToDevice(pack, conn)
	})
}

// handleTunnelCreate 处理隧道创建
func (ctm *ClientTunnelManager) handleTunnelCreate(pack modules.Packet, conn *common.Conn) {
	tunnelID, _ := pack.GetDataString("tunnel_id")
	configData, _ := pack.GetData("config", reflect.Slice)

	// 解析配置
	config := &TunnelConfig{}
	if configBytes, ok := configData.([]byte); ok {
		if err := utils.JSON.Unmarshal(configBytes, config); err != nil {
			golog.Errorf("解析隧道配置失败: %v", err)
			ctm.sendTunnelStatus(tunnelID, TunnelStatusError, err.Error())
			return
		}
	} else {
		golog.Error("隧道配置格式错误")
		ctm.sendTunnelStatus(tunnelID, TunnelStatusError, "配置格式错误")
		return
	}

	// 创建隧道实例
	tunnel := &ClientTunnel{
		ID:          tunnelID,
		Config:      config,
		Status:      TunnelStatusStopped,
		Connections: make(map[string]*TunnelConn),
		Stats:       &TunnelStats{},
	}

	ctm.mutex.Lock()
	ctm.tunnels[tunnelID] = tunnel
	ctm.mutex.Unlock()

	golog.Infof("隧道创建成功 [ID=%s, Type=%s]", tunnelID, config.Type)
	ctm.sendTunnelStatus(tunnelID, TunnelStatusStopped, "")
}

// handleTunnelStart 处理隧道启动
func (ctm *ClientTunnelManager) handleTunnelStart(pack modules.Packet, conn *common.Conn) {
	tunnelID, _ := pack.GetDataString("tunnel_id")

	ctm.mutex.RLock()
	tunnel, exists := ctm.tunnels[tunnelID]
	ctm.mutex.RUnlock()

	if !exists {
		golog.Errorf("隧道不存在: %s", tunnelID)
		ctm.sendTunnelStatus(tunnelID, TunnelStatusError, "隧道不存在")
		return
	}

	tunnel.mutex.Lock()
	tunnel.Status = TunnelStatusRunning
	tunnel.mutex.Unlock()

	golog.Infof("隧道启动成功 [ID=%s]", tunnelID)
	ctm.sendTunnelStatus(tunnelID, TunnelStatusRunning, "")
}

// handleTunnelStop 处理隧道停止
func (ctm *ClientTunnelManager) handleTunnelStop(pack modules.Packet, conn *common.Conn) {
	tunnelID, _ := pack.GetDataString("tunnel_id")

	ctm.mutex.RLock()
	tunnel, exists := ctm.tunnels[tunnelID]
	ctm.mutex.RUnlock()

	if !exists {
		golog.Errorf("隧道不存在: %s", tunnelID)
		return
	}

	tunnel.mutex.Lock()
	// 关闭所有连接
	for _, conn := range tunnel.Connections {
		if conn.LocalConn != nil {
			conn.LocalConn.Close()
		}
	}
	tunnel.Connections = make(map[string]*TunnelConn)
	tunnel.Status = TunnelStatusStopped
	tunnel.mutex.Unlock()

	golog.Infof("隧道停止成功 [ID=%s]", tunnelID)
	ctm.sendTunnelStatus(tunnelID, TunnelStatusStopped, "")
}

// handleTunnelDelete 处理隧道删除
func (ctm *ClientTunnelManager) handleTunnelDelete(pack modules.Packet, conn *common.Conn) {
	tunnelID, _ := pack.GetDataString("tunnel_id")

	ctm.mutex.Lock()
	tunnel, exists := ctm.tunnels[tunnelID]
	if exists {
		// 先停止隧道
		tunnel.mutex.Lock()
		for _, conn := range tunnel.Connections {
			if conn.LocalConn != nil {
				conn.LocalConn.Close()
			}
		}
		tunnel.mutex.Unlock()

		// 删除隧道
		delete(ctm.tunnels, tunnelID)
	}
	ctm.mutex.Unlock()

	golog.Infof("隧道删除成功 [ID=%s]", tunnelID)
}

// handleNewConnection 处理新连接
func (ctm *ClientTunnelManager) handleNewConnection(pack modules.Packet, conn *common.Conn) {
	tunnelID, _ := pack.GetDataString("tunnel_id")
	connectionID, _ := pack.GetDataString("connection_id")

	ctm.mutex.RLock()
	tunnel, exists := ctm.tunnels[tunnelID]
	ctm.mutex.RUnlock()

	if !exists {
		golog.Errorf("隧道不存在: %s", tunnelID)
		ctm.sendConnectionResponse(connectionID, "reject", "隧道不存在")
		return
	}

	if tunnel.Status != TunnelStatusRunning {
		golog.Errorf("隧道未运行: %s", tunnelID)
		ctm.sendConnectionResponse(connectionID, "reject", "隧道未运行")
		return
	}

	// 对于 SOCKS5 隧道，不需要预先建立本地连接
	// 连接将在收到 SOCKS5 请求时动态建立
	var localConn net.Conn
	var err error

	if tunnel.Config.Type == "socks5" {
		// SOCKS5 隧道使用虚拟连接，实际连接在 SOCKS5 握手后建立
		localConn = &VirtualConn{connectionID: connectionID, manager: ctm}
	} else {
		// 其他类型隧道建立到本地服务的连接
		localAddr := fmt.Sprintf("%s:%d", tunnel.Config.LocalHost, tunnel.Config.LocalPort)
		localConn, err = net.Dial("tcp", localAddr)
		if err != nil {
			golog.Errorf("连接本地服务失败 [%s]: %v", localAddr, err)
			ctm.sendConnectionResponse(connectionID, "reject", err.Error())
			return
		}
	}

	// 创建连接记录
	tunnelConn := &TunnelConn{
		ID:        connectionID,
		LocalConn: localConn,
		StartTime: time.Now(),
	}

	tunnel.mutex.Lock()
	tunnel.Connections[connectionID] = tunnelConn
	tunnel.Stats.ActiveConnections++
	tunnel.Stats.TotalConnections++
	tunnel.mutex.Unlock()

	ctm.mutex.Lock()
	ctm.connections[connectionID] = localConn
	ctm.mutex.Unlock()

	// 确认连接建立
	ctm.sendConnectionResponse(connectionID, "accept", "")

	// 启动数据转发
	go ctm.forwardLocalToRemote(tunnelConn, tunnel)

	golog.Infof("新连接建立 [TunnelID=%s, ConnID=%s]", tunnelID, connectionID)
}

// handleConnectionData 处理连接数据
func (ctm *ClientTunnelManager) handleConnectionData(pack modules.Packet, conn *common.Conn) {
	connectionID, _ := pack.GetDataString("connection_id")
	data, _ := pack.GetDataBytes("data")

	ctm.mutex.RLock()
	localConn, exists := ctm.connections[connectionID]
	ctm.mutex.RUnlock()

	if !exists {
		golog.Warnf("连接不存在: %s", connectionID)
		return
	}

	// 写入本地连接
	_, err := localConn.Write(data)
	if err != nil {
		golog.Errorf("写入本地连接失败: %v", err)
		ctm.closeConnection(connectionID)
	}
}

// handleConnectionClose 处理连接关闭
func (ctm *ClientTunnelManager) handleConnectionClose(pack modules.Packet, conn *common.Conn) {
	connectionID, _ := pack.GetDataString("connection_id")
	ctm.closeConnection(connectionID)
}

// forwardLocalToRemote 转发本地数据到远程
func (ctm *ClientTunnelManager) forwardLocalToRemote(tunnelConn *TunnelConn, tunnel *ClientTunnel) {
	defer ctm.closeConnection(tunnelConn.ID)

	buffer := make([]byte, 32*1024) // 32KB缓冲区

	for {
		n, err := tunnelConn.LocalConn.Read(buffer)
		if err != nil {
			golog.Infof("本地连接关闭 [ConnID=%s]: %v", tunnelConn.ID, err)
			break
		}

		// 更新统计
		tunnelConn.BytesOut += int64(n)
		tunnel.Stats.TotalBytesOut += int64(n)

		// 发送数据到服务端
		data := make([]byte, n)
		copy(data, buffer[:n])

		ctm.wsConn.SendPack(modules.Packet{
			Act:   "TUNNEL_CONNECTION_DATA",
			Event: tunnelConn.ID,
			Data: map[string]any{
				"tunnel_id":     tunnel.ID,
				"connection_id": tunnelConn.ID,
				"data":          data,
			},
		})
	}
}

// VirtualConn SOCKS5 虚拟连接，用于处理动态目标连接
type VirtualConn struct {
	connectionID string
	manager      *ClientTunnelManager
	targetConn   net.Conn
	closed       bool
	mutex        sync.Mutex
}

// Read 实现 net.Conn 接口
func (vc *VirtualConn) Read(b []byte) (n int, err error) {
	vc.mutex.Lock()
	defer vc.mutex.Unlock()

	if vc.closed {
		return 0, io.EOF
	}

	if vc.targetConn == nil {
		return 0, fmt.Errorf("目标连接未建立")
	}

	return vc.targetConn.Read(b)
}

// Write 实现 net.Conn 接口
func (vc *VirtualConn) Write(b []byte) (n int, err error) {
	vc.mutex.Lock()
	defer vc.mutex.Unlock()

	if vc.closed {
		return 0, io.EOF
	}

	if vc.targetConn == nil {
		return 0, fmt.Errorf("目标连接未建立")
	}

	return vc.targetConn.Write(b)
}

// Close 实现 net.Conn 接口
func (vc *VirtualConn) Close() error {
	vc.mutex.Lock()
	defer vc.mutex.Unlock()

	if vc.closed {
		return nil
	}

	vc.closed = true

	if vc.targetConn != nil {
		return vc.targetConn.Close()
	}

	return nil
}

// LocalAddr 实现 net.Conn 接口
func (vc *VirtualConn) LocalAddr() net.Addr {
	if vc.targetConn != nil {
		return vc.targetConn.LocalAddr()
	}
	return nil
}

// RemoteAddr 实现 net.Conn 接口
func (vc *VirtualConn) RemoteAddr() net.Addr {
	if vc.targetConn != nil {
		return vc.targetConn.RemoteAddr()
	}
	return nil
}

// SetDeadline 实现 net.Conn 接口
func (vc *VirtualConn) SetDeadline(t time.Time) error {
	if vc.targetConn != nil {
		return vc.targetConn.SetDeadline(t)
	}
	return nil
}

// SetReadDeadline 实现 net.Conn 接口
func (vc *VirtualConn) SetReadDeadline(t time.Time) error {
	if vc.targetConn != nil {
		return vc.targetConn.SetReadDeadline(t)
	}
	return nil
}

// SetWriteDeadline 实现 net.Conn 接口
func (vc *VirtualConn) SetWriteDeadline(t time.Time) error {
	if vc.targetConn != nil {
		return vc.targetConn.SetWriteDeadline(t)
	}
	return nil
}

// ConnectToTarget 连接到目标地址（用于 SOCKS5）
func (vc *VirtualConn) ConnectToTarget(address string) error {
	vc.mutex.Lock()
	defer vc.mutex.Unlock()

	if vc.closed {
		return fmt.Errorf("连接已关闭")
	}

	if vc.targetConn != nil {
		return fmt.Errorf("目标连接已存在")
	}

	conn, err := net.Dial("tcp", address)
	if err != nil {
		return err
	}

	vc.targetConn = conn
	return nil
}

// closeConnection 关闭连接
func (ctm *ClientTunnelManager) closeConnection(connectionID string) {
	ctm.mutex.Lock()
	localConn, exists := ctm.connections[connectionID]
	if exists {
		localConn.Close()
		delete(ctm.connections, connectionID)
	}
	ctm.mutex.Unlock()

	// 从隧道中移除连接
	ctm.mutex.RLock()
	for _, tunnel := range ctm.tunnels {
		tunnel.mutex.Lock()
		if _, exists := tunnel.Connections[connectionID]; exists {
			delete(tunnel.Connections, connectionID)
			tunnel.Stats.ActiveConnections--
			golog.Infof("连接关闭 [TunnelID=%s, ConnID=%s]", tunnel.ID, connectionID)
		}
		tunnel.mutex.Unlock()
	}
	ctm.mutex.RUnlock()

	// 通知服务端连接关闭
	ctm.wsConn.SendPack(modules.Packet{
		Act:   "TUNNEL_CONNECTION_CLOSE",
		Event: connectionID,
		Data: map[string]any{
			"connection_id": connectionID,
		},
	})
}

// sendTunnelStatus 发送隧道状态
func (ctm *ClientTunnelManager) sendTunnelStatus(tunnelID string, status TunnelStatus, error string) {
	ctm.wsConn.SendPack(modules.Packet{
		Act:   "TUNNEL_STATUS",
		Event: tunnelID,
		Data: map[string]any{
			"tunnel_id": tunnelID,
			"status":    string(status),
			"error":     error,
		},
	})
}

// sendConnectionResponse 发送连接响应
func (ctm *ClientTunnelManager) sendConnectionResponse(connectionID, action, reason string) {
	ctm.wsConn.SendPack(modules.Packet{
		Act:   "TUNNEL_CONNECTION_RESPONSE",
		Event: connectionID,
		Data: map[string]any{
			"connection_id": connectionID,
			"action":        action,
			"reason":        reason,
		},
	})
}

// handleSOCKS5TunnelConnect 处理SOCKS5隧道连接请求
func (ctm *ClientTunnelManager) handleSOCKS5TunnelConnect(pack modules.Packet, conn *common.Conn) {
	tunnelID, _ := pack.GetDataString("tunnel_id")
	connID, _ := pack.GetDataString("connection_id")
	targetAddr, _ := pack.GetDataString("target_addr")

	golog.Infof("收到SOCKS5隧道连接请求 [TunnelID=%s, ConnID=%s, Target=%s]", tunnelID, connID, targetAddr)

	// 建立到目标地址的连接
	targetConn, err := net.Dial("tcp", targetAddr)
	if err != nil {
		golog.Errorf("连接目标地址失败 [%s]: %v", targetAddr, err)
		// 发送连接失败响应
		failurePacket := modules.Packet{
			Act: "SOCKS5_TUNNEL_CONNECT_RESPONSE",
			Data: map[string]any{
				"tunnel_id":     tunnelID,
				"connection_id": connID,
				"target_addr":   targetAddr,
				"success":       false,
				"error":         err.Error(),
			},
		}
		golog.Infof("发送SOCKS5连接失败响应: %+v", failurePacket)
		ctm.wsConn.SendPack(failurePacket)
		return
	}

	// 存储连接映射（使用连接ID作为键）
	ctm.mutex.Lock()
	ctm.connections[connID] = targetConn
	ctm.mutex.Unlock()

	// 发送连接成功响应
	responsePacket := modules.Packet{
		Act: "SOCKS5_TUNNEL_CONNECT_RESPONSE",
		Data: map[string]any{
			"tunnel_id":     tunnelID,
			"connection_id": connID,
			"target_addr":   targetAddr,
			"success":       true,
		},
	}
	golog.Infof("发送SOCKS5连接成功响应: %+v", responsePacket)
	ctm.wsConn.SendPack(responsePacket)

	golog.Infof("SOCKS5隧道连接建立成功 [TunnelID=%s, ConnID=%s, Target=%s]", tunnelID, connID, targetAddr)

	// 启动数据转发
	go ctm.forwardSOCKS5Data(tunnelID, connID, targetAddr, targetConn)

}

// forwardSOCKS5Data 转发SOCKS5数据
func (ctm *ClientTunnelManager) forwardSOCKS5Data(tunnelID, connID, targetAddr string, targetConn net.Conn) {
	defer func() {
		targetConn.Close()
		// 清理连接映射
		ctm.mutex.Lock()
		delete(ctm.connections, connID)
		ctm.mutex.Unlock()
		golog.Infof("目标连接关闭 [TunnelID=%s, ConnID=%s, Target=%s]", tunnelID, connID, targetAddr)
	}()

	buffer := make([]byte, 32*1024) // 32KB缓冲区

	for {
		n, err := targetConn.Read(buffer)
		if err != nil {
			golog.Infof("目标连接关闭 [TunnelID=%s, Target=%s]: %v", tunnelID, targetAddr, err)
			break
		}

		// 发送数据到服务端
		data := make([]byte, n)
		copy(data, buffer[:n])

		ctm.wsConn.SendPack(modules.Packet{
			Act: "SOCKS5_TUNNEL_DATA",
			Data: map[string]any{
				"tunnel_id":     tunnelID,
				"connection_id": connID,
				"target_addr":   targetAddr,
				"data":          data,
			},
		})
		golog.Debugf("转发SOCKS5数据到服务端 [TunnelID=%s, ConnID=%s, Target=%s, Size=%d]", tunnelID, connID, targetAddr, len(data))
	}

	// 通知服务端连接关闭
	ctm.wsConn.SendPack(modules.Packet{
		Act: "SOCKS5_TUNNEL_CLOSE",
		Data: map[string]any{
			"tunnel_id":   tunnelID,
			"target_addr": targetAddr,
		},
	})
}

// handleSOCKS5TunnelDataToDevice 处理从服务端发来的SOCKS5隧道数据
func (ctm *ClientTunnelManager) handleSOCKS5TunnelDataToDevice(pack modules.Packet, conn *common.Conn) {
	tunnelID, _ := pack.GetDataString("tunnel_id")
	connID, _ := pack.GetDataString("connection_id")
	data, _ := pack.GetDataBytes("data")

	golog.Debugf("收到SOCKS5隧道数据 [TunnelID=%s, ConnID=%s, Size=%d]", tunnelID, connID, len(data))

	// 查找对应的目标连接
	ctm.mutex.RLock()
	targetConn, exists := ctm.connections[connID]
	ctm.mutex.RUnlock()

	if !exists {
		golog.Warnf("目标连接不存在 [TunnelID=%s, ConnID=%s]", tunnelID, connID)
		return
	}

	// 写入数据到目标连接
	_, err := targetConn.Write(data)
	if err != nil {
		golog.Errorf("写入目标连接失败 [TunnelID=%s, ConnID=%s]: %v", tunnelID, connID, err)
		// 清理连接
		ctm.mutex.Lock()
		delete(ctm.connections, connID)
		ctm.mutex.Unlock()
		targetConn.Close()
	} else {
		golog.Debugf("SOCKS5数据转发成功 [TunnelID=%s, ConnID=%s, Size=%d]", tunnelID, connID, len(data))
	}
}

// HandleTunnelEvent 处理隧道相关事件
func HandleTunnelEvent(pack modules.Packet, wsConn *common.Conn) {
	if GlobalClientTunnelManager == nil {
		golog.Error("隧道管理器未初始化")
		return
	}

	switch pack.Act {
	case "TUNNEL_CREATE":
		GlobalClientTunnelManager.handleTunnelCreate(pack, wsConn)
	case "TUNNEL_START":
		GlobalClientTunnelManager.handleTunnelStart(pack, wsConn)
	case "TUNNEL_STOP":
		GlobalClientTunnelManager.handleTunnelStop(pack, wsConn)
	case "TUNNEL_DELETE":
		GlobalClientTunnelManager.handleTunnelDelete(pack, wsConn)
	case "TUNNEL_CONNECTION_DATA":
		GlobalClientTunnelManager.handleConnectionData(pack, wsConn)
	case "TUNNEL_CONNECTION_NEW":
		GlobalClientTunnelManager.handleNewConnection(pack, wsConn)
	case "TUNNEL_CONNECTION_CLOSE":
		GlobalClientTunnelManager.handleConnectionClose(pack, wsConn)
	case "SOCKS5_TUNNEL_CONNECT":
		GlobalClientTunnelManager.handleSOCKS5TunnelConnect(pack, wsConn)
	case "SOCKS5_TUNNEL_DATA_TO_DEVICE":
		GlobalClientTunnelManager.handleSOCKS5TunnelDataToDevice(pack, wsConn)
	default:
		golog.Warnf("未知的隧道事件: %s", pack.Act)
	}
}
