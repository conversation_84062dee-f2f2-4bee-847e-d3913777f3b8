package common

import (
	"Spark/modules"
	"Spark/utils/cmap"
	"Spark/utils/melody"
	"time"
)

type EventCallback func(modules.Packet, *melody.Session)
type event struct {
	connection string
	callback   EventCallback
	finish     chan bool
	remove     chan bool
}

var events = cmap.New[*event]()

// CallEvent tries to call the callback with the given uuid
// after that, it will notify the caller via the channel
func CallEvent(pack modules.Packet, session *melody.Session) {
	// 首先尝试基于Event字段的事件处理（旧系统）
	if len(pack.Event) > 0 {
		ev, ok := events.Get(pack.Event)
		if ok {
			if session == nil || session.UUID == ev.connection {
				ev.callback(pack, session)
				if ev.finish != nil {
					ev.finish <- true
				}
				return
			}
		}
	}

	// 然后尝试基于Act字段的事件处理（新系统）
	if len(pack.Act) > 0 {
		ev, ok := events.Get(pack.Act)
		if ok {
			if session == nil || session.UUID == ev.connection || ev.connection == "" {
				ev.callback(pack, session)
				if ev.finish != nil {
					ev.finish <- true
				}
				return
			}
		}
	}
}

// AddEventOnce adds a new event only once and client
// can call back the event with the given event trigger.
// Event trigger should be uuid to make every event unique.
func AddEventOnce(fn EventCallback, connUUID, trigger string, timeout time.Duration) bool {
	ev := &event{
		connection: connUUID,
		callback:   fn,
		finish:     make(chan bool),
		remove:     make(chan bool),
	}
	events.Set(trigger, ev)
	defer close(ev.remove)
	defer close(ev.finish)
	select {
	case ok := <-ev.finish:
		events.Remove(trigger)
		return ok
	case ok := <-ev.remove:
		events.Remove(trigger)
		return ok
	case <-time.After(timeout):
		events.Remove(trigger)
		return false
	}
}

// AddEvent adds a new event and client can call back
// the event with the given event trigger.
func AddEvent(fn EventCallback, connUUID, trigger string) {
	ev := &event{
		connection: connUUID,
		callback:   fn,
	}
	events.Set(trigger, ev)
}

// RemoveEvent deletes the event with the given event trigger.
// The ok will be returned to caller if the event is temp (only once).
func RemoveEvent(trigger string, ok ...bool) {
	ev, found := events.Get(trigger)
	if !found {
		return
	}
	events.Remove(trigger)
	if ev.remove != nil {
		if len(ok) > 0 {
			ev.remove <- ok[0]
		} else {
			ev.remove <- false
		}
	}
	ev = nil
}

// AddEventHandler 注册事件处理函数
// pattern是事件的模式，比如{Act: "XX", Event: "YY"}
// handler是事件处理函数
func AddEventHandler(pattern modules.Packet, handler EventCallback) {
	if len(pattern.Act) == 0 {
		return
	}

	// 注册事件处理函数到全局事件映射
	// 使用Act作为事件标识符
	AddEvent(handler, "", pattern.Act)
}



// HasEvent returns if the event exists.
func HasEvent(trigger string) bool {
	return events.Has(trigger)
}
