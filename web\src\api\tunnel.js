import request from '@/utils/request'

// 隧道管理API
export const tunnelApi = {
  // 获取隧道列表
  getTunnels(params) {
    return request({
      url: '/api/tunnels',
      method: 'get',
      params
    })
  },

  // 创建隧道
  createTunnel(deviceUuid, data) {
    return request({
      url: `/api/devices/${deviceUuid}/tunnels`,
      method: 'post',
      data
    })
  },

  // 更新隧道
  updateTunnel(tunnelId, data) {
    return request({
      url: `/api/tunnels/${tunnelId}`,
      method: 'put',
      data
    })
  },

  // 删除隧道
  deleteTunnel(tunnelId) {
    return request({
      url: `/api/tunnels/${tunnelId}`,
      method: 'delete'
    })
  },

  // 切换隧道状态
  toggleTunnel(tunnelId) {
    return request({
      url: `/api/tunnels/${tunnelId}/toggle`,
      method: 'post'
    })
  },

  // 获取隧道详情
  getTunnelDetail(tunnelId) {
    return request({
      url: `/api/tunnels/${tunnelId}`,
      method: 'get'
    })
  }
}

export default tunnelApi
