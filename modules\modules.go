package modules

import "reflect"

type Packet struct {
	Code  int            `json:"code"`
	Act   string         `json:"act,omitempty"`
	Msg   string         `json:"msg,omitempty"`
	Data  map[string]any `json:"data,omitempty"`
	Event string         `json:"event,omitempty"`
}

type CommonPack struct {
	Code  int    `json:"code"`
	Act   string `json:"act,omitempty"`
	Msg   string `json:"msg,omitempty"`
	Data  any    `json:"data,omitempty"`
	Event string `json:"event,omitempty"`
}

type Device struct {
	ID       string `json:"id"`
	OS       string `json:"os"`
	Arch     string `json:"arch"`
	LAN      string `json:"lan"`
	WAN      string `json:"wan"`
	MAC      string `json:"mac"`
	Net      Net    `json:"net"`
	CPU      CPU    `json:"cpu"`
	RAM      IO     `json:"ram"`
	Disk     IO     `json:"disk"`
	Uptime   uint64 `json:"uptime"`
	Latency  uint   `json:"latency"`
	Hostname string `json:"hostname"`
	Username string `json:"username"`
}

type IO struct {
	Total uint64  `json:"total"`
	Used  uint64  `json:"used"`
	Usage float64 `json:"usage"`
}

type CPU struct {
	Model string  `json:"model"`
	Usage float64 `json:"usage"`
	Cores struct {
		Logical  int `json:"logical"`
		Physical int `json:"physical"`
	} `json:"cores"`
}

type Net struct {
	Sent uint64 `json:"sent"`
	Recv uint64 `json:"recv"`
}

func (p *Packet) GetData(key string, t reflect.Kind) (any, bool) {
	if p.Data == nil {
		return nil, false
	}
	data, ok := p.Data[key]
	if !ok {
		return nil, false
	}
	switch t {
	case reflect.String:
		val, ok := data.(string)
		return val, ok
	case reflect.Uint:
		val, ok := data.(uint)
		return val, ok
	case reflect.Uint32:
		val, ok := data.(uint32)
		return val, ok
	case reflect.Uint64:
		val, ok := data.(uint64)
		return val, ok
	case reflect.Int:
		val, ok := data.(int)
		return val, ok
	case reflect.Int64:
		val, ok := data.(int64)
		return val, ok
	case reflect.Bool:
		val, ok := data.(bool)
		return val, ok
	case reflect.Float64:
		val, ok := data.(float64)
		return val, ok
	default:
		return nil, false
	}
}

func (p *Packet) GetDataInt(key string) (int, bool) {
	v, ok := p.GetData(key, reflect.Int)
	if !ok {
		// 尝试从float64转换，因为JSON解析可能将数字解析为float64
		if val, ok := p.Data[key].(float64); ok {
			return int(val), true
		}
		return 0, false
	}
	return v.(int), ok
}

func (p *Packet) GetDataString(key string) (string, bool) {
	v, ok := p.GetData(key, reflect.String)
	if !ok {
		return "", false
	}
	return v.(string), ok
}

func (p *Packet) GetDataBool(key string) (bool, bool) {
	v, ok := p.GetData(key, reflect.Bool)
	if !ok {
		return false, false
	}
	return v.(bool), ok
}

func (p *Packet) GetDataBytes(key string) ([]byte, bool) {
	if p.Data == nil {
		return nil, false
	}
	
	v, ok := p.Data[key]
	if !ok {
		return nil, false
	}
	
	// 处理不同类型的二进制数据表示
	switch data := v.(type) {
	case []byte:
		return data, true
	case string:
		return []byte(data), true
	case []interface{}:
		// 处理JSON中可能解析为[]interface{}的情况
		bytes := make([]byte, len(data))
		for i, b := range data {
			if val, ok := b.(float64); ok {
				bytes[i] = byte(val)
			} else {
				return nil, false
			}
		}
		return bytes, true
	default:
		return nil, false
	}
}

// ConvertToPacket 将CommonPack转换为Packet
func (cp *CommonPack) ConvertToPacket() Packet {
	packet := Packet{
		Code:  cp.Code,
		Act:   cp.Act,
		Msg:   cp.Msg,
		Event: cp.Event,
	}
	
	// 处理Data字段
	if cp.Data != nil {
		if mapData, ok := cp.Data.(map[string]any); ok {
			packet.Data = mapData
		} else {
			// 如果不是map类型，创建一个新map并包含原始数据
			packet.Data = map[string]any{
				"data": cp.Data,
			}
		}
	}
	
	return packet
}
