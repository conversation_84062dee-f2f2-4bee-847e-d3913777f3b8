import { Modal } from "antd";
import React, { useRef, useState } from "react";
import Draggable from "react-draggable";

function DraggableModal(props) {
	const [disabled, setDisabled] = useState(true);
	const [bounds, setBounds] = useState({
		left: 0,
		top: 0,
		bottom: 0,
		right: 0,
	});
	const draggableRef = useRef(null);

	const onStart = (_event, uiData) => {
		const { clientWidth, clientHeight } = window.document.documentElement;
		const targetRect = draggableRef.current?.getBoundingClientRect();
		if (!targetRect || disabled) {
			return;
		}

		setBounds({
			left: -targetRect.left + uiData.x,
			right: clientWidth - (targetRect.right - uiData.x),
			top: -targetRect.top + uiData.y,
			bottom: clientHeight - (targetRect.bottom - uiData.y),
		});
	};

	return (
		<Modal
			title={
				<div
					style={{
						width: '100%',
						cursor: 'move',
					}}
					onMouseOver={() => {
						if (disabled && props.draggable) setDisabled(false);
					}}
					onMouseOut={() => {
						setDisabled(true);
					}}
					onFocus={() => {}}
					onBlur={() => {
						setDisabled(true);
					}}
				>
					{props.modalTitle}
				</div>
			}
			modalRender={(modal) => (
				<Draggable
					disabled={disabled || !props.draggable}
					bounds={bounds}
					onStart={(event, uiData) => onStart(event, uiData)}
				>
					<div ref={draggableRef}>{modal}</div>
				</Draggable>
			)}
			{...props}
		>
			{props.children}
		</Modal>
	);
};

export default DraggableModal;