package proxy

import (
	"Spark/modules"
	"Spark/server/common"
	"Spark/utils"
	"Spark/utils/melody"
	"io"
	"net"
	"sync"
	"time"

	"github.com/kataras/golog"
)

var (
	// 存储活跃的端口转发
	proxyForwards = make(map[string]*ProxyForward)
	proxyMutex    sync.Mutex
)

// ProxyForward 表示一个活跃的端口转发
type ProxyForward struct {
	EventID   string
	Session   *melody.Session
	Port      int
	Listener  net.Listener
	Clients   map[string]net.Conn
	ClientsMu sync.Mutex
}

// 处理SOCKS5代理端口转发请求
func HandlePortForward(pack modules.Packet, session *melody.Session) {
	eventID := pack.Event
	if eventID == "" {
		eventID = "default"
	}

	// 获取客户端请求的端口
	port, ok := pack.GetDataInt("port")
	if !ok {
		common.SendPack(modules.Packet{
			Code: 1,
			Msg:  "Missing port parameter",
			Event: pack.Event,
		}, session)
		return
	}

	// 创建并启动端口转发
	err := startPortForward(eventID, port, session)
	if err != nil {
		common.SendPack(modules.Packet{
			Code: 1,
			Msg:  err.Error(),
			Event: pack.Event,
		}, session)
		return
	}

	common.SendPack(modules.Packet{
		Code: 0,
		Data: map[string]any{
			"status": "Port forwarding started",
			"port":   port,
		},
		Event: pack.Event,
	}, session)
}

// 启动端口转发
func startPortForward(eventID string, port int, session *melody.Session) error {
	proxyMutex.Lock()
	defer proxyMutex.Unlock()

	// 检查是否已经存在相同的端口转发
	if proxy, exists := proxyForwards[eventID]; exists {
		// 如果已存在，停止之前的
		stopProxyForward(proxy)
	}

	// 在服务器上监听指定端口
	listener, err := net.Listen("tcp", ":"+utils.ToString(port))
	if err != nil {
		return err
	}

	// 创建转发实例
	proxy := &ProxyForward{
		EventID:  eventID,
		Session:  session,
		Port:     port,
		Listener: listener,
		Clients:  make(map[string]net.Conn),
	}
	proxyForwards[eventID] = proxy

	// 启动端口监听
	go handleProxyConnections(proxy)

	golog.Infof("SOCKS5代理端口转发已启动 [EventID=%s, Port=%d]", eventID, port)
	return nil
}

// 处理代理连接
func handleProxyConnections(proxy *ProxyForward) {
	for {
		// 接受来自客户端的连接
		client, err := proxy.Listener.Accept()
		if err != nil {
			golog.Errorf("接受连接失败: %v", err)
			break
		}

		// 为每个连接创建一个唯一ID
		connID := utils.GetUUIDStr()

		// 保存客户端连接
		proxy.ClientsMu.Lock()
		proxy.Clients[connID] = client
		proxy.ClientsMu.Unlock()

		// 处理客户端连接
		go handleClientConnection(proxy, client, connID)
	}
}

// 处理客户端连接的数据传输
func handleClientConnection(proxy *ProxyForward, client net.Conn, connID string) {
	// 在函数结束时关闭连接并清理
	defer func() {
		client.Close()
		proxy.ClientsMu.Lock()
		delete(proxy.Clients, connID)
		proxy.ClientsMu.Unlock()
	}()

	// 创建用于通信的通道
	dataChan := make(chan []byte)
	errChan := make(chan error)

	// 向客户端发送连接建立通知
	common.SendPack(modules.Packet{
		Act:   "SOCKS5_PROXY_CONNECTION",
		Event: proxy.EventID,
		Data: map[string]any{
			"conn_id": connID,
			"action":  "connect",
		},
	}, proxy.Session)

	// 启动从WebSocket接收数据的goroutine
	go func() {
		// 当代理连接关闭时，退出goroutine
		common.AddEventHandler(modules.Packet{
			Act:   "SOCKS5_PROXY_DATA",
			Event: proxy.EventID,
		}, func(p modules.Packet, _ *melody.Session) {
			if cid, ok := p.GetDataString("conn_id"); ok && cid == connID {
				if data, ok := p.GetDataBytes("data"); ok {
					dataChan <- data
				}
			}
		})

		// 处理连接关闭
		common.AddEventHandler(modules.Packet{
			Act:   "SOCKS5_PROXY_CLOSE",
			Event: proxy.EventID,
		}, func(p modules.Packet, _ *melody.Session) {
			if cid, ok := p.GetDataString("conn_id"); ok && cid == connID {
				errChan <- nil // 正常关闭
			}
		})
	}()

	// 从客户端读取数据并转发到WebSocket
	go func() {
		buffer := make([]byte, 32*1024) // 32KB缓冲区
		for {
			n, err := client.Read(buffer)
			if err != nil {
				if err != io.EOF {
					golog.Errorf("从代理客户端读取数据错误: %v", err)
				}
				errChan <- err
				return
			}

			// 复制数据，防止缓冲区被覆盖
			data := make([]byte, n)
			copy(data, buffer[:n])

			// 通过WebSocket发送数据到客户端
			common.SendPack(modules.Packet{
				Act:   "SOCKS5_PROXY_DATA",
				Event: proxy.EventID,
				Data: map[string]any{
					"conn_id": connID,
					"data":    data,
				},
			}, proxy.Session)
		}
	}()

	// 主循环 - 处理数据流
	for {
		select {
		case data := <-dataChan:
			// 将从WebSocket接收的数据写入客户端连接
			_, err := client.Write(data)
			if err != nil {
				golog.Errorf("写入代理客户端数据错误: %v", err)
				return
			}
		case err := <-errChan:
			if err != nil && err != io.EOF {
				golog.Errorf("代理连接错误: %v", err)
			}
			
			// 发送连接关闭通知
			common.SendPack(modules.Packet{
				Act:   "SOCKS5_PROXY_CONNECTION",
				Event: proxy.EventID,
				Data: map[string]any{
					"conn_id": connID,
					"action":  "close",
				},
			}, proxy.Session)
			return
		case <-time.After(5 * time.Hour): // 设置最长连接时间
			golog.Warnf("代理连接超时关闭 [ConnID=%s]", connID)
			return
		}
	}
}

// 停止端口转发
func stopProxyForward(proxy *ProxyForward) {
	// 关闭监听器
	if proxy.Listener != nil {
		proxy.Listener.Close()
	}

	// 关闭所有客户端连接
	proxy.ClientsMu.Lock()
	for _, conn := range proxy.Clients {
		conn.Close()
	}
	proxy.ClientsMu.Unlock()

	// 从映射中删除
	delete(proxyForwards, proxy.EventID)

	golog.Infof("SOCKS5代理端口转发已停止 [EventID=%s, Port=%d]", proxy.EventID, proxy.Port)
}

// 处理停止端口转发请求
func HandleStopPortForward(pack modules.Packet, session *melody.Session) {
	eventID := pack.Event
	if eventID == "" {
		eventID = "default"
	}

	proxyMutex.Lock()
	defer proxyMutex.Unlock()

	if proxy, exists := proxyForwards[eventID]; exists {
		stopProxyForward(proxy)
		common.SendPack(modules.Packet{
			Code: 0,
			Data: map[string]any{
				"status": "Port forwarding stopped",
			},
			Event: pack.Event,
		}, session)
	} else {
		common.SendPack(modules.Packet{
			Code: 1,
			Msg:  "Port forwarding not found",
			Event: pack.Event,
		}, session)
	}
}

// 清理客户端的所有端口转发
func CleanupClientForwards(session *melody.Session) {
	proxyMutex.Lock()
	defer proxyMutex.Unlock()

	for eventID, proxy := range proxyForwards {
		if proxy.Session == session {
			stopProxyForward(proxy)
			delete(proxyForwards, eventID)
		}
	}
}

// 处理从客户端发来的代理数据
func HandleProxyData(pack modules.Packet, session *melody.Session) {
	// 只需转发数据包，不做其他处理
	// 在ProxyForward.handleClientConnection中注册的事件处理器会处理这些数据
}

// 处理客户端上报的隧道状态
func HandleSocks5TunnelStatus(pack modules.Packet, session *melody.Session) {
	tunnelID, ok := pack.Data["tunnel_id"].(int)
	if !ok {
		golog.Error("Missing tunnel ID in status update")
		return
	}
	
	status, ok := pack.Data["status"].(string)
	if !ok {
		golog.Error("Missing status in tunnel update")
		return
	}
	
	errorMsg, _ := pack.Data["error"].(string)
	
	// 更新隧道状态
	err := common.UpdateSocks5TunnelStatus(tunnelID, status, errorMsg)
	if err != nil {
		golog.Errorf("更新隧道状态失败: %v", err)
	}
	
	// 回复客户端
	common.SendPack(modules.Packet{
		Code: 0,
		Msg:  "Status updated",
		Event: pack.Event,
	}, session)
} 