package service

import (
	"fmt"
	"log"
	"math/rand"
	"net"
	"strconv"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
	"Spark/server/common"
	"Spark/server/model"
)

type TunnelService struct {
	db *gorm.DB
}

func NewTunnelService(db *gorm.DB) *TunnelService {
	return &TunnelService{db: db}
}

// CreateTunnel 创建隧道
func (s *TunnelService) CreateTunnel(deviceUUID string, config *model.TunnelConfig) (*model.Tunnel, error) {
	// 检查设备是否存在（支持WebSocket会话UUID和设备硬件ID）
	var device model.Device
	err := s.db.Where("uuid = ?", deviceUUID).First(&device).Error
	if err != nil {
		log.Printf("直接查找设备失败，尝试从内存转换: %s", deviceUUID)
		// 如果直接查找失败，可能传入的是WebSocket会话UUID，需要转换为设备硬件ID
		// 从内存中查找对应的设备硬件ID
		var foundDeviceID string
		if deviceInfo, exists := common.Devices.Get(deviceUUID); exists {
			foundDeviceID = deviceInfo.ID
			log.Printf("从内存找到设备硬件ID: %s -> %s", deviceUUID, foundDeviceID)
			// 用设备硬件ID再次查找
			err = s.db.Where("uuid = ?", foundDeviceID).First(&device).Error
		}

		if err != nil {
			return nil, fmt.Errorf("设备不存在或离线: %v", err)
		}

		// 使用设备硬件ID作为隧道的DeviceUUID
		deviceUUID = foundDeviceID
		log.Printf("隧道将使用设备硬件ID: %s", deviceUUID)
	} else {
		log.Printf("直接找到设备: %s", deviceUUID)
	}

	// 处理服务端端口
	var serverPort int

	requestedPort := config.GetServerPort()
	if requestedPort > 0 {
		// 用户指定了端口，检查是否可用
		if !s.isPortAvailable(requestedPort) {
			return nil, fmt.Errorf("端口 %d 已被占用", requestedPort)
		}

		// 检查数据库中是否已有隧道使用此端口
		var count int64
		if err := s.db.Model(&model.Tunnel{}).Where("server_port = ?", requestedPort).Count(&count).Error; err != nil {
			return nil, fmt.Errorf("检查端口失败: %v", err)
		}
		if count > 0 {
			return nil, fmt.Errorf("端口 %d 已被其他隧道使用", requestedPort)
		}

		serverPort = requestedPort
	} else {
		// 自动分配端口
		var portErr error
		serverPort, portErr = s.allocatePort()
		if portErr != nil {
			return nil, fmt.Errorf("分配端口失败: %v", portErr)
		}
	}

	// 创建隧道
	tunnel := &model.Tunnel{
		DeviceUUID: deviceUUID,
		TunnelID:   uuid.New().String(),
		TunnelName: config.TunnelName,
		TunnelType: config.TunnelType,
		ServerPort: serverPort,
		Username:   config.Username,
		Password:   config.Password,
		Enabled:    config.Enabled,
		Status:     "inactive",
		CreatedAt:  time.Now(),
		UpdatedAt:  time.Now(),
	}

	if err := s.db.Create(tunnel).Error; err != nil {
		return nil, fmt.Errorf("创建隧道失败: %v", err)
	}

	// 如果设备在线且隧道启用，立即启动隧道
	if device.IsOnline() && tunnel.Enabled {
		go s.startTunnel(tunnel)
	}

	return tunnel, nil
}

// GetTunnels 获取隧道列表
func (s *TunnelService) GetTunnels(page, pageSize int) ([]model.TunnelResponse, int64, error) {
	var tunnels []model.Tunnel
	var total int64

	// 计算偏移量
	offset := (page - 1) * pageSize

	// 获取总数
	if err := s.db.Model(&model.Tunnel{}).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取隧道列表（包含设备信息）
	if err := s.db.Preload("Device").
		Offset(offset).
		Limit(pageSize).
		Order("created_at DESC").
		Find(&tunnels).Error; err != nil {
		return nil, 0, err
	}

	// 转换为响应格式
	var responses []model.TunnelResponse
	for _, tunnel := range tunnels {
		response := model.TunnelResponse{
			ID:             tunnel.ID,
			DeviceUUID:     tunnel.DeviceUUID,
			TunnelID:       tunnel.TunnelID,
			TunnelName:     tunnel.TunnelName,
			TunnelType:     tunnel.TunnelType,
			ServerPort:     tunnel.ServerPort,
			Username:       tunnel.Username,
			Password:       tunnel.Password,
			Enabled:        tunnel.Enabled,
			Status:         tunnel.Status,
			ErrorMessage:   tunnel.ErrorMessage,
			CreatedAt:      tunnel.CreatedAt,
			UpdatedAt:      tunnel.UpdatedAt,
			DeviceHostname: tunnel.Device.Hostname,
			DeviceStatus:   tunnel.Device.Status,
			ProxyURL:       tunnel.GetProxyURL("localhost"), // 这里应该使用实际的服务器地址
		}
		responses = append(responses, response)
	}

	return responses, total, nil
}

// GetTunnel 获取隧道详情
func (s *TunnelService) GetTunnel(tunnelID string) (*model.TunnelResponse, error) {
	var tunnel model.Tunnel
	if err := s.db.Preload("Device").Where("tunnel_id = ?", tunnelID).First(&tunnel).Error; err != nil {
		return nil, fmt.Errorf("隧道不存在: %v", err)
	}

	response := &model.TunnelResponse{
		ID:             tunnel.ID,
		DeviceUUID:     tunnel.DeviceUUID,
		TunnelID:       tunnel.TunnelID,
		TunnelName:     tunnel.TunnelName,
		TunnelType:     tunnel.TunnelType,
		ServerPort:     tunnel.ServerPort,
		Username:       tunnel.Username,
		Password:       tunnel.Password,
		Enabled:        tunnel.Enabled,
		Status:         tunnel.Status,
		ErrorMessage:   tunnel.ErrorMessage,
		CreatedAt:      tunnel.CreatedAt,
		UpdatedAt:      tunnel.UpdatedAt,
		DeviceHostname: tunnel.Device.Hostname,
		DeviceStatus:   tunnel.Device.Status,
		ProxyURL:       tunnel.GetProxyURL("localhost"), // 这里应该使用实际的服务器地址
	}

	return response, nil
}

// UpdateTunnel 更新隧道
func (s *TunnelService) UpdateTunnel(tunnelID string, config *model.TunnelConfig) error {
	var tunnel model.Tunnel
	if err := s.db.Where("tunnel_id = ?", tunnelID).First(&tunnel).Error; err != nil {
		return fmt.Errorf("隧道不存在: %v", err)
	}

	// 更新隧道配置
	tunnel.TunnelName = config.TunnelName
	tunnel.TunnelType = config.TunnelType
	tunnel.Username = config.Username
	tunnel.Password = config.Password
	tunnel.Enabled = config.Enabled
	tunnel.UpdatedAt = time.Now()

	if err := s.db.Save(&tunnel).Error; err != nil {
		return fmt.Errorf("更新隧道失败: %v", err)
	}

	// 根据启用状态启动或停止隧道
	var device model.Device
	if err := s.db.Where("uuid = ?", tunnel.DeviceUUID).First(&device).Error; err == nil {
		if device.IsOnline() {
			if tunnel.Enabled {
				go s.startTunnel(&tunnel)
			} else {
				go s.stopTunnel(&tunnel)
			}
		}
	}

	return nil
}

// DeleteTunnel 删除隧道
func (s *TunnelService) DeleteTunnel(tunnelID string) error {
	log.Printf("开始删除隧道，tunnel_id: %s", tunnelID)

	var tunnel model.Tunnel
	if err := s.db.Where("tunnel_id = ?", tunnelID).First(&tunnel).Error; err != nil {
		log.Printf("查找隧道失败: %v", err)
		return fmt.Errorf("隧道不存在: %v", err)
	}

	log.Printf("找到隧道记录，ID: %d, TunnelID: %s, Name: %s", tunnel.ID, tunnel.TunnelID, tunnel.TunnelName)

	// 先停止隧道（同步执行，确保停止完成）
	s.stopTunnel(&tunnel)

	// 删除隧道（硬删除）
	result := s.db.Unscoped().Delete(&tunnel)
	if result.Error != nil {
		log.Printf("删除隧道失败: %v", result.Error)
		return fmt.Errorf("删除隧道失败: %v", result.Error)
	}

	log.Printf("隧道删除操作完成，影响行数: %d", result.RowsAffected)

	if result.RowsAffected == 0 {
		return fmt.Errorf("删除隧道失败: 没有记录被删除")
	}

	// 验证删除是否成功
	var checkTunnel model.Tunnel
	err := s.db.Unscoped().Where("tunnel_id = ?", tunnelID).First(&checkTunnel).Error
	if err == nil {
		log.Printf("警告：隧道记录仍然存在于数据库中！ID: %d", checkTunnel.ID)
		return fmt.Errorf("删除验证失败: 隧道记录仍然存在")
	} else {
		log.Printf("删除验证成功：隧道记录已从数据库中移除")
	}

	return nil
}

// ToggleTunnel 切换隧道状态
func (s *TunnelService) ToggleTunnel(tunnelID string) error {
	var tunnel model.Tunnel
	if err := s.db.Where("tunnel_id = ?", tunnelID).First(&tunnel).Error; err != nil {
		return fmt.Errorf("隧道不存在: %v", err)
	}

	// 切换启用状态
	tunnel.Enabled = !tunnel.Enabled
	tunnel.UpdatedAt = time.Now()

	if err := s.db.Save(&tunnel).Error; err != nil {
		return fmt.Errorf("更新隧道状态失败: %v", err)
	}

	// 根据新状态启动或停止隧道
	var device model.Device
	if err := s.db.Where("uuid = ?", tunnel.DeviceUUID).First(&device).Error; err == nil {
		if device.IsOnline() {
			if tunnel.Enabled {
				go s.startTunnel(&tunnel)
			} else {
				go s.stopTunnel(&tunnel)
			}
		}
	}

	return nil
}

// allocatePort 分配可用端口
func (s *TunnelService) allocatePort() (int, error) {
	// 端口范围：10000-65535
	minPort := 10000
	maxPort := 65535
	maxAttempts := 100

	for i := 0; i < maxAttempts; i++ {
		port := rand.Intn(maxPort-minPort+1) + minPort

		// 检查端口是否已被使用
		var count int64
		if err := s.db.Model(&model.Tunnel{}).Where("server_port = ?", port).Count(&count).Error; err != nil {
			continue
		}

		if count == 0 {
			// 检查端口是否可用
			if s.isPortAvailable(port) {
				return port, nil
			}
		}
	}

	return 0, fmt.Errorf("无法分配可用端口")
}

// isPortAvailable 检查端口是否可用
func (s *TunnelService) isPortAvailable(port int) bool {
	ln, err := net.Listen("tcp", ":"+strconv.Itoa(port))
	if err != nil {
		return false
	}
	ln.Close()
	return true
}

// startTunnel 启动隧道
func (s *TunnelService) startTunnel(tunnel *model.Tunnel) {
	log.Printf("启动隧道: %s (类型: %s, 端口: %d)", tunnel.TunnelName, tunnel.TunnelType, tunnel.ServerPort)

	var err error

	// 根据隧道类型启动相应的服务
	switch tunnel.TunnelType {
	case "socks5":
		err = StartSOCKS5Tunnel(tunnel)
	case "tcp":
		err = s.startTCPTunnel(tunnel)
	case "http":
		err = s.startHTTPTunnel(tunnel)
	default:
		err = fmt.Errorf("不支持的隧道类型: %s", tunnel.TunnelType)
	}

	// 更新隧道状态
	if err != nil {
		tunnel.Status = "error"
		tunnel.ErrorMessage = err.Error()
		log.Printf("启动隧道失败: %v", err)
	} else {
		tunnel.Status = "active"
		tunnel.ErrorMessage = ""
	}

	tunnel.UpdatedAt = time.Now()
	s.db.Save(tunnel)
}

// stopTunnel 停止隧道
func (s *TunnelService) stopTunnel(tunnel *model.Tunnel) {
	log.Printf("停止隧道: %s", tunnel.TunnelName)

	var err error

	// 根据隧道类型停止相应的服务
	switch tunnel.TunnelType {
	case "socks5":
		err = StopSOCKS5Tunnel(tunnel.TunnelID)
	case "tcp":
		err = s.stopTCPTunnel(tunnel.TunnelID)
	case "http":
		err = s.stopHTTPTunnel(tunnel.TunnelID)
	}

	if err != nil {
		log.Printf("停止隧道失败: %v", err)
	}

	// 更新隧道状态为非活跃
	tunnel.Status = "inactive"
	tunnel.UpdatedAt = time.Now()
	s.db.Save(tunnel)
}

// OnDeviceOnline 设备上线时的处理
func (s *TunnelService) OnDeviceOnline(deviceUUID string) {
	var tunnels []model.Tunnel
	if err := s.db.Where("device_uuid = ? AND enabled = ?", deviceUUID, true).Find(&tunnels).Error; err != nil {
		log.Printf("获取设备隧道失败: %v", err)
		return
	}

	// 启动所有启用的隧道
	for _, tunnel := range tunnels {
		go s.startTunnel(&tunnel)
	}
}

// OnDeviceOffline 设备离线时的处理
func (s *TunnelService) OnDeviceOffline(deviceUUID string) {
	var tunnels []model.Tunnel
	if err := s.db.Where("device_uuid = ?", deviceUUID).Find(&tunnels).Error; err != nil {
		log.Printf("获取设备隧道失败: %v", err)
		return
	}

	// 停止所有隧道
	for _, tunnel := range tunnels {
		go s.stopTunnel(&tunnel)
	}
}

// startTCPTunnel 启动TCP隧道 (占位符实现)
func (s *TunnelService) startTCPTunnel(tunnel *model.Tunnel) error {
	log.Printf("TCP隧道启动 (占位符): %s", tunnel.TunnelName)
	// TODO: 实现TCP隧道逻辑
	return nil
}

// stopTCPTunnel 停止TCP隧道 (占位符实现)
func (s *TunnelService) stopTCPTunnel(tunnelID string) error {
	log.Printf("TCP隧道停止 (占位符): %s", tunnelID)
	// TODO: 实现TCP隧道停止逻辑
	return nil
}

// startHTTPTunnel 启动HTTP隧道 (占位符实现)
func (s *TunnelService) startHTTPTunnel(tunnel *model.Tunnel) error {
	log.Printf("HTTP隧道启动 (占位符): %s", tunnel.TunnelName)
	// TODO: 实现HTTP隧道逻辑
	return nil
}

// stopHTTPTunnel 停止HTTP隧道 (占位符实现)
func (s *TunnelService) stopHTTPTunnel(tunnelID string) error {
	log.Printf("HTTP隧道停止 (占位符): %s", tunnelID)
	// TODO: 实现HTTP隧道停止逻辑
	return nil
}


