#!/bin/bash

# Spark 隧道功能测试脚本

echo "=== Spark 隧道功能测试 ==="

# 检查服务是否运行
check_service() {
    echo "检查 Spark 服务状态..."
    if curl -s http://localhost:8000/api/device/list > /dev/null; then
        echo "✓ Spark 服务运行正常"
        return 0
    else
        echo "✗ Spark 服务未运行，请先启动服务"
        return 1
    fi
}

# 测试隧道 API
test_tunnel_api() {
    echo "测试隧道管理 API..."
    
    # 获取隧道列表
    echo "1. 获取隧道列表"
    response=$(curl -s -w "%{http_code}" http://localhost:8000/api/tunnels)
    http_code="${response: -3}"
    if [ "$http_code" = "200" ]; then
        echo "✓ 隧道列表 API 正常"
    else
        echo "✗ 隧道列表 API 失败 (HTTP $http_code)"
    fi
    
    # 获取可用端口
    echo "2. 获取可用端口"
    response=$(curl -s -w "%{http_code}" "http://localhost:8000/api/ports/available?type=tcp&count=5")
    http_code="${response: -3}"
    if [ "$http_code" = "200" ]; then
        echo "✓ 可用端口 API 正常"
    else
        echo "✗ 可用端口 API 失败 (HTTP $http_code)"
    fi
    
    # 获取端口统计
    echo "3. 获取端口统计"
    response=$(curl -s -w "%{http_code}" http://localhost:8000/api/ports/stats)
    http_code="${response: -3}"
    if [ "$http_code" = "200" ]; then
        echo "✓ 端口统计 API 正常"
    else
        echo "✗ 端口统计 API 失败 (HTTP $http_code)"
    fi
}

# 测试数据库
test_database() {
    echo "测试数据库表结构..."
    
    # 检查数据库文件是否存在
    if [ -f "spark.db" ]; then
        echo "✓ 数据库文件存在"
        
        # 检查隧道相关表是否存在
        tables=$(sqlite3 spark.db ".tables" | grep -E "(tunnels|tunnel_sessions|port_allocations)")
        if [ -n "$tables" ]; then
            echo "✓ 隧道相关表已创建"
            echo "  发现表: $tables"
        else
            echo "✗ 隧道相关表未创建"
        fi
    else
        echo "✗ 数据库文件不存在"
    fi
}

# 测试前端页面
test_frontend() {
    echo "测试前端页面..."
    
    # 检查隧道管理页面
    response=$(curl -s -w "%{http_code}" http://localhost:8000/tunnels)
    http_code="${response: -3}"
    if [ "$http_code" = "200" ]; then
        echo "✓ 隧道管理页面可访问"
    else
        echo "✗ 隧道管理页面无法访问 (HTTP $http_code)"
    fi
}

# 创建测试隧道
test_create_tunnel() {
    echo "测试创建隧道..."
    
    # 首先获取设备列表
    devices=$(curl -s http://localhost:8000/api/device/list)
    if echo "$devices" | grep -q '"code":0'; then
        echo "✓ 获取设备列表成功"
        
        # 提取第一个设备ID (如果有的话)
        device_id=$(echo "$devices" | grep -o '"id":"[^"]*"' | head -1 | cut -d'"' -f4)
        
        if [ -n "$device_id" ]; then
            echo "  使用设备ID: $device_id"
            
            # 创建测试隧道
            tunnel_data='{
                "device_id": "'$device_id'",
                "name": "测试隧道",
                "description": "自动化测试创建的隧道",
                "type": "tcp",
                "protocol": "tcp",
                "local_host": "localhost",
                "local_port": 22,
                "auto_start": false
            }'
            
            response=$(curl -s -X POST \
                -H "Content-Type: application/json" \
                -d "$tunnel_data" \
                -w "%{http_code}" \
                http://localhost:8000/api/tunnels)
            
            http_code="${response: -3}"
            if [ "$http_code" = "200" ]; then
                echo "✓ 隧道创建成功"
                
                # 提取隧道ID
                tunnel_id=$(echo "${response%???}" | grep -o '"id":"[^"]*"' | cut -d'"' -f4)
                if [ -n "$tunnel_id" ]; then
                    echo "  隧道ID: $tunnel_id"
                    
                    # 测试删除隧道
                    delete_response=$(curl -s -X DELETE -w "%{http_code}" http://localhost:8000/api/tunnels/$tunnel_id)
                    delete_code="${delete_response: -3}"
                    if [ "$delete_code" = "200" ]; then
                        echo "✓ 隧道删除成功"
                    else
                        echo "✗ 隧道删除失败 (HTTP $delete_code)"
                    fi
                fi
            else
                echo "✗ 隧道创建失败 (HTTP $http_code)"
                echo "  响应: ${response%???}"
            fi
        else
            echo "⚠ 没有可用设备，跳过隧道创建测试"
        fi
    else
        echo "✗ 获取设备列表失败"
    fi
}

# 主测试流程
main() {
    echo "开始测试..."
    echo
    
    # 检查服务状态
    if ! check_service; then
        exit 1
    fi
    echo
    
    # 测试数据库
    test_database
    echo
    
    # 测试 API
    test_tunnel_api
    echo
    
    # 测试前端
    test_frontend
    echo
    
    # 测试创建隧道
    test_create_tunnel
    echo
    
    echo "=== 测试完成 ==="
}

# 运行测试
main
