package process

import (
	"fmt"
	"runtime"
)

// ===== 通用类型定义 =====

// 沙箱检测器
type SandboxDetector struct {
	platform string
}

// 反调试引擎
type AntiDebugEngine struct {
	platform string
}

// 注入引擎
type InjectionEngine struct {
	antiDebug    bool
	sandboxCheck bool
	memEncrypt   bool
	encryptKey   []byte
}

// 高级注入引擎
type AdvancedInjectionEngine struct {
	*InjectionEngine
	directSyscall bool
	etwDisabled   bool
	stackSpoofing bool
}

// ===== 构造函数 =====

func NewSandboxDetector() *SandboxDetector {
	return &SandboxDetector{platform: runtime.GOOS}
}

func NewAntiDebugEngine() *AntiDebugEngine {
	return &AntiDebugEngine{platform: runtime.GOOS}
}

func NewInjectionEngine() *InjectionEngine {
	return &InjectionEngine{
		antiDebug:    true,
		sandboxCheck: true,
		memEncrypt:   true,
		encryptKey:   nil,
	}
}

func NewInjectionEngineWithKey(customKey []byte) *InjectionEngine {
	return &InjectionEngine{
		antiDebug:    true,
		sandboxCheck: true,
		memEncrypt:   true,
		encryptKey:   customKey,
	}
}

func NewAdvancedInjectionEngine() *AdvancedInjectionEngine {
	return &AdvancedInjectionEngine{
		InjectionEngine: NewInjectionEngine(),
		directSyscall:   runtime.GOOS == "windows",
		etwDisabled:     runtime.GOOS == "windows",
		stackSpoofing:   runtime.GOOS == "windows",
	}
}

func NewAdvancedInjectionEngineWithKey(encryptKey []byte) *AdvancedInjectionEngine {
	return &AdvancedInjectionEngine{
		InjectionEngine: NewInjectionEngineWithKey(encryptKey),
		directSyscall:   runtime.GOOS == "windows",
		etwDisabled:     runtime.GOOS == "windows",
		stackSpoofing:   runtime.GOOS == "windows",
	}
}

// ===== 通用接口实现 =====

// 注意：DetectSandbox, DetectDebugger, CheckIsDebuggerPresentPublic 等方法
// 在平台特定文件中实现（如 detection_windows.go）

func (ie *InjectionEngine) InjectAdvanced(pid int32, shellcode []byte, method string) error {
	// 使用跨平台注入
	selectedMethod, err := SelectOptimalMethod(method)
	if err != nil {
		return fmt.Errorf("方法选择失败: %v", err)
	}
	
	return ExecuteCrossPlatformInjection(pid, shellcode, selectedMethod)
}

// 注意：InjectUltimate 和 testAPIAvailability 方法
// 在平台特定文件中实现（如 detection_windows.go）

// ===== 跨平台注入函数实现 =====

// APC注入 - 跨平台实现
func injectViaAPC(pid int32, shellcode []byte) error {
	if runtime.GOOS == "windows" {
		// Windows APC注入实现
		return injectWindowsAPC(pid, shellcode)
	} else {
		// Linux映射到信号注入
		return injectLinuxSignal(pid, shellcode)
	}
}

// DLL注入 - 跨平台实现
func injectViaDLL(pid int32, shellcode []byte) error {
	if runtime.GOOS == "windows" {
		// Windows DLL注入实现
		return injectWindowsDLL(pid, shellcode)
	} else {
		// Linux映射到共享库注入
		return injectLinuxSharedLib(pid, shellcode)
	}
}

// 直接注入 - 跨平台实现
func injectDirect(pid int32, shellcode []byte) error {
	if runtime.GOOS == "windows" {
		// Windows直接注入实现
		return injectWindowsDirect(pid, shellcode)
	} else {
		// Linux映射到ptrace注入
		return injectLinuxPtrace(pid, shellcode)
	}
}

// Linux特定注入函数
func injectViaPtrace(pid int32, shellcode []byte) error {
	if runtime.GOOS != "linux" {
		return fmt.Errorf("Ptrace注入仅支持Linux系统")
	}
	return injectLinuxPtrace(pid, shellcode)
}

func injectViaMemFile(pid int32, shellcode []byte) error {
	if runtime.GOOS != "linux" {
		return fmt.Errorf("内存文件注入仅支持Linux系统")
	}
	return injectLinuxMemFile(pid, shellcode)
}

func injectViaLdPreload(pid int32, shellcode []byte) error {
	if runtime.GOOS != "linux" {
		return fmt.Errorf("LD_PRELOAD注入仅支持Linux系统")
	}
	return injectLinuxSharedLib(pid, shellcode)
}

func injectViaSignal(pid int32, shellcode []byte) error {
	if runtime.GOOS != "linux" {
		return fmt.Errorf("信号注入仅支持Linux系统")
	}
	return injectLinuxSignal(pid, shellcode)
}

// 注意：checkPtracePermission 函数在平台特定文件中实现

// ===== 平台特定函数声明 =====
// 注意：以下函数在平台特定文件中实现，这里不提供默认实现以避免重复声明

// ===== Linux平台实现函数 =====

func injectLinuxPtrace(pid int32, shellcode []byte) error {
	if runtime.GOOS != "linux" {
		return fmt.Errorf("Ptrace注入仅支持Linux系统")
	}
	// 模拟实现 - 返回成功（实际项目中需要真实的ptrace系统调用）
	return nil
}

func injectLinuxMemFile(pid int32, shellcode []byte) error {
	if runtime.GOOS != "linux" {
		return fmt.Errorf("内存文件注入仅支持Linux系统")
	}
	// 模拟实现 - 返回成功（实际项目中需要真实的/proc/pid/mem操作）
	return nil
}

func injectLinuxSharedLib(pid int32, shellcode []byte) error {
	if runtime.GOOS != "linux" {
		return fmt.Errorf("共享库注入仅支持Linux系统")
	}
	// 模拟实现 - 返回成功（实际项目中需要真实的LD_PRELOAD机制）
	return nil
}

func injectLinuxSignal(pid int32, shellcode []byte) error {
	if runtime.GOOS != "linux" {
		return fmt.Errorf("信号注入仅支持Linux系统")
	}
	// 模拟实现 - 返回成功（实际项目中需要真实的信号处理机制）
	return nil
}

// 栈欺骗函数
func InjectWithStackSpoofing(pid int32, shellcode []byte, method string) error {
	if runtime.GOOS == "windows" {
		return fmt.Errorf("栈欺骗注入需要在Windows平台文件中实现")
	}
	return fmt.Errorf("Linux系统暂不支持栈欺骗注入")
}
