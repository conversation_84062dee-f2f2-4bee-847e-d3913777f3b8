package common

import (
	"database/sql"
	"fmt"
	"time"
)

// Tunnel 隧道结构
type Tunnel struct {
	ID              int    `json:"id"`
	TunnelID        string `json:"tunnel_id"`
	DeviceID        string `json:"device_id"`
	GroupID         string `json:"group_id"`
	Name            string `json:"name"`
	Description     string `json:"description"`
	TunnelType      string `json:"tunnel_type"`      // tcp/http/socks5
	Protocol        string `json:"protocol"`         // tcp/udp
	RemotePort      int    `json:"remote_port"`      // 服务端监听端口
	LocalHost       string `json:"local_host"`       // 本地目标主机
	LocalPort       int    `json:"local_port"`       // 本地目标端口
	Subdomain       string `json:"subdomain"`        // 子域名(HTTP隧道)
	CustomDomain    string `json:"custom_domain"`    // 自定义域名
	AuthRequired    bool   `json:"auth_required"`    // 是否需要认证
	AuthUsername    string `json:"auth_username"`    // 认证用户名
	AuthPassword    string `json:"auth_password"`    // 认证密码
	Username        string `json:"username"`         // SOCKS5用户名
	Password        string `json:"password"`         // SOCKS5密码
	AllowedIPs      string `json:"allowed_ips"`      // 允许的IP列表(JSON)
	Status          string `json:"status"`           // stopped/starting/running/error
	AutoStart       bool   `json:"auto_start"`       // 是否自动启动
	TotalConnections int   `json:"total_connections"` // 总连接数
	TotalBytesIn    int64  `json:"total_bytes_in"`   // 总入流量
	TotalBytesOut   int64  `json:"total_bytes_out"`  // 总出流量
	LastConnectionTime int64 `json:"last_connection_time"` // 最后连接时间
	CreateTime      int64  `json:"create_time"`      // 创建时间
	UpdateTime      int64  `json:"update_time"`      // 更新时间
}

// TunnelSession 隧道会话结构
type TunnelSession struct {
	ID           int    `json:"id"`
	SessionID    string `json:"session_id"`
	TunnelID     string `json:"tunnel_id"`
	ClientIP     string `json:"client_ip"`
	ClientPort   int    `json:"client_port"`
	UserAgent    string `json:"user_agent"`
	BytesIn      int64  `json:"bytes_in"`
	BytesOut     int64  `json:"bytes_out"`
	StartTime    int64  `json:"start_time"`
	LastActivity int64  `json:"last_activity"`
}

// PortAllocation 端口分配结构
type PortAllocation struct {
	ID            int    `json:"id"`
	Port          int    `json:"port"`
	TunnelID      string `json:"tunnel_id"`
	AllocationType string `json:"allocation_type"` // tcp/http/socks5
	Status        string `json:"status"`          // allocated/in_use/released
	AllocatedTime int64  `json:"allocated_time"`
	ReleasedTime  int64  `json:"released_time"`
}

// InitTunnelTables 初始化隧道相关数据库表
func InitTunnelTables() error {
	if DB == nil {
		return fmt.Errorf("数据库未初始化")
	}

	// 创建隧道配置表
	tunnelTableSQL := `
	CREATE TABLE IF NOT EXISTS tunnels (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		tunnel_id VARCHAR(64) UNIQUE NOT NULL,
		device_id VARCHAR(64) NOT NULL,
		group_id VARCHAR(32) DEFAULT 'default',
		name VARCHAR(128) NOT NULL,
		description TEXT,
		tunnel_type VARCHAR(16) NOT NULL,
		protocol VARCHAR(8) DEFAULT 'tcp',
		remote_port INTEGER,
		local_host VARCHAR(128) DEFAULT 'localhost',
		local_port INTEGER,
		subdomain VARCHAR(64),
		custom_domain VARCHAR(128),
		auth_required BOOLEAN DEFAULT FALSE,
		auth_username VARCHAR(64),
		auth_password VARCHAR(128),
		username VARCHAR(64),
		password VARCHAR(128),
		allowed_ips TEXT,
		status VARCHAR(16) DEFAULT 'stopped',
		auto_start BOOLEAN DEFAULT TRUE,
		total_connections INTEGER DEFAULT 0,
		total_bytes_in BIGINT DEFAULT 0,
		total_bytes_out BIGINT DEFAULT 0,
		last_connection_time INTEGER,
		create_time INTEGER NOT NULL,
		update_time INTEGER NOT NULL
	);`

	if _, err := DB.Exec(tunnelTableSQL); err != nil {
		return fmt.Errorf("failed to create tunnels table: %v", err)
	}

	// 创建隧道会话表
	sessionTableSQL := `
	CREATE TABLE IF NOT EXISTS tunnel_sessions (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		session_id VARCHAR(64) UNIQUE NOT NULL,
		tunnel_id VARCHAR(64) NOT NULL,
		client_ip VARCHAR(45) NOT NULL,
		client_port INTEGER,
		user_agent TEXT,
		bytes_in BIGINT DEFAULT 0,
		bytes_out BIGINT DEFAULT 0,
		start_time INTEGER NOT NULL,
		last_activity INTEGER NOT NULL,
		FOREIGN KEY (tunnel_id) REFERENCES tunnels(tunnel_id)
	);`

	if _, err := DB.Exec(sessionTableSQL); err != nil {
		return fmt.Errorf("failed to create tunnel_sessions table: %v", err)
	}

	// 创建端口分配表
	portTableSQL := `
	CREATE TABLE IF NOT EXISTS port_allocations (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		port INTEGER UNIQUE NOT NULL,
		tunnel_id VARCHAR(64),
		allocation_type VARCHAR(16) NOT NULL,
		status VARCHAR(16) DEFAULT 'allocated',
		allocated_time INTEGER NOT NULL,
		released_time INTEGER,
		FOREIGN KEY (tunnel_id) REFERENCES tunnels(tunnel_id)
	);`

	if _, err := DB.Exec(portTableSQL); err != nil {
		return fmt.Errorf("failed to create port_allocations table: %v", err)
	}

	// 创建索引
	indexes := []string{
		"CREATE INDEX IF NOT EXISTS idx_tunnels_device_id ON tunnels(device_id);",
		"CREATE INDEX IF NOT EXISTS idx_tunnels_group_id ON tunnels(group_id);",
		"CREATE INDEX IF NOT EXISTS idx_tunnels_remote_port ON tunnels(remote_port);",
		"CREATE INDEX IF NOT EXISTS idx_tunnels_status ON tunnels(status);",
		"CREATE INDEX IF NOT EXISTS idx_tunnel_sessions_tunnel_id ON tunnel_sessions(tunnel_id);",
		"CREATE INDEX IF NOT EXISTS idx_tunnel_sessions_start_time ON tunnel_sessions(start_time);",
		"CREATE INDEX IF NOT EXISTS idx_port_allocations_port ON port_allocations(port);",
		"CREATE INDEX IF NOT EXISTS idx_port_allocations_status ON port_allocations(status);",
	}

	for _, indexSQL := range indexes {
		if _, err := DB.Exec(indexSQL); err != nil {
			return fmt.Errorf("failed to create index: %v", err)
		}
	}

	return nil
}

// CreateTunnel 创建隧道
func CreateTunnel(tunnel *Tunnel) error {
	if DB == nil {
		return fmt.Errorf("数据库未初始化")
	}

	now := time.Now().Unix()
	tunnel.CreateTime = now
	tunnel.UpdateTime = now

	query := `
	INSERT INTO tunnels (
		tunnel_id, device_id, group_id, name, description, tunnel_type, protocol,
		remote_port, local_host, local_port, subdomain, custom_domain,
		auth_required, auth_username, auth_password, username, password, allowed_ips,
		status, auto_start, create_time, update_time
	) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`

	result, err := DB.Exec(query,
		tunnel.TunnelID, tunnel.DeviceID, tunnel.GroupID, tunnel.Name, tunnel.Description,
		tunnel.TunnelType, tunnel.Protocol, tunnel.RemotePort, tunnel.LocalHost, tunnel.LocalPort,
		tunnel.Subdomain, tunnel.CustomDomain, tunnel.AuthRequired, tunnel.AuthUsername,
		tunnel.AuthPassword, tunnel.Username, tunnel.Password, tunnel.AllowedIPs, tunnel.Status, tunnel.AutoStart,
		tunnel.CreateTime, tunnel.UpdateTime)

	if err != nil {
		return err
	}

	id, err := result.LastInsertId()
	if err != nil {
		return err
	}

	tunnel.ID = int(id)
	return nil
}

// GetTunnelByID 根据隧道ID获取隧道
func GetTunnelByID(tunnelID string) (*Tunnel, error) {
	if DB == nil {
		return nil, fmt.Errorf("数据库未初始化")
	}

	tunnel := &Tunnel{}
	query := `
	SELECT id, tunnel_id, device_id, group_id, name, description, tunnel_type, protocol,
		   remote_port, local_host, local_port, subdomain, custom_domain,
		   auth_required, auth_username, auth_password, username, password, allowed_ips,
		   status, auto_start, total_connections, total_bytes_in, total_bytes_out,
		   last_connection_time, create_time, update_time
	FROM tunnels WHERE tunnel_id = ?`

	err := DB.QueryRow(query, tunnelID).Scan(
		&tunnel.ID, &tunnel.TunnelID, &tunnel.DeviceID, &tunnel.GroupID,
		&tunnel.Name, &tunnel.Description, &tunnel.TunnelType, &tunnel.Protocol,
		&tunnel.RemotePort, &tunnel.LocalHost, &tunnel.LocalPort,
		&tunnel.Subdomain, &tunnel.CustomDomain, &tunnel.AuthRequired,
		&tunnel.AuthUsername, &tunnel.AuthPassword, &tunnel.Username, &tunnel.Password, &tunnel.AllowedIPs,
		&tunnel.Status, &tunnel.AutoStart, &tunnel.TotalConnections,
		&tunnel.TotalBytesIn, &tunnel.TotalBytesOut, &tunnel.LastConnectionTime,
		&tunnel.CreateTime, &tunnel.UpdateTime)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, err
	}

	return tunnel, nil
}

// GetTunnelsByDevice 获取设备的所有隧道
func GetTunnelsByDevice(deviceID string) ([]*Tunnel, error) {
	if DB == nil {
		return nil, fmt.Errorf("数据库未初始化")
	}

	query := `
	SELECT id, tunnel_id, device_id, group_id, name, description, tunnel_type, protocol,
		   remote_port, local_host, local_port, subdomain, custom_domain,
		   auth_required, auth_username, auth_password, username, password, allowed_ips,
		   status, auto_start, total_connections, total_bytes_in, total_bytes_out,
		   last_connection_time, create_time, update_time
	FROM tunnels WHERE device_id = ? ORDER BY create_time DESC`

	rows, err := DB.Query(query, deviceID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var tunnels []*Tunnel
	for rows.Next() {
		tunnel := &Tunnel{}
		err := rows.Scan(
			&tunnel.ID, &tunnel.TunnelID, &tunnel.DeviceID, &tunnel.GroupID,
			&tunnel.Name, &tunnel.Description, &tunnel.TunnelType, &tunnel.Protocol,
			&tunnel.RemotePort, &tunnel.LocalHost, &tunnel.LocalPort,
			&tunnel.Subdomain, &tunnel.CustomDomain, &tunnel.AuthRequired,
			&tunnel.AuthUsername, &tunnel.AuthPassword, &tunnel.Username, &tunnel.Password, &tunnel.AllowedIPs,
			&tunnel.Status, &tunnel.AutoStart, &tunnel.TotalConnections,
			&tunnel.TotalBytesIn, &tunnel.TotalBytesOut, &tunnel.LastConnectionTime,
			&tunnel.CreateTime, &tunnel.UpdateTime)
		if err != nil {
			return nil, err
		}
		tunnels = append(tunnels, tunnel)
	}

	return tunnels, nil
}

// UpdateTunnelStatus 更新隧道状态
func UpdateTunnelStatus(tunnelID, status string) error {
	if DB == nil {
		return fmt.Errorf("数据库未初始化")
	}

	query := "UPDATE tunnels SET status = ?, update_time = ? WHERE tunnel_id = ?"
	_, err := DB.Exec(query, status, time.Now().Unix(), tunnelID)
	return err
}

// GetAllTunnels 获取所有隧道
func GetAllTunnels() ([]*Tunnel, error) {
	if DB == nil {
		return nil, fmt.Errorf("数据库未初始化")
	}

	query := `
	SELECT id, tunnel_id, device_id, group_id, name, description, tunnel_type, protocol,
		   remote_port, local_host, local_port, subdomain, custom_domain,
		   auth_required, auth_username, auth_password, username, password, allowed_ips,
		   status, auto_start, total_connections, total_bytes_in, total_bytes_out,
		   last_connection_time, create_time, update_time
	FROM tunnels ORDER BY create_time DESC`

	rows, err := DB.Query(query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var tunnels []*Tunnel
	for rows.Next() {
		tunnel := &Tunnel{}
		err := rows.Scan(
			&tunnel.ID, &tunnel.TunnelID, &tunnel.DeviceID, &tunnel.GroupID,
			&tunnel.Name, &tunnel.Description, &tunnel.TunnelType, &tunnel.Protocol,
			&tunnel.RemotePort, &tunnel.LocalHost, &tunnel.LocalPort,
			&tunnel.Subdomain, &tunnel.CustomDomain, &tunnel.AuthRequired,
			&tunnel.AuthUsername, &tunnel.AuthPassword, &tunnel.Username, &tunnel.Password, &tunnel.AllowedIPs,
			&tunnel.Status, &tunnel.AutoStart, &tunnel.TotalConnections,
			&tunnel.TotalBytesIn, &tunnel.TotalBytesOut, &tunnel.LastConnectionTime,
			&tunnel.CreateTime, &tunnel.UpdateTime)
		if err != nil {
			return nil, err
		}
		tunnels = append(tunnels, tunnel)
	}

	return tunnels, nil
}

// UpdateTunnelStats 更新隧道统计信息
func UpdateTunnelStats(tunnelID string, totalConnections int, totalBytesIn, totalBytesOut int64, lastConnectionTime int64) error {
	if DB == nil {
		return fmt.Errorf("数据库未初始化")
	}

	query := `UPDATE tunnels SET
		total_connections = ?,
		total_bytes_in = ?,
		total_bytes_out = ?,
		last_connection_time = ?,
		update_time = ?
		WHERE tunnel_id = ?`

	_, err := DB.Exec(query, totalConnections, totalBytesIn, totalBytesOut,
		lastConnectionTime, time.Now().Unix(), tunnelID)
	return err
}

// DeleteTunnel 删除隧道
func DeleteTunnel(tunnelID string) error {
	if DB == nil {
		return fmt.Errorf("数据库未初始化")
	}

	// 删除相关会话
	if _, err := DB.Exec("DELETE FROM tunnel_sessions WHERE tunnel_id = ?", tunnelID); err != nil {
		return err
	}

	// 删除端口分配
	if _, err := DB.Exec("DELETE FROM port_allocations WHERE tunnel_id = ?", tunnelID); err != nil {
		return err
	}

	// 删除隧道
	if _, err := DB.Exec("DELETE FROM tunnels WHERE tunnel_id = ?", tunnelID); err != nil {
		return err
	}

	return nil
}
