package model

import (
	"time"
)

// Device 设备模型
type Device struct {
	ID          int       `json:"id" gorm:"primaryKey"`
	UUID        string    `json:"uuid" gorm:"uniqueIndex;not null"`
	Hostname    string    `json:"hostname"`
	Username    string    `json:"username"`
	OS          string    `json:"os"`
	Arch        string    `json:"arch"`
	CPUInfo     string    `json:"cpu_info"`
	MemoryTotal int64     `json:"memory_total"`
	IPAddress   string    `json:"ip_address"`
	MACAddress  string    `json:"mac_address"` // MAC地址，用于设备唯一标识
	Status      string    `json:"status" gorm:"default:offline"` // online, offline
	Remark      string    `json:"remark"`                        // 设备备注
	Notes       string    `json:"notes" gorm:"type:text"`        // 设备笔记（Markdown格式）
	LastSeen    time.Time `json:"last_seen"`
	FirstSeen   time.Time `json:"first_seen"` // 首次连接时间
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`

	// 关联隧道
	Tunnels []Tunnel `json:"tunnels" gorm:"foreignKey:DeviceUUID;references:UUID"`
}

// DeviceInfo 设备基本信息（用于注册和更新）
type DeviceInfo struct {
	UUID        string `json:"uuid"`
	Hostname    string `json:"hostname"`
	Username    string `json:"username"`
	OS          string `json:"os"`
	Arch        string `json:"arch"`
	CPUInfo     string `json:"cpu_info"`
	MemoryTotal int64  `json:"memory_total"`
	IPAddress   string `json:"ip_address"`
}

// IsOnline 检查设备是否在线
func (d *Device) IsOnline() bool {
	return d.Status == "online"
}

// GetActiveTunnels 获取设备的活跃隧道
func (d *Device) GetActiveTunnels() []Tunnel {
	var activeTunnels []Tunnel
	for _, tunnel := range d.Tunnels {
		if tunnel.Enabled && tunnel.Status == "active" {
			activeTunnels = append(activeTunnels, tunnel)
		}
	}
	return activeTunnels
}
