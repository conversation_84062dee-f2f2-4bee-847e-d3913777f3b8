#!/bin/bash

# Spark 隧道数据一致性检查脚本

echo "=== Spark 隧道数据一致性检查 ==="

# 配置
SERVER_URL="http://localhost:8000"
API_BASE="$SERVER_URL/api"
DB_FILE="spark.db"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    print_info "检查依赖工具..."
    
    if ! command -v sqlite3 &> /dev/null; then
        print_error "sqlite3 未安装，无法直接查询数据库"
        return 1
    fi
    
    if ! command -v curl &> /dev/null; then
        print_error "curl 未安装，无法调用 API"
        return 1
    fi
    
    if ! command -v jq &> /dev/null; then
        print_warning "jq 未安装，JSON 输出将不会格式化"
    fi
    
    print_success "依赖检查完成"
    return 0
}

# 检查服务状态
check_service() {
    print_info "检查 Spark 服务状态..."
    
    if curl -s "$API_BASE/device/list" > /dev/null; then
        print_success "Spark 服务运行正常"
        return 0
    else
        print_error "Spark 服务未运行"
        return 1
    fi
}

# 检查数据库
check_database() {
    print_info "检查数据库状态..."
    
    if [ ! -f "$DB_FILE" ]; then
        print_error "数据库文件不存在: $DB_FILE"
        return 1
    fi
    
    # 检查表是否存在
    tables=$(sqlite3 "$DB_FILE" ".tables" | grep -E "(tunnels|tunnel_sessions|port_allocations)")
    if [ -z "$tables" ]; then
        print_error "隧道相关表不存在"
        return 1
    fi
    
    print_success "数据库检查通过"
    print_info "发现表: $tables"
    return 0
}

# 获取数据库中的隧道数据
get_db_tunnels() {
    print_info "查询数据库中的隧道..."
    
    db_count=$(sqlite3 "$DB_FILE" "SELECT COUNT(*) FROM tunnels;")
    print_info "数据库隧道总数: $db_count"
    
    if [ "$db_count" -gt 0 ]; then
        print_info "数据库隧道列表:"
        sqlite3 "$DB_FILE" "
        SELECT 
            tunnel_id,
            name,
            tunnel_type,
            status,
            remote_port,
            total_connections,
            datetime(create_time, 'unixepoch', 'localtime') as create_time
        FROM tunnels 
        ORDER BY create_time DESC;" \
        -header -column
    fi
    
    return $db_count
}

# 获取 API 中的隧道数据
get_api_tunnels() {
    print_info "查询 API 中的隧道..."
    
    response=$(curl -s "$API_BASE/tunnels")
    if echo "$response" | grep -q '"code":0'; then
        if command -v jq &> /dev/null; then
            api_count=$(echo "$response" | jq '.data.tunnels | length')
            print_info "API 隧道总数: $api_count"
            
            if [ "$api_count" -gt 0 ]; then
                print_info "API 隧道列表:"
                echo "$response" | jq -r '.data.tunnels[] | "\(.id) \(.name) \(.type) \(.status) \(.remote_port) \(.stats.total_connections)"' | \
                while read id name type status port connections; do
                    printf "%-36s %-20s %-8s %-10s %-6s %-12s\n" "$id" "$name" "$type" "$status" "$port" "$connections"
                done
            fi
            
            return $api_count
        else
            # 没有 jq，简单计算
            api_count=$(echo "$response" | grep -o '"id":' | wc -l)
            print_info "API 隧道总数: $api_count"
            return $api_count
        fi
    else
        print_error "获取 API 隧道数据失败"
        return -1
    fi
}

# 比较数据一致性
compare_data() {
    print_info "比较数据一致性..."
    
    db_count=$(get_db_tunnels)
    echo
    api_count=$(get_api_tunnels)
    echo
    
    if [ "$db_count" -eq "$api_count" ]; then
        print_success "隧道数量一致: $db_count"
    else
        print_warning "隧道数量不一致: 数据库=$db_count, API=$api_count"
    fi
    
    # 详细比较（需要 jq）
    if command -v jq &> /dev/null; then
        print_info "进行详细数据比较..."
        
        # 获取 API 数据
        api_response=$(curl -s "$API_BASE/tunnels")
        if echo "$api_response" | grep -q '"code":0'; then
            # 提取隧道 ID 列表
            api_ids=$(echo "$api_response" | jq -r '.data.tunnels[].id' | sort)
            db_ids=$(sqlite3 "$DB_FILE" "SELECT tunnel_id FROM tunnels ORDER BY tunnel_id;")
            
            # 比较 ID 列表
            if [ "$api_ids" = "$db_ids" ]; then
                print_success "隧道 ID 列表一致"
            else
                print_warning "隧道 ID 列表不一致"
                echo "API IDs:"
                echo "$api_ids"
                echo "DB IDs:"
                echo "$db_ids"
            fi
            
            # 比较状态
            print_info "检查状态一致性..."
            echo "$api_response" | jq -r '.data.tunnels[] | "\(.id) \(.status)"' | \
            while read id status; do
                db_status=$(sqlite3 "$DB_FILE" "SELECT status FROM tunnels WHERE tunnel_id='$id';")
                if [ "$status" != "$db_status" ]; then
                    print_warning "状态不一致 [$id]: API=$status, DB=$db_status"
                fi
            done
        fi
    fi
}

# 显示统计信息
show_statistics() {
    print_info "显示详细统计信息..."
    
    if [ -f "$DB_FILE" ]; then
        echo
        print_info "数据库统计:"
        sqlite3 "$DB_FILE" "
        SELECT 
            tunnel_type,
            status,
            COUNT(*) as count
        FROM tunnels 
        GROUP BY tunnel_type, status
        ORDER BY tunnel_type, status;" \
        -header -column
        
        echo
        print_info "端口分配统计:"
        sqlite3 "$DB_FILE" "
        SELECT 
            allocation_type,
            status,
            COUNT(*) as count
        FROM port_allocations 
        GROUP BY allocation_type, status
        ORDER BY allocation_type, status;" \
        -header -column 2>/dev/null || print_warning "端口分配表为空或不存在"
        
        echo
        print_info "会话统计:"
        sqlite3 "$DB_FILE" "
        SELECT 
            COUNT(*) as active_sessions,
            SUM(bytes_in) as total_bytes_in,
            SUM(bytes_out) as total_bytes_out
        FROM tunnel_sessions;" \
        -header -column 2>/dev/null || print_warning "会话表为空或不存在"
    fi
}

# 修复建议
suggest_fixes() {
    print_info "修复建议:"
    echo "1. 如果发现数据不一致，可以尝试重启 Spark 服务"
    echo "2. 服务重启时会自动从数据库恢复隧道配置"
    echo "3. 运行中的隧道会被标记为停止状态，需要手动重启"
    echo "4. 如果问题持续存在，可以检查日志文件: logs/spark.log"
    echo "5. 严重情况下可以备份数据库后重新初始化"
}

# 主函数
main() {
    echo
    print_info "开始隧道数据一致性检查..."
    echo
    
    # 检查依赖
    if ! check_dependencies; then
        exit 1
    fi
    echo
    
    # 检查数据库
    if ! check_database; then
        exit 1
    fi
    echo
    
    # 检查服务（可选）
    if check_service; then
        echo
        # 比较数据
        compare_data
    else
        print_warning "服务未运行，只能检查数据库数据"
        echo
        get_db_tunnels > /dev/null
    fi
    
    echo
    show_statistics
    
    echo
    suggest_fixes
    
    echo
    print_success "检查完成"
}

# 显示帮助
show_help() {
    echo "Spark 隧道数据一致性检查脚本"
    echo
    echo "用法: $0 [选项]"
    echo
    echo "选项:"
    echo "  check    执行完整检查 (默认)"
    echo "  db       仅检查数据库"
    echo "  api      仅检查 API"
    echo "  stats    显示统计信息"
    echo "  help     显示帮助"
    echo
    echo "依赖:"
    echo "  - sqlite3: 查询数据库"
    echo "  - curl: 调用 API"
    echo "  - jq: 格式化 JSON (可选)"
}

# 处理命令行参数
case "${1:-check}" in
    "check")
        main
        ;;
    "db")
        check_dependencies && check_database && get_db_tunnels > /dev/null && show_statistics
        ;;
    "api")
        check_dependencies && check_service && get_api_tunnels > /dev/null
        ;;
    "stats")
        check_dependencies && check_database && show_statistics
        ;;
    "help")
        show_help
        ;;
    *)
        print_error "未知选项: $1"
        show_help
        exit 1
        ;;
esac
