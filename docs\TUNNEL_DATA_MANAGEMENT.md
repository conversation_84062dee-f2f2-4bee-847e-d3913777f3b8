# Spark 隧道数据管理机制

## 概述

Spark 隧道功能采用**双层数据管理**机制，确保数据的持久化存储和实时状态的准确性。

## 🏗️ 数据架构

### 数据存储层次

```
┌─────────────────┐    ┌─────────────────┐
│   前端展示层     │    │   API 接口层     │
│  (React UI)     │◄──►│  (REST API)     │
└─────────────────┘    └─────────────────┘
           ▲                      ▲
           │                      │
           ▼                      ▼
┌─────────────────┐    ┌─────────────────┐
│   内存状态层     │◄──►│   数据库层       │
│ (TunnelManager) │    │  (SQLite DB)    │
└─────────────────┘    └─────────────────┘
```

### 1. **数据库层 (持久化存储)**
- **作用**: 持久化存储隧道配置和历史统计
- **表结构**:
  - `tunnels`: 隧道配置和基础统计
  - `tunnel_sessions`: 历史会话记录
  - `port_allocations`: 端口分配记录

### 2. **内存状态层 (实时状态)**
- **作用**: 管理运行时状态和实时统计
- **组件**:
  - `TunnelManager`: 隧道实例管理
  - `PortAllocator`: 端口分配管理
  - `TunnelSession`: 活跃连接管理

## 📊 数据流转机制

### 隧道创建流程
```
1. API 接收创建请求
2. 验证参数和设备状态
3. 分配端口资源
4. 保存配置到数据库 ✓
5. 创建内存实例 ✓
6. 返回创建结果
```

### 隧道状态更新流程
```
1. 状态变更 (启动/停止/错误)
2. 更新内存状态 ✓
3. 同步到数据库 ✓
4. 通知前端更新
```

### 统计信息更新流程
```
1. 连接建立/数据传输
2. 实时更新内存统计 ✓
3. 定期同步到数据库 ✓ (30秒间隔)
4. API 返回合并数据
```

## 🔄 数据同步策略

### 1. **实时同步**
- **触发时机**: 状态变更、连接建立/关闭
- **同步内容**: 隧道状态、连接数变化
- **实现方式**: 立即写入数据库

### 2. **定期同步**
- **触发时机**: 每30秒自动执行
- **同步内容**: 流量统计、连接统计
- **实现方式**: 批量更新数据库

### 3. **启动时恢复**
- **触发时机**: 服务启动时
- **恢复内容**: 从数据库加载所有隧道配置
- **状态处理**: 运行中的隧道标记为停止状态

### 4. **一致性检查**
- **触发时机**: 每5分钟自动执行
- **检查内容**: 内存与数据库的数据一致性
- **修复机制**: 自动同步不一致的数据

## 📈 前端数据展示

### 数据来源优先级
1. **实时状态**: 从内存获取 (状态、活跃连接数)
2. **历史统计**: 从数据库获取 (总连接数、总流量)
3. **配置信息**: 从数据库获取 (名称、端口、类型等)

### 刷新策略
- **自动刷新**: 每5秒自动刷新列表
- **手动刷新**: 用户点击刷新按钮
- **状态指示**: 显示最后更新时间

## 🛠️ 数据管理工具

### 1. **数据一致性检查脚本**
```bash
# 检查数据一致性
./scripts/check_tunnel_data.sh check

# 仅检查数据库
./scripts/check_tunnel_data.sh db

# 仅检查API
./scripts/check_tunnel_data.sh api

# 显示统计信息
./scripts/check_tunnel_data.sh stats
```

### 2. **数据同步管理器**
```go
// 手动同步内存到数据库
dataSyncManager.SyncMemoryToDB()

// 手动同步数据库到内存
dataSyncManager.SyncDBToMemory()

// 检查数据一致性
dataSyncManager.CheckDataConsistency()

// 修复数据不一致
dataSyncManager.RepairDataInconsistency()
```

## 🔧 故障处理

### 常见数据不一致问题

#### 1. **内存状态丢失**
- **现象**: 服务重启后隧道状态不正确
- **原因**: 内存状态未持久化
- **解决**: 自动从数据库恢复配置

#### 2. **统计信息滞后**
- **现象**: 前端显示的统计信息不是最新的
- **原因**: 定期同步延迟
- **解决**: 前端优先显示内存中的实时数据

#### 3. **端口分配冲突**
- **现象**: 端口被重复分配
- **原因**: 内存和数据库端口记录不一致
- **解决**: 启动时重建端口分配表

### 数据修复步骤

#### 自动修复
```bash
# 服务会自动执行以下修复:
1. 启动时从数据库恢复隧道配置
2. 定期检查数据一致性
3. 自动同步不一致的数据
4. 重建端口分配表
```

#### 手动修复
```bash
# 1. 检查数据一致性
./scripts/check_tunnel_data.sh check

# 2. 重启服务 (触发自动恢复)
./spark-server

# 3. 验证修复结果
./scripts/check_tunnel_data.sh check
```

## 📋 最佳实践

### 1. **数据备份**
```bash
# 定期备份数据库
cp spark.db spark.db.backup.$(date +%Y%m%d_%H%M%S)

# 导出隧道配置
sqlite3 spark.db ".dump tunnels" > tunnels_backup.sql
```

### 2. **监控建议**
- 监控数据库文件大小增长
- 定期检查数据一致性
- 监控内存使用情况
- 记录同步失败日志

### 3. **性能优化**
- 合理设置同步间隔
- 批量更新统计信息
- 定期清理历史会话记录
- 优化数据库索引

## 🔍 调试指南

### 查看同步日志
```bash
# 查看隧道管理日志
tail -f logs/spark.log | grep -i tunnel

# 查看数据同步日志
tail -f logs/spark.log | grep -i sync
```

### 数据库查询
```sql
-- 查看隧道状态
SELECT tunnel_id, name, status, total_connections 
FROM tunnels ORDER BY update_time DESC;

-- 查看活跃会话
SELECT tunnel_id, COUNT(*) as active_sessions 
FROM tunnel_sessions 
GROUP BY tunnel_id;

-- 查看端口分配
SELECT port, tunnel_id, allocation_type, status 
FROM port_allocations 
WHERE status = 'allocated';
```

### API 调试
```bash
# 获取隧道列表
curl -s http://localhost:8000/api/tunnels | jq

# 获取特定隧道
curl -s http://localhost:8000/api/tunnels/{tunnel-id} | jq

# 获取端口统计
curl -s http://localhost:8000/api/ports/stats | jq
```

## 📝 总结

Spark 隧道数据管理机制通过双层架构确保了：

1. **数据持久性**: 所有配置和统计信息都持久化到数据库
2. **状态实时性**: 运行时状态在内存中实时更新
3. **数据一致性**: 自动同步机制保证内存和数据库一致
4. **故障恢复**: 服务重启时自动恢复隧道配置
5. **性能优化**: 合理的同步策略平衡性能和一致性

这种设计确保了隧道管理功能的可靠性和高性能，为用户提供准确的实时信息。
